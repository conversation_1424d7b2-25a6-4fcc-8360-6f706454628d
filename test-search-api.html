<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Custom Search API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .search-type {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .search-type:hover {
            background: #3367d6;
        }
        .search-type.active {
            background: #34a853;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f1f3f4;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #4285f4;
        }
        .result-title {
            font-weight: bold;
            color: #1a0dab;
            margin-bottom: 5px;
        }
        .result-url {
            color: #006621;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .result-snippet {
            color: #545454;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input[type="text"] {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Google Custom Search API Test</h1>
        <p>Test the enhanced Google Custom Search API implementation with advanced parameters for Images, Videos, Shopping, and Books.</p>
        
        <div>
            <input type="text" id="searchQuery" placeholder="Enter search query..." value="artificial intelligence">
            <button onclick="testSearch()">Test Search</button>
        </div>
        
        <div style="margin: 20px 0;">
            <strong>Search Types:</strong><br>
            <button class="search-type active" data-type="web" onclick="selectSearchType('web')">Web</button>
            <button class="search-type" data-type="images" onclick="selectSearchType('images')">Images</button>
            <button class="search-type" data-type="videos" onclick="selectSearchType('videos')">Videos</button>
            <button class="search-type" data-type="news" onclick="selectSearchType('news')">News</button>
            <button class="search-type" data-type="shopping" onclick="selectSearchType('shopping')">Shopping</button>
            <button class="search-type" data-type="books" onclick="selectSearchType('books')">Books</button>
        </div>
        
        <div id="status"></div>
        <div id="results" class="results" style="display: none;"></div>
    </div>

    <script>
        let currentSearchType = 'web';
        
        function selectSearchType(type) {
            currentSearchType = type;
            document.querySelectorAll('.search-type').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');
        }
        
        async function testSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                showStatus('Please enter a search query', 'error');
                return;
            }
            
            showStatus(`Testing ${currentSearchType} search for: "${query}"...`, 'info');
            
            try {
                const params = buildSearchParams(query, currentSearchType);
                const url = `https://www.googleapis.com/customsearch/v1?${params.toString()}`;
                
                showStatus(`Making API request to Google Custom Search...`, 'info');
                console.log('API URL:', url);
                
                const response = await fetch(url);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
                }
                
                const data = await response.json();
                displayResults(data, query, currentSearchType);
                
            } catch (error) {
                console.error('Search test error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }
        
        function buildSearchParams(query, searchType) {
            const params = new URLSearchParams({
                key: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
                cx: '61201925358ea4e83',
                q: enhanceQuery(query, searchType),
                num: 10,
                start: 1
            });
            
            // Add search type specific parameters
            if (searchType === 'images') {
                params.append('searchType', 'image');
                params.append('imgSize', 'xlarge');
                params.append('imgType', 'photo');
                params.append('safe', 'active');
                params.append('fileType', 'jpg,png,gif,webp');
            } else if (searchType === 'videos') {
                params.append('siteSearch', 'youtube.com OR vimeo.com OR ted.com');
                params.append('orTerms', 'video tutorial watch lesson');
                params.append('dateRestrict', 'y2');
            } else if (searchType === 'news') {
                params.append('siteSearch', 'reuters.com OR bbc.com OR cnn.com OR apnews.com');
                params.append('sort', 'date');
                params.append('dateRestrict', 'd3');
                params.append('orTerms', 'news breaking latest');
            } else if (searchType === 'shopping') {
                params.append('siteSearch', 'amazon.com OR ebay.com OR walmart.com');
                params.append('orTerms', 'buy price product review');
                params.append('cr', 'countryUS');
            } else if (searchType === 'books') {
                params.append('siteSearch', 'amazon.com OR goodreads.com OR books.google.com');
                params.append('orTerms', 'book author review read');
                params.append('fileType', 'pdf,epub');
            }
            
            return params;
        }
        
        function enhanceQuery(query, searchType) {
            switch (searchType) {
                case 'images':
                    return `${query} high resolution photo image`;
                case 'videos':
                    return `${query} video tutorial educational content`;
                case 'news':
                    return `${query} news latest breaking update`;
                case 'shopping':
                    return `${query} buy price product review`;
                case 'books':
                    return `${query} book author review summary`;
                default:
                    return query;
            }
        }
        
        function displayResults(data, query, searchType) {
            const resultsDiv = document.getElementById('results');
            
            if (!data.items || data.items.length === 0) {
                showStatus('No results found', 'error');
                resultsDiv.style.display = 'none';
                return;
            }
            
            const totalResults = data.searchInformation?.totalResults || '0';
            const searchTime = data.searchInformation?.searchTime || '0';
            
            showStatus(`Found ${parseInt(totalResults).toLocaleString()} results in ${searchTime} seconds`, 'success');
            
            let html = `<h3>${searchType.toUpperCase()} Results for "${query}"</h3>`;
            
            data.items.forEach((item, index) => {
                html += `
                    <div class="result-item">
                        <div class="result-title">${escapeHtml(item.title)}</div>
                        <div class="result-url">${escapeHtml(item.displayLink || item.link)}</div>
                        <div class="result-snippet">${escapeHtml(item.snippet || 'No description available')}</div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Allow Enter key to trigger search
        document.getElementById('searchQuery').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testSearch();
            }
        });
    </script>
</body>
</html>
