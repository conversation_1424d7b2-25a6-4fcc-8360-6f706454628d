<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Enhanced Google Clone - Complete Demo</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/google-enhancements.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            font-family: arial, sans-serif;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .demo-title {
            font-size: 48px;
            font-weight: 300;
            color: #202124;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .demo-subtitle {
            font-size: 18px;
            color: #5f6368;
            margin-bottom: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 500;
            color: #202124;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-icon {
            font-size: 24px;
        }
        
        .feature-description {
            color: #5f6368;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .demo-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .enhancement-list {
            list-style: none;
            padding: 0;
        }
        
        .enhancement-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #202124;
        }
        
        .enhancement-list .check {
            color: #34a853;
            font-weight: bold;
        }
        
        .cta-section {
            text-align: center;
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 60px 40px;
            border-radius: 12px;
            margin: 60px 0;
        }
        
        .cta-title {
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 20px;
        }
        
        .cta-description {
            font-size: 16px;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .cta-button {
            background: white;
            color: #1a73e8;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .cta-button:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .cta-button.secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .cta-button.secondary:hover {
            background: white;
            color: #1a73e8;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #202124;
        }
        
        .comparison-table .before {
            color: #ea4335;
        }
        
        .comparison-table .after {
            color: #34a853;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .demo-title {
                font-size: 36px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .cta-section {
                padding: 40px 20px;
            }
            
            .cta-title {
                font-size: 24px;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">🎨 Enhanced Google Clone</h1>
            <p class="demo-subtitle">Pixel-perfect Google experience with advanced features and authentic styling</p>
            
            <!-- Live Google Logo -->
            <div class="google-logo" style="margin: 30px 0;">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 200px; height: 68px;">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </div>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h3 class="feature-title">
                    <span class="feature-icon">🎨</span>
                    Pixel-Perfect Design
                </h3>
                <p class="feature-description">
                    Authentic Google interface with exact colors, typography, spacing, and visual elements.
                </p>
                <div class="demo-section">
                    <div class="search-container" style="max-width: 300px;">
                        <input type="text" class="search-box" placeholder="Try the enhanced search" style="font-size: 14px; height: 36px;">
                        <div class="search-icons">
                            <div class="voice-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                                </svg>
                            </div>
                            <div class="search-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <ul class="enhancement-list">
                    <li><span class="check">✓</span> Official Google logo & colors</li>
                    <li><span class="check">✓</span> Arial font family</li>
                    <li><span class="check">✓</span> Material Design shadows</li>
                    <li><span class="check">✓</span> Authentic spacing</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">
                    <span class="feature-icon">⚡</span>
                    Enhanced Interactions
                </h3>
                <p class="feature-description">
                    Google-style animations, hover effects, and smooth transitions for professional UX.
                </p>
                <div class="demo-section">
                    <button class="btn" onclick="this.style.transform='scale(0.95)'; setTimeout(() => this.style.transform='scale(1)', 100)">
                        Google Search
                    </button>
                    <button class="btn" style="margin-left: 10px;">I'm Feeling Lucky</button>
                </div>
                <ul class="enhancement-list">
                    <li><span class="check">✓</span> Smooth hover effects</li>
                    <li><span class="check">✓</span> Button ripple animations</li>
                    <li><span class="check">✓</span> Focus indicators</li>
                    <li><span class="check">✓</span> Loading animations</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">
                    <span class="feature-icon">🔍</span>
                    Advanced Search Features
                </h3>
                <p class="feature-description">
                    Enhanced autocomplete, voice search, image search, and comprehensive navigation tabs.
                </p>
                <div class="demo-section">
                    <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                        <span style="background: #e8f5e8; padding: 4px 8px; border-radius: 4px; color: #2e7d32; font-size: 12px;">Voice Search</span>
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 4px; color: #1565c0; font-size: 12px;">Image Search</span>
                        <span style="background: #fce4ec; padding: 4px 8px; border-radius: 4px; color: #c2185b; font-size: 12px;">Autocomplete</span>
                    </div>
                </div>
                <ul class="enhancement-list">
                    <li><span class="check">✓</span> Smart autocomplete with icons</li>
                    <li><span class="check">✓</span> Voice search integration</li>
                    <li><span class="check">✓</span> Image search capability</li>
                    <li><span class="check">✓</span> Multiple search types</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">
                    <span class="feature-icon">📱</span>
                    Responsive Excellence
                </h3>
                <p class="feature-description">
                    Perfect adaptation to all devices with Google's mobile-first approach and touch optimization.
                </p>
                <div class="demo-section">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <div style="width: 60px; height: 40px; background: #4285f4; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">Desktop</div>
                        <div style="width: 40px; height: 40px; background: #34a853; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">Tablet</div>
                        <div style="width: 30px; height: 40px; background: #fbbc05; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">Mobile</div>
                    </div>
                </div>
                <ul class="enhancement-list">
                    <li><span class="check">✓</span> Mobile-first design</li>
                    <li><span class="check">✓</span> Touch-friendly interface</li>
                    <li><span class="check">✓</span> Flexible layouts</li>
                    <li><span class="check">✓</span> Optimized performance</li>
                </ul>
            </div>
        </div>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Feature</th>
                    <th>Before Enhancement</th>
                    <th>After Enhancement</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Typography</strong></td>
                    <td class="before">Generic web fonts</td>
                    <td class="after">Google's Arial font family</td>
                </tr>
                <tr>
                    <td><strong>Logo</strong></td>
                    <td class="before">Text-based "Search"</td>
                    <td class="after">Official Google SVG logo</td>
                </tr>
                <tr>
                    <td><strong>Colors</strong></td>
                    <td class="before">Basic blue theme</td>
                    <td class="after">Official Google brand colors</td>
                </tr>
                <tr>
                    <td><strong>Animations</strong></td>
                    <td class="before">Basic CSS transitions</td>
                    <td class="after">Material Design animations</td>
                </tr>
                <tr>
                    <td><strong>Search Box</strong></td>
                    <td class="before">Simple input field</td>
                    <td class="after">Google-style with voice & camera</td>
                </tr>
                <tr>
                    <td><strong>Buttons</strong></td>
                    <td class="before">Standard HTML buttons</td>
                    <td class="after">Google Material Design buttons</td>
                </tr>
                <tr>
                    <td><strong>Navigation</strong></td>
                    <td class="before">Basic tabs</td>
                    <td class="after">Google-style tabs with icons</td>
                </tr>
                <tr>
                    <td><strong>Results Page</strong></td>
                    <td class="before">Simple layout</td>
                    <td class="after">Authentic Google results layout</td>
                </tr>
            </tbody>
        </table>
        
        <div class="cta-section">
            <h2 class="cta-title">Experience the Enhanced Google Clone</h2>
            <p class="cta-description">
                Try the pixel-perfect Google experience with all the visual enhancements, smooth interactions, and professional features.
            </p>
            <div class="cta-buttons">
                <a href="/" class="cta-button">🏠 Main Page</a>
                <a href="/results-enhanced.html?q=test&start=1&type=web" class="cta-button">🔍 Results Page</a>
                <a href="/design-showcase.html" class="cta-button secondary">🎨 Design Showcase</a>
            </div>
        </div>
    </div>
    
    <script>
        // Add interactive demos
        document.addEventListener('DOMContentLoaded', () => {
            // Animate feature cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            // Initially hide cards
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease-out';
                observer.observe(card);
            });
            
            // Add hover effects to buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', () => {
                    btn.style.transform = 'translateY(-1px)';
                });
                
                btn.addEventListener('mouseleave', () => {
                    btn.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
