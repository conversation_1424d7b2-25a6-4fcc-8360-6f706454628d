<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Search - Working Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fff;
            color: #202124;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .search-header {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dadce0;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 400;
            color: #4285f4;
            text-decoration: none;
        }
        
        .search-container {
            flex: 1;
            max-width: 500px;
            position: relative;
        }
        
        .search-box {
            width: 100%;
            height: 40px;
            border: 1px solid #dfe1e5;
            border-radius: 20px;
            padding: 0 40px 0 16px;
            font-size: 16px;
            outline: none;
        }
        
        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
        }
        
        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .results-info {
            color: #70757a;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .result-item {
            margin-bottom: 30px;
            max-width: 600px;
        }
        
        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .demo-notice {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #2e7d32;
        }
        
        .search-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .search-btn:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-header">
            <a href="/" class="logo">Search</a>
            <div class="search-container">
                <input 
                    type="text" 
                    class="search-box" 
                    id="search-input"
                    placeholder="Search..."
                    value="javascript programming"
                >
                <svg class="search-icon" id="search-btn" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
                <button class="search-btn" onclick="performSearch()">Search</button>
            </div>
        </div>
        
        <div class="demo-notice">
            <strong>✅ Demo Mode Active:</strong> This shows how search results will look. Configure Google Custom Search API for real results.
        </div>
        
        <div class="results-info" id="results-info">
            About 1,234,567 results (0.42 seconds)
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        
        <div id="results-container">
            <!-- Results will be inserted here -->
        </div>
    </div>
    
    <script>
        // Simple search functionality that definitely works
        function performSearch() {
            const query = document.getElementById('search-input').value.trim();
            if (!query) return;
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results-container').innerHTML = '';
            
            // Simulate search delay
            setTimeout(() => {
                showResults(query);
                document.getElementById('loading').style.display = 'none';
            }, 500);
        }
        
        function showResults(query) {
            const container = document.getElementById('results-container');
            
            // Demo results data
            const results = [
                {
                    title: `${query} - Complete Guide and Tutorial`,
                    url: 'https://example.com/guide',
                    domain: 'example.com',
                    snippet: `Comprehensive guide to ${query}. Learn everything you need to know with practical examples, best practices, and step-by-step tutorials.`
                },
                {
                    title: `Best ${query} Resources and Tools 2024`,
                    url: 'https://developer.example.com/resources',
                    domain: 'developer.example.com',
                    snippet: `Top resources, tools, and libraries for ${query}. Updated regularly with the latest developments and community recommendations.`
                },
                {
                    title: `${query} Documentation and API Reference`,
                    url: 'https://docs.example.com/api',
                    domain: 'docs.example.com',
                    snippet: `Official documentation and API reference for ${query}. Complete with examples, code samples, and detailed explanations.`
                },
                {
                    title: `${query} Community Forum and Discussions`,
                    url: 'https://forum.example.com/topics',
                    domain: 'forum.example.com',
                    snippet: `Join the ${query} community. Ask questions, share knowledge, and connect with other developers and enthusiasts.`
                },
                {
                    title: `${query} News and Latest Updates`,
                    url: 'https://news.example.com/latest',
                    domain: 'news.example.com',
                    snippet: `Latest news and updates about ${query}. Stay informed about new features, releases, and industry developments.`
                },
                {
                    title: `Learn ${query} - Free Online Course`,
                    url: 'https://learn.example.com/course',
                    domain: 'learn.example.com',
                    snippet: `Free comprehensive online course for ${query}. From beginner to advanced level with hands-on projects and exercises.`
                },
                {
                    title: `${query} Examples and Code Snippets`,
                    url: 'https://code.example.com/snippets',
                    domain: 'code.example.com',
                    snippet: `Collection of ${query} examples and code snippets. Copy-paste ready code for common use cases and patterns.`
                },
                {
                    title: `${query} vs Alternatives - Comparison Guide`,
                    url: 'https://compare.example.com/guide',
                    domain: 'compare.example.com',
                    snippet: `Detailed comparison of ${query} with alternatives. Pros, cons, performance benchmarks, and use case recommendations.`
                }
            ];
            
            // Update results info
            document.getElementById('results-info').textContent = 
                `About ${(Math.random() * 1000000 + 100000).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',')} results (${(Math.random() * 0.5 + 0.1).toFixed(2)} seconds) for "${query}"`;
            
            // Render results
            let html = '';
            results.forEach(result => {
                html += `
                    <div class="result-item">
                        <div class="result-url">${result.domain}</div>
                        <a href="${result.url}" class="result-title" target="_blank">${result.title}</a>
                        <div class="result-snippet">${result.snippet}</div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Event listeners
        document.getElementById('search-input').addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }
        });
        
        document.getElementById('search-btn').addEventListener('click', performSearch);
        
        // Auto-search on page load
        window.addEventListener('load', () => {
            performSearch();
        });
    </script>
</body>
</html>
