<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Grid Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        /* Image Results Styles */
        .image-results-container {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
            gap: 16px !important;
            padding: 20px 0 !important;
            width: 100% !important;
        }

        .image-result-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            animation: fadeInUp 0.3s ease-out;
        }

        .image-result-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .image-wrapper {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .image-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-wrapper:hover img {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 12px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .image-result-item:hover .image-overlay {
            transform: translateY(0);
        }

        .image-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .image-domain {
            font-size: 12px;
            opacity: 0.8;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .image-results-container {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
                gap: 12px !important;
            }

            .image-wrapper {
                height: 150px;
            }
        }

        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }

        .test-button:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image Grid Layout Test</h1>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button class="test-button" onclick="loadTestImages()">Load Test Images</button>
            <button class="test-button" onclick="clearImages()">Clear Images</button>
        </div>

        <div id="image-container" class="image-results-container">
            <!-- Images will be loaded here -->
        </div>
    </div>

    <script>
        function loadTestImages() {
            const container = document.getElementById('image-container');
            container.innerHTML = '';
            
            // Test image data
            const testImages = [
                {
                    title: 'Beautiful Landscape',
                    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1506905925346-21bda4d32df4'
                },
                {
                    title: 'City Architecture',
                    imageUrl: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1441974231531-c6227db76b6e'
                },
                {
                    title: 'Nature Photography',
                    imageUrl: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1470071459604-3b5ec3a7fe05'
                },
                {
                    title: 'Abstract Art',
                    imageUrl: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1518837695005-2083093ee35b'
                },
                {
                    title: 'Ocean View',
                    imageUrl: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1439066615861-d1af74d74000'
                },
                {
                    title: 'Mountain Peak',
                    imageUrl: 'https://images.unsplash.com/photo-1464822759844-d150baec3e5e?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1464822759844-d150baec3e5e'
                },
                {
                    title: 'Forest Path',
                    imageUrl: 'https://images.unsplash.com/photo-1441260038675-7329ab4cc264?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1441260038675-7329ab4cc264'
                },
                {
                    title: 'Desert Sunset',
                    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
                    domain: 'unsplash.com',
                    link: 'https://unsplash.com/photo-1506905925346-21bda4d32df4'
                }
            ];

            testImages.forEach((item, index) => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'image-result-item';
                imageDiv.style.animationDelay = `${index * 0.05}s`;

                imageDiv.innerHTML = `
                    <div class="image-wrapper">
                        <a href="${item.link}" target="_blank" rel="noopener">
                            <img src="${item.imageUrl}"
                                 alt="${item.title}"
                                 loading="lazy">
                        </a>
                        <div class="image-overlay">
                            <div class="image-title">${item.title}</div>
                            <div class="image-domain">${item.domain}</div>
                        </div>
                    </div>
                `;

                container.appendChild(imageDiv);
            });

            console.log('✅ Test images loaded successfully');
        }

        function clearImages() {
            const container = document.getElementById('image-container');
            container.innerHTML = '';
            console.log('🗑️ Images cleared');
        }

        // Auto-load test images on page load
        window.addEventListener('load', () => {
            setTimeout(loadTestImages, 500);
        });
    </script>
</body>
</html>
