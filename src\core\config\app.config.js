/**
 * Application Configuration
 * Centralized configuration for the Google Clone application
 */

export const APP_CONFIG = {
  // Application metadata
  APP_NAME: 'Google Clone',
  APP_VERSION: '2.0.0',
  APP_DESCRIPTION: 'High-performance Google search clone',
  
  // API Configuration - Updated to match utils.js configuration
  API: {
    GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ', // Updated to match utils.js
    SEARCH_ENGINE_ID: '61201925358ea4e83', // Updated to match utils.js
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3
  },
  
  // Search Configuration
  SEARCH: {
    RESULTS_PER_PAGE: 10,
    MAX_SUGGESTIONS: 8,
    DEBOUNCE_DELAY: 300,
    CACHE_DURATION: 300000, // 5 minutes
    MAX_QUERY_LENGTH: 2048,
    MIN_QUERY_LENGTH: 1
  },
  
  // UI Configuration
  UI: {
    ANIMATION_DURATION: 300,
    SCROLL_THRESHOLD: 100,
    MOBILE_BREAKPOINT: 768,
    TABLET_BREAKPOINT: 1024
  },
  
  // Performance Configuration
  PERFORMANCE: {
    LAZY_LOAD_THRESHOLD: 200,
    IMAGE_QUALITY: 85,
    CACHE_SIZE_LIMIT: 50,
    PRELOAD_NEXT_PAGE: true
  },
  
  // Feature Flags
  FEATURES: {
    VOICE_SEARCH: true,
    IMAGE_SEARCH: true,
    INSTANT_ANSWERS: false, // Disabled for now
    DARK_MODE: true,
    OFFLINE_MODE: true,
    ANALYTICS: false
  },
  
  // Storage Keys
  STORAGE_KEYS: {
    SEARCH_HISTORY: 'google_clone_search_history',
    USER_PREFERENCES: 'google_clone_preferences',
    CACHE_PREFIX: 'google_clone_cache_',
    THEME: 'google_clone_theme'
  },
  
  // Error Messages
  ERRORS: {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    API_ERROR: 'Search service temporarily unavailable.',
    INVALID_QUERY: 'Please enter a valid search query.',
    NO_RESULTS: 'No results found for your search.',
    RATE_LIMIT: 'Too many requests. Please try again later.'
  },
  
  // Google Brand Colors
  COLORS: {
    GOOGLE_BLUE: '#4285f4',
    GOOGLE_RED: '#ea4335',
    GOOGLE_YELLOW: '#fbbc05',
    GOOGLE_GREEN: '#34a853',
    TEXT_PRIMARY: '#202124',
    TEXT_SECONDARY: '#5f6368',
    TEXT_DISABLED: '#9aa0a6',
    BACKGROUND: '#ffffff',
    SURFACE: '#f8f9fa',
    BORDER: '#dadce0'
  },
  
  // Typography
  TYPOGRAPHY: {
    FONT_FAMILY: 'arial, sans-serif',
    FONT_SIZE_BASE: '14px',
    FONT_SIZE_LARGE: '20px',
    FONT_SIZE_SMALL: '13px',
    LINE_HEIGHT: 1.4,
    FONT_WEIGHT_NORMAL: 400,
    FONT_WEIGHT_MEDIUM: 500
  },
  
  // Spacing
  SPACING: {
    XS: '4px',
    SM: '8px',
    MD: '16px',
    LG: '24px',
    XL: '32px',
    XXL: '48px'
  },
  
  // Z-Index Scale
  Z_INDEX: {
    DROPDOWN: 1000,
    MODAL: 2000,
    TOOLTIP: 3000,
    NOTIFICATION: 4000
  },
  
  // Development Configuration
  DEV: {
    DEBUG: false,
    MOCK_API: false,
    PERFORMANCE_MONITORING: true,
    ERROR_REPORTING: true
  }
};

// Environment-specific overrides
if (typeof window !== 'undefined') {
  // Browser environment
  if (window.location.hostname === 'localhost') {
    APP_CONFIG.DEV.DEBUG = true;
    APP_CONFIG.DEV.MOCK_API = true;
  }
}

// Freeze configuration to prevent accidental modifications
Object.freeze(APP_CONFIG);

export default APP_CONFIG;
