/* Google-specific enhancements for pixel-perfect styling */

/* Google Material Design Shadows */
.elevation-1 {
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.elevation-2 {
    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.elevation-3 {
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

/* Google Header Enhancements */
.header-links {
    display: flex;
    gap: 15px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-link {
    color: rgba(0,0,0,.87);
    text-decoration: none;
    font-size: 13px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.1s ease;
}

.header-link:hover {
    background-color: rgba(60,64,67,.08);
}

.sign-in-btn {
    background: #1a73e8;
    color: white;
    border: 1px solid #1a73e8;
    border-radius: 4px;
    padding: 9px 23px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.1s ease;
    font-family: arial, sans-serif;
}

.sign-in-btn:hover {
    background: #1557b0;
    border-color: #1557b0;
    box-shadow: 0 1px 2px rgba(0,0,0,.1);
}

.sign-in-btn:active {
    background: #1557b0;
    box-shadow: 0 2px 4px rgba(0,0,0,.2);
}

/* Google Search Box Enhancements */
.search-container {
    position: relative;
}

.search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 24px;
    background: transparent;
    transition: box-shadow 0.2s ease;
    pointer-events: none;
    z-index: -1;
}

.search-container:hover::before {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
}

.search-box:focus + .search-container::before {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
}

/* Google Button Enhancements */
.btn {
    position: relative;
    overflow: hidden;
    font-family: arial, sans-serif;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.25px;
    text-transform: none;
    border-radius: 4px;
    padding: 10px 20px;
    min-width: 54px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.1s ease;
    background: #f8f9fa;
    border: 1px solid #f8f9fa;
    color: #3c4043;
}

.btn:hover {
    box-shadow: 0 1px 1px rgba(0,0,0,.1);
    background: #f1f3f4;
    border: 1px solid #dadce0;
    color: #202124;
}

.btn:active {
    background: #e8eaed;
    box-shadow: 0 1px 2px rgba(0,0,0,.1);
}

.btn:focus {
    border: 1px solid #4285f4;
    outline: none;
}

/* Google Logo Animation */
.google-logo {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search Suggestions Enhancement */
.suggestions {
    background: white;
    border: 1px solid #dfe1e5;
    border-top: none;
    border-radius: 0 0 24px 24px;
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestion-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 16px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    gap: 12px;
    color: #202124;
    font-family: arial, sans-serif;
    transition: background-color 0.1s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background: #f1f3f4;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    width: 16px;
    height: 16px;
    fill: #9aa0a6;
    flex-shrink: 0;
}

/* Voice Search Animation */
.voice-icon.listening {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Loading Spinner - Google Style */
.google-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Result Item Enhancements */
.result-item {
    padding: 0 0 28px 12px;
    max-width: 600px;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.result-title {
    color: #1a0dab;
    font-size: 20px;
    line-height: 1.3;
    font-weight: 400;
    text-decoration: none;
    display: block;
    margin-bottom: 3px;
    transition: text-decoration 0.1s ease;
}

.result-title:hover {
    text-decoration: underline;
}

.result-title:visited {
    color: #609;
}

.result-url {
    color: #202124;
    font-size: 14px;
    line-height: 1.3;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-snippet {
    color: #4d5156;
    font-size: 14px;
    line-height: 1.58;
}

.result-snippet em {
    font-style: normal;
    font-weight: bold;
}

/* Pagination - Google Style */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    padding-left: 12px;
}

.pagination-nav {
    display: flex;
    align-items: center;
    gap: 0;
}

.page-btn {
    padding: 10px 16px;
    border: none;
    background: none;
    color: #1a73e8;
    cursor: pointer;
    font-size: 14px;
    font-family: arial, sans-serif;
    border-radius: 4px;
    transition: background-color 0.1s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-btn:hover {
    background: #f1f3f4;
}

.page-btn:disabled {
    color: #dadce0;
    cursor: not-allowed;
}

.page-number {
    padding: 10px 16px;
    color: #202124;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.1s ease;
    text-decoration: none;
    min-width: 44px;
    text-align: center;
}

.page-number:hover {
    background: #f1f3f4;
}

.page-number.current {
    background: #1a73e8;
    color: white;
}

/* Header Enhancements */
.header {
    background: white;
    border-bottom: 1px solid #dadce0;
    position: sticky;
    top: 0;
    z-index: 100;
    padding: 6px 20px 0 20px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 30px;
    min-height: 58px;
}

/* Navigation Tabs */
.nav-tabs {
    background: white;
    padding: 0 20px;
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    gap: 0;
    align-items: center;
}

.nav-tab {
    padding: 12px 16px;
    color: #5f6368;
    text-decoration: none;
    font-size: 13px;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.nav-tab.active {
    color: #1a73e8;
    border-bottom-color: #1a73e8;
}

.nav-tab:hover {
    color: #1a73e8;
}

.nav-tab svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .header-content {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .search-container {
        order: 3;
        width: 100%;
        max-width: none;
    }
    
    .nav-content {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 0;
    }
    
    .result-item {
        padding-left: 0;
    }
    
    .pagination {
        padding-left: 0;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 6px 15px 0 15px;
    }
    
    .nav-tab {
        padding: 12px 12px;
        font-size: 12px;
    }
    
    .btn {
        width: 180px;
        justify-content: center;
    }
}
