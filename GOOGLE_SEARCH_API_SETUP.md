# 🔧 Google Custom Search API Setup Guide

## Overview
This guide explains how to configure Google Custom Search API to get relevant results for different content types (Images, Videos, News, Shopping, Books).

## 📋 Prerequisites

### 1. Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the **Custom Search API**
4. Create API credentials (API Key)

### 2. Custom Search Engine Setup
1. Go to [Google Custom Search](https://cse.google.com/cse/)
2. Click "Add" to create a new search engine
3. Configure search engine settings

## 🎯 Search Engine Configuration

### Option 1: Single Search Engine (Current Setup)
**Pros**: Simple setup, one API key
**Cons**: Limited content type specificity

```javascript
const API_CONFIG = {
    GOOGLE_API_KEY: 'YOUR_API_KEY',
    SEARCH_ENGINES: {
        web: 'YOUR_SEARCH_ENGINE_ID',
        images: 'YOUR_SEARCH_ENGINE_ID',    // Same ID with different parameters
        videos: 'YOUR_SEARCH_ENGINE_ID',    // Same ID with site restrictions
        news: 'YOUR_SEARCH_ENGINE_ID',      // Same ID with news sites
        shopping: 'YOUR_SEARCH_ENGINE_ID',  // Same ID with shopping sites
        books: 'YOUR_SEARCH_ENGINE_ID'      // Same ID with book sites
    }
};
```

### Option 2: Multiple Search Engines (Recommended)
**Pros**: Better content specificity, optimized results
**Cons**: More complex setup

```javascript
const API_CONFIG = {
    GOOGLE_API_KEY: 'YOUR_API_KEY',
    SEARCH_ENGINES: {
        web: 'WEB_SEARCH_ENGINE_ID',
        images: 'IMAGE_SEARCH_ENGINE_ID',
        videos: 'VIDEO_SEARCH_ENGINE_ID',
        news: 'NEWS_SEARCH_ENGINE_ID',
        shopping: 'SHOPPING_SEARCH_ENGINE_ID',
        books: 'BOOKS_SEARCH_ENGINE_ID'
    }
};
```

## 🔧 Search Engine Configurations

### 1. Web Search Engine
- **Sites to search**: Entire web
- **SafeSearch**: Moderate
- **Language**: Your preferred language
- **Country**: Your target country

### 2. Images Search Engine
- **Sites to search**: 
  - unsplash.com
  - pixabay.com
  - pexels.com
  - flickr.com
  - wikimedia.org
- **Image search**: Enabled
- **SafeSearch**: Strict

### 3. Videos Search Engine
- **Sites to search**:
  - youtube.com
  - vimeo.com
  - dailymotion.com
  - ted.com
  - coursera.org
- **Keywords**: video, watch, tutorial, course

### 4. News Search Engine
- **Sites to search**:
  - reuters.com
  - bbc.com
  - cnn.com
  - apnews.com
  - npr.org
  - theguardian.com
  - nytimes.com
- **Freshness**: Recent content preferred

### 5. Shopping Search Engine
- **Sites to search**:
  - amazon.com
  - ebay.com
  - walmart.com
  - target.com
  - bestbuy.com
  - etsy.com
- **Keywords**: buy, price, product, shop, store

### 6. Books Search Engine
- **Sites to search**:
  - amazon.com (books section)
  - goodreads.com
  - books.google.com
  - worldcat.org
  - barnesandnoble.com
- **Keywords**: book, author, read, library, isbn

## 📝 Implementation Steps

### Step 1: Update API Configuration
Replace the API configuration in `results.html`:

```javascript
const API_CONFIG = {
    GOOGLE_API_KEY: 'YOUR_ACTUAL_API_KEY',
    SEARCH_ENGINES: {
        web: 'YOUR_WEB_SEARCH_ENGINE_ID',
        images: 'YOUR_IMAGE_SEARCH_ENGINE_ID',
        videos: 'YOUR_VIDEO_SEARCH_ENGINE_ID',
        news: 'YOUR_NEWS_SEARCH_ENGINE_ID',
        shopping: 'YOUR_SHOPPING_SEARCH_ENGINE_ID',
        books: 'YOUR_BOOKS_SEARCH_ENGINE_ID'
    },
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    RESULTS_PER_PAGE: 10
};
```

### Step 2: Test Each Search Type
1. Test Images tab with query like "nature photography"
2. Test Videos tab with query like "programming tutorial"
3. Test News tab with query like "technology news"
4. Test Shopping tab with query like "laptop computer"
5. Test Books tab with query like "machine learning"

### Step 3: Monitor API Usage
- Check [Google Cloud Console](https://console.cloud.google.com/) for API usage
- Monitor quota limits (100 searches/day for free tier)
- Set up billing alerts if needed

## 🚨 Important Notes

### API Limitations
- **Free Tier**: 100 searches per day
- **Paid Tier**: $5 per 1000 queries (after free tier)
- **Rate Limits**: 10 queries per second

### Content Type Limitations
- Google Custom Search API doesn't have native support for all content types
- We use site restrictions and enhanced queries to simulate content-specific search
- Results quality depends on the sites included in each search engine

### Alternative Approaches
If Google Custom Search API limitations are too restrictive:

1. **YouTube Data API** for videos
2. **News API** for news content
3. **Amazon Product API** for shopping
4. **Google Books API** for books
5. **Unsplash API** for images

## 🔄 Fallback Strategy

The current implementation includes intelligent fallbacks:
1. **Real API Results**: When API is configured and working
2. **Enhanced Demo Results**: Realistic content when API fails
3. **Error Handling**: Graceful degradation with user feedback

## 📊 Testing Results

After proper configuration, you should see:
- **Images Tab**: Actual images from photo platforms
- **Videos Tab**: Video content from YouTube/Vimeo
- **News Tab**: Recent news articles
- **Shopping Tab**: Product listings
- **Books Tab**: Book information and reviews

## 🛠️ Troubleshooting

### Common Issues
1. **403 Forbidden**: Check API key and enable Custom Search API
2. **No Results**: Verify search engine configuration
3. **Wrong Content**: Adjust site restrictions and keywords
4. **Quota Exceeded**: Monitor usage in Google Cloud Console

### Debug Mode
Enable console logging to see API requests:
```javascript
console.log('🌐 Making API request to:', url);
```

## 📈 Optimization Tips

1. **Cache Results**: Implement intelligent caching to reduce API calls
2. **Batch Requests**: Combine multiple searches when possible
3. **Smart Fallbacks**: Use demo data when API is unavailable
4. **User Feedback**: Show API status to users
5. **Performance Monitoring**: Track search response times
