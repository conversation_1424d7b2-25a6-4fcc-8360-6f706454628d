<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Custom Search API Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .setup-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1a73e8;
            margin-bottom: 10px;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .step h3 {
            color: #1a73e8;
            margin-top: 0;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #1a73e8;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
            font-weight: bold;
        }
        .config-form {
            background: #e8f0fe;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #1557b0;
        }
        .btn-secondary {
            background: #5f6368;
        }
        .btn-secondary:hover {
            background: #4a4d52;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-block {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .link {
            color: #1a73e8;
            text-decoration: none;
        }
        .link:hover {
            text-decoration: underline;
        }
        .important {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="header">
            <h1>🔍 Google Custom Search API Setup</h1>
            <p>Configure your Google Custom Search API to enable real search results</p>
        </div>

        <div class="step">
            <h3><span class="step-number">1</span>Get Google API Key</h3>
            <p>First, you need to create a Google API key:</p>
            <ol>
                <li>Go to <a href="https://console.developers.google.com/" target="_blank" class="link">Google Cloud Console</a></li>
                <li>Create a new project or select an existing one</li>
                <li>Enable the "Custom Search API"</li>
                <li>Go to "Credentials" and create an API key</li>
                <li>Restrict the API key to "Custom Search API" for security</li>
            </ol>
        </div>

        <div class="step">
            <h3><span class="step-number">2</span>Create Custom Search Engine</h3>
            <p>Next, create a Custom Search Engine:</p>
            <ol>
                <li>Go to <a href="https://cse.google.com/" target="_blank" class="link">Google Custom Search Engine</a></li>
                <li>Click "Add" to create a new search engine</li>
                <li>Enter "*" in "Sites to search" for web-wide search</li>
                <li>Name your search engine (e.g., "My Search Engine")</li>
                <li>Click "Create" and copy the Search Engine ID</li>
            </ol>
        </div>

        <div class="step">
            <h3><span class="step-number">3</span>Configure API Credentials</h3>
            <div class="config-form">
                <div class="form-group">
                    <label for="apiKey">Google API Key:</label>
                    <input type="text" id="apiKey" placeholder="AIzaSy..." />
                </div>
                <div class="form-group">
                    <label for="searchEngineId">Search Engine ID:</label>
                    <input type="text" id="searchEngineId" placeholder="017576662512468239146:omuauf_lfve" />
                </div>
                <button class="btn" onclick="testConfiguration()">Test Configuration</button>
                <button class="btn btn-secondary" onclick="generateConfig()">Generate Config Code</button>
            </div>
            <div id="testResults" class="test-results" style="display: none;"></div>
        </div>

        <div class="step">
            <h3><span class="step-number">4</span>Update Configuration Files</h3>
            <p>Update the following files with your API credentials:</p>
            
            <h4>js/utils.js:</h4>
            <div class="code-block" id="utilsConfig">
const CONFIG = {
    GOOGLE_API_KEY: 'YOUR_API_KEY_HERE',
    SEARCH_ENGINE_ID: 'YOUR_SEARCH_ENGINE_ID_HERE',
    // ... rest of config
};
            </div>

            <h4>src/core/config/app.config.js:</h4>
            <div class="code-block" id="appConfig">
API: {
    GOOGLE_API_KEY: 'YOUR_API_KEY_HERE',
    SEARCH_ENGINE_ID: 'YOUR_SEARCH_ENGINE_ID_HERE',
    // ... rest of config
}
            </div>
        </div>

        <div class="important">
            <h4>⚠️ Important Security Notes:</h4>
            <ul>
                <li>Never commit API keys to public repositories</li>
                <li>Use environment variables in production</li>
                <li>Restrict API keys to specific domains</li>
                <li>Monitor API usage in Google Cloud Console</li>
                <li>Free tier allows 100 searches per day</li>
            </ul>
        </div>

        <div class="step">
            <h3><span class="step-number">5</span>Verify Setup</h3>
            <p>After updating the configuration:</p>
            <ol>
                <li>Refresh your search application</li>
                <li>Perform a test search</li>
                <li>Check browser console for API status messages</li>
                <li>Verify real results are displayed (not demo data)</li>
            </ol>
            <button class="btn" onclick="window.open('/', '_blank')">Test Search App</button>
        </div>
    </div>

    <script>
        async function testConfiguration() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const searchEngineId = document.getElementById('searchEngineId').value.trim();
            const resultsDiv = document.getElementById('testResults');
            
            if (!apiKey || !searchEngineId) {
                showResult('Please enter both API Key and Search Engine ID', 'error');
                return;
            }
            
            showResult('Testing API configuration...', 'warning');
            
            try {
                const testUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=test&num=1`;
                
                const response = await fetch(testUrl);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    showResult(`❌ API Test Failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                    return;
                }
                
                const data = await response.json();
                const totalResults = data.searchInformation?.totalResults || '0';
                const searchTime = data.searchInformation?.searchTime || '0';
                
                showResult(`✅ API Test Successful!<br>Found ${parseInt(totalResults).toLocaleString()} results in ${searchTime} seconds`, 'success');
                
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            }
        }
        
        function generateConfig() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const searchEngineId = document.getElementById('searchEngineId').value.trim();
            
            if (!apiKey || !searchEngineId) {
                showResult('Please enter both API Key and Search Engine ID first', 'error');
                return;
            }
            
            // Update config displays
            document.getElementById('utilsConfig').innerHTML = `const CONFIG = {
    GOOGLE_API_KEY: '${apiKey}',
    SEARCH_ENGINE_ID: '${searchEngineId}',
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    RESULTS_PER_PAGE: 10,
    MAX_CACHE_SIZE: 50,
    DEBOUNCE_DELAY: 300
};`;
            
            document.getElementById('appConfig').innerHTML = `API: {
    GOOGLE_API_KEY: '${apiKey}',
    SEARCH_ENGINE_ID: '${searchEngineId}',
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3
}`;
            
            showResult('✅ Configuration code generated! Copy and paste into your files.', 'success');
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            resultsDiv.style.display = 'block';
        }
    </script>
</body>
</html>
