<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border-left: 4px solid #f44336;
            color: #c62828;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        .test-btn {
            background: #4285f4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-btn:hover {
            background: #3367d6;
        }
        .result-item {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .result-title {
            color: #1a0dab;
            font-size: 18px;
            text-decoration: none;
            display: block;
            margin-bottom: 5px;
        }
        .result-url {
            color: #006621;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .result-snippet {
            color: #545454;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .api-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <h1>🔧 Google Custom Search API Test</h1>
    
    <div class="api-info">
        <strong>📡 API Configuration:</strong><br>
        <strong>API Key:</strong> AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM<br>
        <strong>Search Engine ID:</strong> 30a8567a4e17d49d2<br>
        <strong>Status:</strong> <span id="api-status">Testing...</span>
    </div>
    
    <div class="test-section">
        <h3>🧪 API Connection Test</h3>
        <button class="test-btn" onclick="testApiConnection()">Test API Connection</button>
        <div id="connection-result"></div>
    </div>
    
    <div class="test-section">
        <h3>🔍 Search Test</h3>
        <input type="text" id="search-query" placeholder="Enter search query" value="javascript" style="padding: 8px; width: 200px; margin-right: 10px;">
        <button class="test-btn" onclick="testSearch()">Test Search</button>
        <div id="search-result"></div>
    </div>
    
    <div class="test-section">
        <h3>🖼️ Image Search Test</h3>
        <button class="test-btn" onclick="testImageSearch()">Test Image Search</button>
        <div id="image-result"></div>
    </div>
    
    <div class="test-section">
        <h3>📊 API Quota Check</h3>
        <button class="test-btn" onclick="checkQuota()">Check Daily Quota</button>
        <div id="quota-result"></div>
    </div>
    
    <script>
        const API_KEY = 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM';
        const SEARCH_ENGINE_ID = '30a8567a4e17d49d2';
        const BASE_URL = 'https://www.googleapis.com/customsearch/v1';
        
        let searchCount = 0;
        
        // Test API connection
        async function testApiConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.innerHTML = '<div class="loading">🔄 Testing API connection...</div>';
            
            try {
                const url = `${BASE_URL}?key=${API_KEY}&cx=${SEARCH_ENGINE_ID}&q=test&num=1`;
                console.log('Testing API URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.items) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ API Connection Successful!</strong><br>
                            Response time: ${data.searchInformation?.searchTime || 'N/A'} seconds<br>
                            Total results available: ${data.searchInformation?.totalResults || 'N/A'}<br>
                            Found ${data.items.length} results
                        </div>
                    `;
                    document.getElementById('api-status').innerHTML = '<span style="color: green;">✅ Connected</span>';
                } else {
                    throw new Error(data.error?.message || 'Unknown API error');
                }
            } catch (error) {
                console.error('API test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ API Connection Failed</strong><br>
                        Error: ${error.message}<br>
                        <small>Check your API key and Search Engine ID</small>
                    </div>
                `;
                document.getElementById('api-status').innerHTML = '<span style="color: red;">❌ Failed</span>';
            }
        }
        
        // Test search functionality
        async function testSearch() {
            const query = document.getElementById('search-query').value.trim();
            const resultDiv = document.getElementById('search-result');
            
            if (!query) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter a search query</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="loading">🔍 Searching...</div>';
            searchCount++;
            
            try {
                const url = `${BASE_URL}?key=${API_KEY}&cx=${SEARCH_ENGINE_ID}&q=${encodeURIComponent(query)}&num=5`;
                console.log('Search URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.items) {
                    let html = `
                        <div class="success">
                            <strong>✅ Search Successful!</strong><br>
                            Query: "${query}"<br>
                            Found: ${data.searchInformation?.totalResults || 'N/A'} total results<br>
                            Search time: ${data.searchInformation?.searchTime || 'N/A'} seconds<br>
                            Showing: ${data.items.length} results
                        </div>
                    `;
                    
                    data.items.forEach((item, index) => {
                        html += `
                            <div class="result-item">
                                <a href="${item.link}" class="result-title" target="_blank">
                                    ${item.title}
                                </a>
                                <div class="result-url">${item.displayLink}</div>
                                <div class="result-snippet">${item.snippet}</div>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(data.error?.message || 'Search failed');
                }
            } catch (error) {
                console.error('Search failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ Search Failed</strong><br>
                        Error: ${error.message}<br>
                        Query: "${query}"
                    </div>
                `;
            }
        }
        
        // Test image search
        async function testImageSearch() {
            const resultDiv = document.getElementById('image-result');
            resultDiv.innerHTML = '<div class="loading">🖼️ Testing image search...</div>';
            searchCount++;
            
            try {
                const url = `${BASE_URL}?key=${API_KEY}&cx=${SEARCH_ENGINE_ID}&q=cats&searchType=image&num=3`;
                console.log('Image search URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.items) {
                    let html = `
                        <div class="success">
                            <strong>✅ Image Search Successful!</strong><br>
                            Found: ${data.items.length} images
                        </div>
                    `;
                    
                    data.items.forEach((item, index) => {
                        html += `
                            <div class="result-item">
                                <img src="${item.link}" alt="${item.title}" style="max-width: 200px; max-height: 150px; object-fit: cover;" onerror="this.style.display='none'">
                                <div><strong>${item.title}</strong></div>
                                <div class="result-url">${item.displayLink}</div>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(data.error?.message || 'Image search failed');
                }
            } catch (error) {
                console.error('Image search failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ Image Search Failed</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        // Check quota usage
        function checkQuota() {
            const resultDiv = document.getElementById('quota-result');
            resultDiv.innerHTML = `
                <div class="warning">
                    <strong>📊 Quota Information</strong><br>
                    Searches performed in this session: ${searchCount}<br>
                    <br>
                    <strong>Google Custom Search API Limits:</strong><br>
                    • Free tier: 100 queries per day<br>
                    • Paid tier: Up to 10,000 queries per day<br>
                    • Rate limit: 10 queries per second<br>
                    <br>
                    <small>Note: Actual quota usage can only be checked in Google Cloud Console</small>
                </div>
            `;
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            testApiConnection();
        });
    </script>
</body>
</html>
