// Autocomplete functionality for search suggestions

class AutoComplete {
    constructor(inputElement, suggestionsElement) {
        this.input = inputElement;
        this.suggestions = suggestionsElement;
        this.selectedIndex = -1;
        this.currentSuggestions = [];
        this.isVisible = false;
        
        this.init();
    }
    
    init() {
        if (!this.input || !this.suggestions) return;
        
        this.bindEvents();
    }
    
    bindEvents() {
        // Input events
        this.input.addEventListener('input', Utils.debounce((e) => {
            const query = e.target.value.trim();
            if (query.length > 1) {
                this.fetchSuggestions(query);
            } else {
                this.hide();
            }
        }, Utils.CONFIG.DEBOUNCE_DELAY));
        
        // Keyboard navigation
        this.input.addEventListener('keydown', (e) => {
            if (!this.isVisible) return;
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.selectNext();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.selectPrevious();
                    break;
                case 'Enter':
                    e.preventDefault();
                    this.selectCurrent();
                    break;
                case 'Escape':
                    this.hide();
                    break;
            }
        });
        
        // Focus events
        this.input.addEventListener('focus', () => {
            const query = this.input.value.trim();
            if (query.length > 1 && this.currentSuggestions.length > 0) {
                this.show();
            }
        });
        
        this.input.addEventListener('blur', () => {
            // Delay hiding to allow clicking on suggestions
            setTimeout(() => this.hide(), 150);
        });
        
        // Click outside to hide
        document.addEventListener('click', (e) => {
            if (!this.input.contains(e.target) && !this.suggestions.contains(e.target)) {
                this.hide();
            }
        });
    }
    
    async fetchSuggestions(query) {
        try {
            const suggestions = await this.getSuggestions(query);
            this.currentSuggestions = suggestions;
            this.render(suggestions);
            this.selectedIndex = -1;
        } catch (error) {
            console.error('Failed to fetch suggestions:', error);
            this.hide();
        }
    }
    
    async getSuggestions(query) {
        const cacheKey = `autocomplete-${query}`;
        
        // Check cache first
        const cached = Utils.suggestionCache.get(cacheKey);
        if (cached) {
            return cached;
        }
        
        // Try multiple suggestion sources
        const suggestions = await Promise.race([
            this.getGoogleSuggestions(query),
            this.getLocalSuggestions(query)
        ]);
        
        // Cache the results
        Utils.suggestionCache.set(cacheKey, suggestions);
        
        return suggestions;
    }
    
    async getGoogleSuggestions(query) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            const callbackName = `autocomplete_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            // Set up callback
            window[callbackName] = (data) => {
                try {
                    const suggestions = data[1] || [];
                    resolve(suggestions.slice(0, 8)); // Limit to 8 suggestions
                } catch (error) {
                    reject(error);
                } finally {
                    // Cleanup
                    if (document.head.contains(script)) {
                        document.head.removeChild(script);
                    }
                    delete window[callbackName];
                }
            };
            
            // Set up script
            script.src = `https://suggestqueries.google.com/complete/search?client=firefox&q=${encodeURIComponent(query)}&callback=${callbackName}`;
            script.onerror = () => {
                reject(new Error('Failed to load suggestions'));
                if (document.head.contains(script)) {
                    document.head.removeChild(script);
                }
                delete window[callbackName];
            };
            
            document.head.appendChild(script);
            
            // Timeout after 3 seconds
            setTimeout(() => {
                if (window[callbackName]) {
                    reject(new Error('Suggestions timeout'));
                    if (document.head.contains(script)) {
                        document.head.removeChild(script);
                    }
                    delete window[callbackName];
                }
            }, 3000);
        });
    }
    
    getLocalSuggestions(query) {
        // Fallback to local suggestions based on search history
        const history = Utils.getFromStorage('searchHistory') || [];
        const filtered = history.filter(item => 
            item.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);
        
        return Promise.resolve(filtered);
    }
    
    render(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            this.hide();
            return;
        }

        this.suggestions.innerHTML = '';

        suggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.setAttribute('data-index', index);
            item.setAttribute('role', 'option');

            // Add search icon
            const icon = document.createElement('svg');
            icon.className = 'suggestion-icon';
            icon.setAttribute('viewBox', '0 0 24 24');
            icon.innerHTML = '<path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>';

            // Add suggestion text with highlighting
            const textSpan = document.createElement('span');
            textSpan.className = 'suggestion-text';
            const highlightedText = this.highlightMatch(suggestion, this.input.value);
            textSpan.innerHTML = highlightedText;

            // Add remove button
            const removeBtn = document.createElement('svg');
            removeBtn.className = 'suggestion-remove';
            removeBtn.setAttribute('viewBox', '0 0 24 24');
            removeBtn.setAttribute('title', 'Remove');
            removeBtn.innerHTML = '<path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>';

            // Remove button click handler
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeSuggestion(suggestion);
            });

            // Assemble the item
            item.appendChild(icon);
            item.appendChild(textSpan);
            item.appendChild(removeBtn);

            // Click handler for the whole item
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectSuggestion(suggestion);
            });

            // Mouse hover
            item.addEventListener('mouseenter', () => {
                this.setSelected(index);
            });

            this.suggestions.appendChild(item);
        });

        this.show();
    }
    
    highlightMatch(text, query) {
        if (!query) return Utils.escapeHtml(text);
        
        const escapedText = Utils.escapeHtml(text);
        const escapedQuery = Utils.escapeHtml(query);
        const regex = new RegExp(`(${escapedQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        
        return escapedText.replace(regex, '<strong>$1</strong>');
    }
    
    show() {
        if (this.currentSuggestions.length === 0) return;
        
        this.suggestions.style.display = 'block';
        this.suggestions.setAttribute('aria-expanded', 'true');
        this.input.setAttribute('aria-expanded', 'true');
        this.isVisible = true;
    }
    
    hide() {
        this.suggestions.style.display = 'none';
        this.suggestions.setAttribute('aria-expanded', 'false');
        this.input.setAttribute('aria-expanded', 'false');
        this.isVisible = false;
        this.selectedIndex = -1;
    }
    
    selectNext() {
        if (this.selectedIndex < this.currentSuggestions.length - 1) {
            this.setSelected(this.selectedIndex + 1);
        }
    }
    
    selectPrevious() {
        if (this.selectedIndex > 0) {
            this.setSelected(this.selectedIndex - 1);
        } else if (this.selectedIndex === 0) {
            this.setSelected(-1);
            this.input.value = this.input.getAttribute('data-original-value') || '';
        }
    }
    
    setSelected(index) {
        // Remove previous selection
        const previousSelected = this.suggestions.querySelector('.suggestion-item.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }
        
        this.selectedIndex = index;
        
        if (index >= 0 && index < this.currentSuggestions.length) {
            // Add new selection
            const newSelected = this.suggestions.children[index];
            if (newSelected) {
                newSelected.classList.add('selected');
                
                // Update input value
                if (!this.input.hasAttribute('data-original-value')) {
                    this.input.setAttribute('data-original-value', this.input.value);
                }
                this.input.value = this.currentSuggestions[index];
                
                // Scroll into view if needed
                newSelected.scrollIntoView({ block: 'nearest' });
            }
        }
    }
    
    selectCurrent() {
        if (this.selectedIndex >= 0 && this.selectedIndex < this.currentSuggestions.length) {
            this.selectSuggestion(this.currentSuggestions[this.selectedIndex]);
        } else {
            // No selection, use current input value
            const query = this.input.value.trim();
            if (query) {
                this.performSearch(query);
            }
        }
    }
    
    selectSuggestion(suggestion) {
        this.input.value = suggestion;
        this.input.removeAttribute('data-original-value');
        this.hide();
        this.performSearch(suggestion);
    }

    removeSuggestion(suggestion) {
        // Remove from current suggestions
        const index = this.currentSuggestions.indexOf(suggestion);
        if (index > -1) {
            this.currentSuggestions.splice(index, 1);
        }

        // Remove from search history
        const history = Utils.getFromStorage('searchHistory') || [];
        const filteredHistory = history.filter(item => item !== suggestion);
        Utils.saveToStorage('searchHistory', filteredHistory);

        // Re-render suggestions
        this.render(this.currentSuggestions);
    }
    
    performSearch(query) {
        // Trigger search based on current page
        if (window.location.pathname.includes('results.html')) {
            // On results page, update search
            if (window.resultsPage) {
                window.resultsPage.updateSearch(query, 1, window.resultsPage.currentType);
            }
        } else {
            // On main page, navigate to results
            if (window.searchEngine) {
                window.searchEngine.performSearch(query);
            }
        }
    }
    
    // Public methods
    clear() {
        this.currentSuggestions = [];
        this.hide();
    }
    
    destroy() {
        this.hide();
        // Remove event listeners would go here if needed
    }
}

// Initialize autocomplete when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('search-input');
    const suggestionsContainer = document.getElementById('suggestions');
    
    if (searchInput && suggestionsContainer) {
        window.autoComplete = new AutoComplete(searchInput, suggestionsContainer);
    }
});

// Export for use in other modules
window.AutoComplete = AutoComplete;
