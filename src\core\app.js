/**
 * Main Application Class
 * Orchestrates the entire Google Clone application
 */

import { APP_CONFIG } from './config/app.config.js';
import logger from './utils/logger.js';
import dom from './utils/dom.js';
import apiService from './services/api.service.js';
import SearchBoxComponent from './components/search-box.component.js';

class GoogleCloneApp {
  constructor() {
    this.logger = logger.child('App');
    this.components = new Map();
    this.isInitialized = false;
    this.currentPage = this.detectCurrentPage();
    
    // Bind methods
    this.init = this.init.bind(this);
    this.handleSearch = this.handleSearch.bind(this);
    this.handleError = this.handleError.bind(this);
  }
  
  /**
   * Initialize the application
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isInitialized) {
      this.logger.warn('Application already initialized');
      return;
    }
    
    try {
      this.logger.info('Initializing Google Clone application', {
        version: APP_CONFIG.APP_VERSION,
        page: this.currentPage,
        features: APP_CONFIG.FEATURES
      });
      
      // Wait for DOM to be ready
      await this.waitForDOM();
      
      // Initialize based on current page
      await this.initializePage();
      
      // Set up global event listeners
      this.setupGlobalEventListeners();
      
      // Set up error handling
      this.setupErrorHandling();
      
      // Mark as initialized
      this.isInitialized = true;
      
      this.logger.info('Application initialized successfully');
      
      // Emit ready event
      this.emit('ready');
      
    } catch (error) {
      this.logger.error('Failed to initialize application', error);
      this.handleError(error);
      throw error;
    }
  }
  
  /**
   * Wait for DOM to be ready
   * @returns {Promise<void>}
   */
  waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve, { once: true });
      } else {
        resolve();
      }
    });
  }
  
  /**
   * Detect current page type
   * @returns {string} Page type
   */
  detectCurrentPage() {
    const path = window.location.pathname;
    const search = window.location.search;
    
    if (path.includes('results') || search.includes('q=')) {
      return 'results';
    } else if (path.includes('demo') || path.includes('showcase')) {
      return 'demo';
    } else {
      return 'home';
    }
  }
  
  /**
   * Initialize page-specific components
   * @returns {Promise<void>}
   */
  async initializePage() {
    this.logger.debug(`Initializing ${this.currentPage} page`);
    
    switch (this.currentPage) {
      case 'home':
        await this.initializeHomePage();
        break;
      case 'results':
        await this.initializeResultsPage();
        break;
      case 'demo':
        await this.initializeDemoPage();
        break;
      default:
        this.logger.warn(`Unknown page type: ${this.currentPage}`);
    }
  }
  
  /**
   * Initialize home page components
   * @returns {Promise<void>}
   */
  async initializeHomePage() {
    // Initialize main search box
    const searchContainer = dom.getElementById('search-container') || 
                           dom.querySelector('.search-container');
    
    if (searchContainer) {
      const searchBox = new SearchBoxComponent(searchContainer, {
        placeholder: '',
        showVoiceSearch: true,
        showImageSearch: true,
        enableAutocomplete: true
      });
      
      await searchBox.init();
      this.addComponent('searchBox', searchBox);
      
      // Set up search event handlers
      searchBox.addEventListener('search', this.handleSearch);
      searchBox.addEventListener('voiceSearch', this.handleVoiceSearch.bind(this));
      searchBox.addEventListener('imageSearch', this.handleImageSearch.bind(this));
    }
    
    // Initialize buttons
    this.initializeButtons();
    
    // Initialize footer
    this.initializeFooter();
  }
  
  /**
   * Initialize results page components
   * @returns {Promise<void>}
   */
  async initializeResultsPage() {
    // Initialize header search box
    const headerSearchContainer = dom.querySelector('.header .search-container');
    
    if (headerSearchContainer) {
      const headerSearchBox = new SearchBoxComponent(headerSearchContainer, {
        enableAutocomplete: true,
        showVoiceSearch: true,
        showImageSearch: true
      });
      
      await headerSearchBox.init();
      this.addComponent('headerSearchBox', headerSearchBox);
      
      // Set up search event handlers
      headerSearchBox.addEventListener('search', this.handleSearch);
    }
    
    // Initialize navigation tabs
    this.initializeNavigationTabs();
    
    // Initialize results container
    this.initializeResultsContainer();
    
    // Load initial results if query exists
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q');
    
    if (query) {
      await this.performSearch(query, {
        start: parseInt(urlParams.get('start')) || 1,
        type: urlParams.get('type') || 'web'
      });
    }
  }
  
  /**
   * Initialize demo page components
   * @returns {Promise<void>}
   */
  async initializeDemoPage() {
    // Initialize any demo-specific components
    this.initializeDemoComponents();
  }
  
  /**
   * Initialize buttons
   */
  initializeButtons() {
    const searchButton = dom.getElementById('search-button');
    const luckyButton = dom.getElementById('lucky-button');
    
    if (searchButton) {
      dom.addEventListener(searchButton, 'click', this.handleSearchButton.bind(this));
    }
    
    if (luckyButton) {
      dom.addEventListener(luckyButton, 'click', this.handleLuckyButton.bind(this));
    }
  }
  
  /**
   * Initialize footer
   */
  initializeFooter() {
    // Add any footer-specific functionality
    const footerLinks = dom.querySelectorAll('.footer a');
    
    footerLinks.forEach(link => {
      dom.addEventListener(link, 'click', this.handleFooterLink.bind(this));
    });
  }
  
  /**
   * Initialize navigation tabs
   */
  initializeNavigationTabs() {
    const navTabs = dom.querySelectorAll('.nav-tab');
    
    navTabs.forEach(tab => {
      dom.addEventListener(tab, 'click', this.handleNavTabClick.bind(this));
    });
  }
  
  /**
   * Initialize results container
   */
  initializeResultsContainer() {
    this.resultsContainer = dom.getElementById('results-container');
    this.resultsInfo = dom.getElementById('results-info');
    this.loadingElement = dom.getElementById('loading');
    this.paginationContainer = dom.getElementById('pagination');
  }
  
  /**
   * Initialize demo components
   */
  initializeDemoComponents() {
    // Add demo-specific initialization
    const demoSearchBoxes = dom.querySelectorAll('.demo-section .search-container');
    
    demoSearchBoxes.forEach(async (container, index) => {
      const searchBox = new SearchBoxComponent(container, {
        enableAutocomplete: false
      });
      
      await searchBox.init();
      this.addComponent(`demoSearchBox${index}`, searchBox);
    });
  }
  
  /**
   * Set up global event listeners
   */
  setupGlobalEventListeners() {
    // Keyboard shortcuts
    dom.addEventListener(document, 'keydown', this.handleGlobalKeydown.bind(this));
    
    // Window events
    dom.addEventListener(window, 'popstate', this.handlePopState.bind(this));
    dom.addEventListener(window, 'resize', this.handleResize.bind(this));
    
    // Performance monitoring
    if (APP_CONFIG.DEV.PERFORMANCE_MONITORING) {
      this.setupPerformanceMonitoring();
    }
  }
  
  /**
   * Set up error handling
   */
  setupErrorHandling() {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.logger.error('Global error', event.error);
      this.handleError(event.error);
    });
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.logger.error('Unhandled promise rejection', event.reason);
      this.handleError(event.reason);
    });
  }
  
  /**
   * Set up performance monitoring
   */
  setupPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0];
      this.logger.performance('Page Load', perfData.loadEventEnd - perfData.fetchStart, {
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.fetchStart,
        firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
      });
    });
  }
  
  /**
   * Handle search
   * @param {CustomEvent} event - Search event
   */
  async handleSearch(event) {
    const { query } = event.detail;
    
    if (!query || query.trim().length === 0) {
      this.handleError(new Error(APP_CONFIG.ERRORS.INVALID_QUERY));
      return;
    }
    
    // Navigate to results page if on home page
    if (this.currentPage === 'home') {
      const url = new URL('/results.html', window.location.origin);
      url.searchParams.set('q', query);
      url.searchParams.set('start', '1');
      url.searchParams.set('type', 'web');
      
      window.location.href = url.toString();
    } else {
      // Perform search on results page
      await this.performSearch(query);
    }
  }
  
  /**
   * Perform search
   * @param {string} query - Search query
   * @param {Object} options - Search options
   */
  async performSearch(query, options = {}) {
    const startTime = performance.now();
    
    try {
      this.showLoading();
      
      const searchOptions = {
        start: options.start || 1,
        type: options.type || 'web',
        num: APP_CONFIG.SEARCH.RESULTS_PER_PAGE,
        ...options
      };
      
      this.logger.info('Performing search', { query, options: searchOptions });
      
      const results = await apiService.search(query, searchOptions);
      
      this.displayResults(results, query, searchOptions);
      this.updateURL(query, searchOptions);
      
      const duration = performance.now() - startTime;
      this.logger.performance('Search', duration, { query, resultsCount: results.items.length });
      
    } catch (error) {
      this.logger.error('Search failed', error);
      this.handleSearchError(error);
    } finally {
      this.hideLoading();
    }
  }
  
  /**
   * Display search results
   * @param {Object} results - Search results
   * @param {string} query - Search query
   * @param {Object} options - Search options
   */
  displayResults(results, query, options) {
    if (!this.resultsContainer) return;
    
    // Update results info
    if (this.resultsInfo) {
      const resultsText = `About ${results.totalResults.toLocaleString()} results (${results.searchTime} seconds)`;
      this.resultsInfo.textContent = resultsText;
    }
    
    // Clear previous results
    this.resultsContainer.innerHTML = '';
    
    // Render results
    results.items.forEach((item, index) => {
      const resultElement = this.createResultElement(item, index, query);
      this.resultsContainer.appendChild(resultElement);
    });
    
    // Update pagination
    this.updatePagination(results, query, options);
  }
  
  /**
   * Create result element
   * @param {Object} item - Result item
   * @param {number} index - Item index
   * @param {string} query - Search query
   * @returns {HTMLElement} Result element
   */
  createResultElement(item, index, query) {
    const resultDiv = dom.createElement('div', {
      className: 'result-item',
      style: `animation-delay: ${index * 0.1}s`
    });
    
    const domain = this.extractDomain(item.link);
    const highlightedSnippet = this.highlightSearchTerms(item.snippet || '', query);
    
    resultDiv.innerHTML = `
      <div class="result-url">
        <div class="favicon"></div>
        <span class="breadcrumb">${domain}</span>
      </div>
      <a href="${item.link}" class="result-title" target="_blank" rel="noopener">
        ${item.title}
      </a>
      <div class="result-snippet">${highlightedSnippet}</div>
    `;
    
    return resultDiv;
  }
  
  /**
   * Update pagination
   * @param {Object} results - Search results
   * @param {string} query - Search query
   * @param {Object} options - Search options
   */
  updatePagination(results, query, options) {
    if (!this.paginationContainer) return;
    
    // Implementation would go here
    // This is a simplified version
    this.paginationContainer.innerHTML = `
      <div class="pagination-nav">
        <span>Page ${Math.ceil(options.start / APP_CONFIG.SEARCH.RESULTS_PER_PAGE)}</span>
      </div>
    `;
  }
  
  /**
   * Show loading state
   */
  showLoading() {
    if (this.loadingElement) {
      dom.show(this.loadingElement);
    }
    
    if (this.resultsContainer) {
      dom.hide(this.resultsContainer);
    }
  }
  
  /**
   * Hide loading state
   */
  hideLoading() {
    if (this.loadingElement) {
      dom.hide(this.loadingElement);
    }
    
    if (this.resultsContainer) {
      dom.show(this.resultsContainer);
    }
  }
  
  /**
   * Handle various events
   */
  handleSearchButton() {
    const searchBox = this.getComponent('searchBox');
    if (searchBox) {
      const query = searchBox.getValue();
      this.handleSearch({ detail: { query } });
    }
  }
  
  handleLuckyButton() {
    const searchBox = this.getComponent('searchBox');
    if (searchBox) {
      const query = searchBox.getValue();
      // Implement "I'm Feeling Lucky" functionality
      this.logger.info('I\'m Feeling Lucky clicked', { query });
    }
  }
  
  handleVoiceSearch() {
    this.logger.info('Voice search requested');
    // Implement voice search functionality
  }
  
  handleImageSearch() {
    this.logger.info('Image search requested');
    // Implement image search functionality
  }
  
  handleNavTabClick(event) {
    event.preventDefault();
    const tab = event.currentTarget;
    const type = tab.dataset.type;
    
    // Update active tab
    dom.querySelectorAll('.nav-tab').forEach(t => dom.removeClass(t, 'active'));
    dom.addClass(tab, 'active');
    
    // Perform search with new type
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q');
    
    if (query) {
      this.performSearch(query, { type, start: 1 });
    }
  }
  
  handleGlobalKeydown(event) {
    // Global keyboard shortcuts
    if (event.key === '/' && !event.ctrlKey && !event.metaKey) {
      event.preventDefault();
      const searchBox = this.getComponent('searchBox') || this.getComponent('headerSearchBox');
      if (searchBox) {
        searchBox.focus();
      }
    }
  }
  
  handlePopState(event) {
    // Handle browser back/forward
    this.currentPage = this.detectCurrentPage();
    // Re-initialize if needed
  }
  
  handleResize(event) {
    // Handle window resize
    this.emit('resize', { width: window.innerWidth, height: window.innerHeight });
  }
  
  handleFooterLink(event) {
    // Handle footer link clicks
    this.logger.debug('Footer link clicked', { href: event.target.href });
  }
  
  handleError(error) {
    this.logger.error('Application error', error);
    
    // Show user-friendly error message
    this.showErrorMessage(this.getErrorMessage(error));
  }
  
  handleSearchError(error) {
    this.hideLoading();
    
    let message = APP_CONFIG.ERRORS.API_ERROR;
    
    if (error.status === 429) {
      message = APP_CONFIG.ERRORS.RATE_LIMIT;
    } else if (error.name === 'NetworkError') {
      message = APP_CONFIG.ERRORS.NETWORK_ERROR;
    }
    
    this.showErrorMessage(message);
  }
  
  /**
   * Utility methods
   */
  addComponent(name, component) {
    this.components.set(name, component);
    this.logger.debug(`Added component: ${name}`);
  }
  
  getComponent(name) {
    return this.components.get(name);
  }
  
  removeComponent(name) {
    const component = this.components.get(name);
    if (component) {
      component.destroy();
      this.components.delete(name);
      this.logger.debug(`Removed component: ${name}`);
    }
  }
  
  emit(eventType, detail = null) {
    const event = new CustomEvent(`app:${eventType}`, { detail });
    document.dispatchEvent(event);
    this.logger.debug(`Emitted event: app:${eventType}`, detail);
  }
  
  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  }
  
  highlightSearchTerms(text, query) {
    if (!query || query.length < 2) return text;
    
    const terms = query.split(' ').filter(term => term.length > 1);
    let highlightedText = text;
    
    terms.forEach(term => {
      const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<em>$1</em>');
    });
    
    return highlightedText;
  }
  
  updateURL(query, options) {
    const url = new URL(window.location);
    url.searchParams.set('q', query);
    url.searchParams.set('start', options.start.toString());
    url.searchParams.set('type', options.type);
    
    window.history.pushState({}, '', url.toString());
  }
  
  getErrorMessage(error) {
    if (error.message && Object.values(APP_CONFIG.ERRORS).includes(error.message)) {
      return error.message;
    }
    
    return APP_CONFIG.ERRORS.API_ERROR;
  }
  
  showErrorMessage(message) {
    // Create or update error display
    let errorElement = dom.getElementById('error-message');
    
    if (!errorElement) {
      errorElement = dom.createElement('div', {
        id: 'error-message',
        className: 'error-message',
        style: 'display: none;'
      });
      
      document.body.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    dom.show(errorElement);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      dom.hide(errorElement);
    }, 5000);
  }
  
  /**
   * Destroy the application
   */
  destroy() {
    this.logger.info('Destroying application');
    
    // Destroy all components
    this.components.forEach((component, name) => {
      this.removeComponent(name);
    });
    
    // Clear caches
    apiService.clearCache();
    dom.clearCache();
    
    this.isInitialized = false;
    this.logger.info('Application destroyed');
  }
}

// Create and export singleton instance
const app = new GoogleCloneApp();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

export { GoogleCloneApp };
export default app;
