/**
 * CSS Custom Properties (Variables)
 * Centralized design tokens for the Google Clone
 */

:root {
  /* Google Brand Colors */
  --google-blue: #4285f4;
  --google-red: #ea4335;
  --google-yellow: #fbbc05;
  --google-green: #34a853;
  
  /* Text Colors */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-disabled: #9aa0a6;
  --text-link: #1a0dab;
  --text-link-visited: #609;
  --text-link-hover: #1a73e8;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f1f3f4;
  --bg-hover: rgba(60, 64, 67, 0.08);
  --bg-active: #e8eaed;
  
  /* Border Colors */
  --border-primary: #dadce0;
  --border-secondary: #dfe1e5;
  --border-focus: rgba(223, 225, 229, 0);
  
  /* Shadow Colors */
  --shadow-light: rgba(0, 0, 0, 0.12);
  --shadow-medium: rgba(0, 0, 0, 0.16);
  --shadow-dark: rgba(0, 0, 0, 0.24);
  --shadow-search: rgba(64, 60, 67, 0.16);
  
  /* Typography */
  --font-family: arial, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 48px;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* Spacing Scale */
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-7: 28px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 24px;
  --radius-full: 50%;
  
  /* Transitions */
  --transition-fast: 0.1s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  --transition-search: 0.2s ease;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-modal: 2000;
  --z-tooltip: 3000;
  --z-notification: 4000;
  
  /* Component Sizes */
  --header-height: 60px;
  --search-box-height: 44px;
  --search-box-height-sm: 36px;
  --button-height: 36px;
  --nav-tab-height: 48px;
  
  /* Breakpoints */
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1200px;
  
  /* Layout */
  --container-max-width: 1200px;
  --content-max-width: 600px;
  --sidebar-width: 150px;
  
  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* Animation Easings */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Focus Ring */
  --focus-ring: 0 0 0 2px var(--google-blue);
  --focus-ring-offset: 2px;
  
  /* Button Styles */
  --button-padding: 10px 20px;
  --button-padding-sm: 8px 16px;
  --button-padding-lg: 12px 24px;
  
  /* Input Styles */
  --input-padding: 0 var(--space-4);
  --input-border: 1px solid var(--border-secondary);
  --input-border-focus: 1px solid var(--border-focus);
  
  /* Card Styles */
  --card-padding: var(--space-6);
  --card-border: 1px solid var(--border-primary);
  --card-shadow: 0 1px 3px var(--shadow-light), 0 1px 2px var(--shadow-medium);
  --card-shadow-hover: 0 4px 12px var(--shadow-medium);
  
  /* Search Specific */
  --search-container-max-width: 584px;
  --search-suggestions-max-height: 300px;
  --search-icon-size: 16px;
  --search-icon-container-size: 24px;
  
  /* Results Page */
  --results-margin-left: var(--sidebar-width);
  --result-item-margin: var(--space-7);
  --result-title-size: var(--font-size-xl);
  --result-url-size: var(--font-size-base);
  --result-snippet-size: var(--font-size-base);
  
  /* Navigation */
  --nav-tab-padding: 12px 16px;
  --nav-tab-border-width: 3px;
  --nav-icon-size: 16px;
  
  /* Performance */
  --will-change-transform: transform;
  --will-change-opacity: opacity;
  --will-change-auto: auto;
}

/* Dark Mode Variables (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-disabled: #5f6368;
    --bg-primary: #202124;
    --bg-secondary: #303134;
    --bg-tertiary: #3c4043;
    --border-primary: #5f6368;
    --border-secondary: #5f6368;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
    --duration-fast: 0s;
    --duration-normal: 0s;
    --duration-slow: 0s;
  }
}

/* High Contrast */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --text-secondary: var(--text-primary);
  }
}

/* Print Styles */
@media print {
  :root {
    --bg-primary: #ffffff;
    --bg-secondary: #ffffff;
    --text-primary: #000000;
    --text-secondary: #000000;
    --shadow-light: none;
    --shadow-medium: none;
    --shadow-dark: none;
  }
}
