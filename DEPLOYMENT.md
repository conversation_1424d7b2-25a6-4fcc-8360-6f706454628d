# 🚀 Production Deployment Guide

## Overview
This guide covers deploying the Google Search Clone to production with enterprise-grade performance, security, and monitoring.

## 📋 Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Domain name configured
- [ ] SSL certificate obtained
- [ ] CDN configured (Cloudflare/AWS CloudFront)
- [ ] Google Custom Search API credentials
- [ ] Analytics tracking IDs
- [ ] Error monitoring service (Sentry/LogRocket)

### 2. Configuration
- [ ] Update `deployment.config.js` with production values
- [ ] Set environment variables
- [ ] Configure API rate limits
- [ ] Set up monitoring endpoints

### 3. Security
- [ ] Content Security Policy configured
- [ ] HTTPS enforced
- [ ] Security headers implemented
- [ ] API keys secured
- [ ] Rate limiting enabled

## 🔧 Deployment Options

### Option 1: Static Hosting (Recommended)
**Best for**: High performance, low cost, global distribution

**Platforms**: Netlify, Vercel, AWS S3 + CloudFront, GitHub Pages

```bash
# Build for production
npm run build

# Deploy to Netlify
netlify deploy --prod --dir=dist

# Deploy to Vercel
vercel --prod

# Deploy to AWS S3
aws s3 sync dist/ s3://your-bucket-name --delete
```

### Option 2: Server-Side Hosting
**Best for**: Custom server logic, advanced analytics

**Platforms**: AWS EC2, Google Cloud, DigitalOcean, Heroku

```bash
# Using Docker
docker build -t search-app .
docker run -p 80:80 search-app

# Using PM2
pm2 start server.js --name search-app
```

### Option 3: Serverless
**Best for**: Auto-scaling, pay-per-use

**Platforms**: AWS Lambda, Vercel Functions, Netlify Functions

## 🏗️ Build Process

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment
```bash
# Create .env file
GOOGLE_API_KEY=your_api_key
SEARCH_ENGINE_ID=your_search_engine_id
ANALYTICS_ID=your_analytics_id
```

### 3. Build for Production
```bash
# Minify and optimize
npm run build:prod

# Test production build
npm run serve:prod
```

## 🔒 Security Configuration

### 1. Content Security Policy
Add to your server configuration:
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://www.googleapis.com;";
```

### 2. Security Headers
```nginx
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header Referrer-Policy strict-origin-when-cross-origin;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

### 3. API Security
- Use environment variables for API keys
- Implement rate limiting
- Set up CORS properly
- Monitor API usage

## 📊 Performance Optimization

### 1. Asset Optimization
```bash
# Compress images
imagemin src/images/* --out-dir=dist/images

# Minify CSS/JS
npm run minify

# Generate service worker
npm run sw:generate
```

### 2. CDN Configuration
```javascript
// Configure CDN URLs
const CDN_BASE = 'https://cdn.yourdomain.com';
const ASSET_URLS = {
    css: `${CDN_BASE}/styles/`,
    js: `${CDN_BASE}/js/`,
    images: `${CDN_BASE}/images/`
};
```

### 3. Caching Strategy
```nginx
# Static assets
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML files
location ~* \.html$ {
    expires 1h;
    add_header Cache-Control "public, must-revalidate";
}
```

## 📈 Monitoring & Analytics

### 1. Performance Monitoring
```javascript
// Core Web Vitals tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

### 2. Error Tracking
```javascript
// Sentry integration
import * as Sentry from "@sentry/browser";

Sentry.init({
    dsn: "YOUR_SENTRY_DSN",
    environment: "production"
});
```

### 3. Analytics Setup
```javascript
// Google Analytics 4
gtag('config', 'GA_MEASUREMENT_ID', {
    page_title: 'Search Results',
    page_location: window.location.href
});
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
      - name: Build
        run: npm run build:prod
      - name: Deploy
        run: npm run deploy
```

## 🧪 Testing

### 1. Performance Testing
```bash
# Lighthouse CI
npm install -g @lhci/cli
lhci autorun

# Load testing
npm install -g artillery
artillery run load-test.yml
```

### 2. Security Testing
```bash
# Security audit
npm audit
npm audit fix

# OWASP ZAP scan
zap-baseline.py -t https://yourdomain.com
```

## 📱 PWA Deployment

### 1. Service Worker
- Ensure service-worker.js is in root directory
- Configure caching strategies
- Test offline functionality

### 2. App Store Deployment
```bash
# Generate PWA assets
npm run pwa:generate

# Create app store packages
npm run pwa:package
```

## 🚨 Troubleshooting

### Common Issues
1. **API Rate Limits**: Implement caching and fallbacks
2. **CORS Errors**: Configure server headers properly
3. **Performance Issues**: Optimize images and enable compression
4. **PWA Installation**: Check manifest.json and HTTPS

### Health Checks
```bash
# Check deployment
curl -f https://yourdomain.com/health

# Monitor performance
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com
```

## 📞 Support

For deployment issues:
1. Check the troubleshooting section
2. Review server logs
3. Test in staging environment first
4. Monitor performance metrics

## 🔄 Updates

### Rolling Updates
1. Deploy to staging
2. Run automated tests
3. Gradual rollout (10% → 50% → 100%)
4. Monitor metrics
5. Rollback if issues detected
