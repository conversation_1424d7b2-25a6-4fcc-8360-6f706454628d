// Main search functionality using Google Custom Search JSON API

class SearchEngine {
    constructor() {
        this.currentQuery = '';
        this.isSearching = false;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadSearchHistory();
        
        // Check if API is configured
        if (!Utils.checkApiConfiguration()) {
            console.log('API not configured, will use demo data as fallback');
        } else {
            console.log('Google Custom Search API configured and ready');
        }
    }
    
    bindEvents() {
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const luckyButton = document.getElementById('lucky-button');
        const searchIcon = document.getElementById('search-btn');
        
        if (searchInput) {
            // Handle Enter key
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch(searchInput.value.trim());
                }
            });
            
            // Handle input for autocomplete
            searchInput.addEventListener('input', Utils.debounce((e) => {
                const query = e.target.value.trim();
                if (query.length > 2) {
                    this.showSuggestions(query);
                } else {
                    this.hideSuggestions();
                }
            }, Utils.CONFIG.DEBOUNCE_DELAY));
            
            // Handle focus/blur for suggestions
            searchInput.addEventListener('focus', () => {
                if (searchInput.value.trim().length > 2) {
                    this.showSuggestions(searchInput.value.trim());
                }
            });
            
            searchInput.addEventListener('blur', () => {
                // Delay hiding to allow clicking on suggestions
                setTimeout(() => this.hideSuggestions(), 150);
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', () => {
                const query = searchInput?.value.trim() || '';
                if (query) {
                    this.performSearch(query);
                }
            });
        }
        
        if (luckyButton) {
            luckyButton.addEventListener('click', () => {
                const query = searchInput?.value.trim() || '';
                if (query) {
                    this.performLuckySearch(query);
                }
            });
        }
        
        if (searchIcon) {
            searchIcon.addEventListener('click', () => {
                const query = searchInput?.value.trim() || '';
                if (query) {
                    this.performSearch(query);
                }
            });
        }
    }
    
    async performSearch(query, start = 1, searchType = 'all') {
        if (!query || this.isSearching) return;
        
        this.isSearching = true;
        this.currentQuery = query;
        
        // Save to search history
        this.saveToSearchHistory(query);
        
        // Navigate to results page with query parameters
        const params = new URLSearchParams({
            q: query,
            start: start,
            type: searchType
        });
        
        window.location.href = `results.html?${params.toString()}`;
    }
    
    async performLuckySearch(query) {
        if (!query) return;
        
        try {
            const results = await this.searchGoogle(query, 1, 'web');
            if (results && results.items && results.items.length > 0) {
                // Open first result in current tab
                window.location.href = results.items[0].link;
            } else {
                // Fallback to regular search
                this.performSearch(query);
            }
        } catch (error) {
            console.error('Lucky search failed:', error);
            // Fallback to regular search
            this.performSearch(query);
        }
    }
    
    async searchGoogle(query, start = 1, searchType = 'all') {
        const cacheKey = `${query}-${start}-${searchType}`;

        // Check cache first
        const cached = Utils.searchCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        // Enhanced API configuration check
        const apiStatus = await Utils.verifyApiConfiguration();
        if (!apiStatus.configured || !apiStatus.working) {
            console.log('⚠️ API not available:', apiStatus.message);
            console.log('📝 Using demo data - configure API for real results');
            return this.getDemoResults(query, start, searchType);
        }

        console.log('✅ API verified and working:', apiStatus.message);

        try {
            // Build API URL
            const params = new URLSearchParams({
                key: Utils.CONFIG.GOOGLE_API_KEY,
                cx: Utils.CONFIG.SEARCH_ENGINE_ID,
                q: query,
                start: start,
                num: Utils.CONFIG.RESULTS_PER_PAGE
            });

            // Add advanced search type specific parameters for optimal results
            if (searchType === 'images') {
                params.append('searchType', 'image');
                params.append('imgSize', 'xlarge');
                params.append('imgType', 'photo');
                params.append('imgColorType', 'color');
                params.append('safe', 'active');
                params.append('rights', 'cc_publicdomain,cc_attribute,cc_sharealike,cc_noncommercial,cc_nonderived');
                params.append('fileType', 'jpg,png,gif,webp,svg');
                params.append('filter', '1');
                params.append('exactTerms', query.split(' ').slice(0, 3).join(' '));
                params.append('siteSearch', 'unsplash.com OR pixabay.com OR pexels.com OR wikimedia.org OR flickr.com');
            } else if (searchType === 'videos') {
                params.set('q', `${query} video tutorial watch lesson course documentary site:youtube.com OR site:vimeo.com OR site:dailymotion.com OR site:ted.com OR site:coursera.org`);
                params.append('dateRestrict', 'y2');
                params.append('filter', '1');
                params.append('excludeTerms', 'spam fake clickbait');
                params.append('lr', 'lang_en');
            } else if (searchType === 'news') {
                params.append('siteSearch', 'reuters.com OR bbc.com OR cnn.com OR apnews.com OR npr.org OR theguardian.com OR wsj.com OR nytimes.com OR washingtonpost.com OR bloomberg.com');
                params.append('sort', 'date');
                params.append('dateRestrict', 'd3');
                params.append('orTerms', 'news breaking latest update report today current events');
                params.append('filter', '1');
                params.append('excludeTerms', 'opinion editorial blog personal');
                params.append('lr', 'lang_en');
            } else if (searchType === 'shopping') {
                params.append('siteSearch', 'amazon.com OR ebay.com OR walmart.com OR target.com OR bestbuy.com OR etsy.com OR shopify.com OR costco.com OR homedepot.com');
                params.append('orTerms', 'buy price product store shop deal discount sale review rating customer');
                params.append('cr', 'countryUS');
                params.append('filter', '1');
                params.append('excludeTerms', 'fake counterfeit scam');
                params.append('dateRestrict', 'm6');
                params.append('lr', 'lang_en');
            } else if (searchType === 'books') {
                params.append('siteSearch', 'amazon.com OR goodreads.com OR books.google.com OR worldcat.org OR barnesandnoble.com OR openlibrary.org OR jstor.org OR scholar.google.com');
                params.append('orTerms', 'book author read library isbn publisher review summary academic textbook');
                params.append('fileType', 'pdf,epub,mobi,djvu');
                params.append('filter', '1');
                params.append('rights', 'cc_publicdomain,cc_attribute,cc_sharealike');
                params.append('excludeTerms', 'pirated illegal download torrent');
                params.append('lr', 'lang_en');
            }

            const url = `${Utils.CONFIG.BASE_URL}?${params.toString()}`;
            console.log('Making API request to:', url);

            const response = await fetch(url);

            if (!response.ok) {
                console.error('API request failed:', response.status, response.statusText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API response:', data);

            // Cache the results
            Utils.searchCache.set(cacheKey, data);

            return data;
        } catch (error) {
            console.error('Search API error, falling back to demo data:', error);
            return this.getDemoResults(query, start, searchType);
        }
    }

    getDemoResults(query, start = 1, searchType = 'all') {
        // Return demo data for testing
        return {
            searchInformation: {
                totalResults: "1234567",
                searchTime: 0.45
            },
            items: [
                {
                    title: `Demo Result 1 for "${query}"`,
                    link: "https://example.com/1",
                    snippet: "This is a demo search result. Configure your Google Custom Search API to see real results.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 2 for "${query}"`,
                    link: "https://example.com/2",
                    snippet: "Another demo result showing the search interface functionality.",
                    displayLink: "example.com"
                }
            ]
        };
    }
    
    async showSuggestions(query) {
        const suggestionsContainer = document.getElementById('suggestions');
        if (!suggestionsContainer) return;
        
        try {
            const suggestions = await this.getSuggestions(query);
            this.renderSuggestions(suggestions, suggestionsContainer);
        } catch (error) {
            console.error('Failed to get suggestions:', error);
            this.hideSuggestions();
        }
    }
    
    async getSuggestions(query) {
        const cacheKey = `suggestions-${query}`;
        
        // Check cache first
        const cached = Utils.suggestionCache.get(cacheKey);
        if (cached) {
            return cached;
        }
        
        // Use Google's suggestion API (JSONP)
        return new Promise((resolve) => {
            const script = document.createElement('script');
            const callbackName = `suggestions_${Date.now()}`;
            
            window[callbackName] = (data) => {
                const suggestions = data[1] || [];
                Utils.suggestionCache.set(cacheKey, suggestions);
                resolve(suggestions);
                document.head.removeChild(script);
                delete window[callbackName];
            };
            
            script.src = `https://suggestqueries.google.com/complete/search?client=firefox&q=${encodeURIComponent(query)}&callback=${callbackName}`;
            document.head.appendChild(script);
            
            // Timeout after 3 seconds
            setTimeout(() => {
                if (window[callbackName]) {
                    resolve([]);
                    document.head.removeChild(script);
                    delete window[callbackName];
                }
            }, 3000);
        });
    }
    
    renderSuggestions(suggestions, container) {
        if (!suggestions || suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }
        
        container.innerHTML = '';
        
        suggestions.slice(0, 8).forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = suggestion;
            item.setAttribute('data-index', index);
            
            item.addEventListener('click', () => {
                document.getElementById('search-input').value = suggestion;
                this.performSearch(suggestion);
            });
            
            container.appendChild(item);
        });
        
        container.style.display = 'block';
        container.setAttribute('aria-expanded', 'true');
    }
    
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
            suggestionsContainer.setAttribute('aria-expanded', 'false');
        }
    }
    
    saveToSearchHistory(query) {
        let history = Utils.getFromStorage('searchHistory') || [];
        
        // Remove if already exists
        history = history.filter(item => item !== query);
        
        // Add to beginning
        history.unshift(query);
        
        // Keep only last 20 searches
        history = history.slice(0, 20);
        
        Utils.saveToStorage('searchHistory', history);
    }
    
    loadSearchHistory() {
        const history = Utils.getFromStorage('searchHistory') || [];
        return history;
    }
    
    showApiConfigurationWarning() {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'api-warning';
        warningDiv.innerHTML = `
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px; border-radius: 8px; color: #856404;">
                <strong>⚠️ API Configuration Required</strong><br>
                To use real search results, please configure your Google Custom Search API:
                <ol style="margin: 10px 0; padding-left: 20px;">
                    <li>Get a Google API key from <a href="https://console.developers.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>Create a Custom Search Engine at <a href="https://cse.google.com/" target="_blank">Google CSE</a></li>
                    <li>Update the API_KEY and SEARCH_ENGINE_ID in js/utils.js</li>
                </ol>
                <small>Currently showing demo mode with limited functionality.</small>
            </div>
        `;
        
        const mainContainer = document.querySelector('.main-container') || document.body;
        mainContainer.insertBefore(warningDiv, mainContainer.firstChild);
    }
}

// Initialize search engine when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.searchEngine = new SearchEngine();
});

// Export for use in other modules
window.SearchEngine = SearchEngine;
