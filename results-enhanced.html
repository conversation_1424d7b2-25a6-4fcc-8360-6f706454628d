<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Search Results</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
    
    <!-- Critical CSS for results page - Google-exact styling -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: arial, sans-serif;
            background: #fff;
            color: #202124;
            line-height: 1.4;
            font-size: 14px;
        }
        
        /* Header Styling */
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid #dadce0;
            background: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: none;
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }
        
        .logo {
            text-decoration: none;
            margin-right: 10px;
        }
        
        .logo svg {
            width: 92px;
            height: 30px;
        }
        
        .search-container {
            flex: 1;
            max-width: 584px;
            position: relative;
        }
        
        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 45px 0 16px;
            font-size: 16px;
            outline: none;
            font-family: arial, sans-serif;
            transition: box-shadow 0.2s ease;
        }
        
        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }
        
        .search-icons {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-icon, .voice-icon, .camera-icon {
            width: 24px;
            height: 24px;
            padding: 8px;
            cursor: pointer;
            border-radius: 50%;
            transition: background-color 0.1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-icon:hover, .voice-icon:hover, .camera-icon:hover {
            background-color: rgba(60,64,67,.08);
        }
        
        .search-icon svg, .voice-icon svg, .camera-icon svg {
            width: 16px;
            height: 16px;
            fill: #9aa0a6;
        }
        
        .voice-icon svg {
            fill: #4285f4;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-left: auto;
        }
        
        .apps-menu {
            width: 24px;
            height: 24px;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.1s ease;
        }
        
        .apps-menu:hover {
            background-color: rgba(60,64,67,.08);
        }
        
        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }
        
        /* Navigation Tabs */
        .nav-tabs {
            background: white;
            padding: 0 20px;
        }
        
        .nav-content {
            max-width: none;
            display: flex;
            gap: 0;
            align-items: center;
        }
        
        .nav-tab {
            padding: 12px 16px;
            color: #5f6368;
            text-decoration: none;
            font-size: 13px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }
        
        .nav-tab.active {
            color: #1a73e8;
            border-bottom-color: #1a73e8;
        }
        
        .nav-tab:hover {
            color: #1a73e8;
        }
        
        .nav-tab svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        /* Main Content */
        .main-content {
            max-width: none;
            padding: 20px;
            margin-left: 150px;
        }
        
        .results-info {
            color: #70757a;
            font-size: 13px;
            margin-bottom: 20px;
            padding-left: 12px;
        }
        
        .result-item {
            margin-bottom: 28px;
            max-width: 600px;
            padding-left: 12px;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 1.3;
        }
        
        .result-url .favicon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f1f3f4;
        }
        
        .result-url .breadcrumb {
            color: #202124;
            font-size: 14px;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
            font-weight: 400;
            transition: text-decoration 0.1s ease;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-title:visited {
            color: #609;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }
        
        .result-snippet em {
            font-style: normal;
            font-weight: bold;
        }
        
        /* Loading */
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Pagination */
        .pagination {
            margin-top: 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 0;
            padding-left: 12px;
        }
        
        .pagination-nav {
            display: flex;
            align-items: center;
        }
        
        .page-btn {
            padding: 10px 16px;
            border: none;
            background: none;
            color: #1a73e8;
            cursor: pointer;
            font-size: 14px;
            font-family: arial, sans-serif;
            border-radius: 4px;
            transition: background-color 0.1s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .page-btn:hover {
            background: #f1f3f4;
        }
        
        .page-btn:disabled {
            color: #dadce0;
            cursor: not-allowed;
        }
        
        .page-number {
            padding: 10px 16px;
            color: #202124;
            font-size: 14px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.1s ease;
            text-decoration: none;
            min-width: 44px;
            text-align: center;
        }
        
        .page-number:hover {
            background: #f1f3f4;
        }
        
        .page-number.current {
            background: #1a73e8;
            color: white;
        }
        
        .google-logo-pagination {
            margin: 0 20px;
        }
        
        .google-logo-pagination svg {
            width: 66px;
            height: 22px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-wrap: wrap;
                gap: 15px;
            }
            
            .search-container {
                order: 3;
                width: 100%;
                max-width: none;
            }
            
            .nav-content {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0;
            }
            
            .main-content {
                padding: 15px;
                margin-left: 0;
            }
            
            .result-item {
                padding-left: 0;
            }
            
            .results-info {
                padding-left: 0;
            }
            
            .pagination {
                padding-left: 0;
            }
            
            .result-title {
                font-size: 18px;
            }
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 6px 15px 0 15px;
            }
            
            .nav-content {
                gap: 0;
            }
            
            .nav-tab {
                padding: 12px 12px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            <div class="search-container">
                <input 
                    type="text" 
                    class="search-box" 
                    id="search-input"
                    placeholder=""
                    autocomplete="off"
                    spellcheck="false"
                    maxlength="2048"
                    title="Search"
                >
                <div class="search-icons">
                    <div class="voice-icon" id="voice-search-btn" title="Search by voice">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                        </svg>
                    </div>
                    <div class="camera-icon" title="Search by image">
                        <svg viewBox="0 0 24 24">
                            <path d="M14,6H10L8,4H6A2,2 0 0,0 4,6V18A2,2 0 0,0 6,20H18A2,2 0 0,0 20,18V8A2,2 0 0,0 18,6H16L14,6M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
                        </svg>
                    </div>
                    <div class="search-icon" id="search-btn" title="Search">
                        <svg viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="apps-menu" title="Google apps">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
                    </svg>
                </div>
                <div class="profile-pic" title="Google Account">G</div>
            </div>
        </div>
    </header>

    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active" data-type="web">
                <svg viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                All
            </a>
            <a href="#" class="nav-tab" data-type="images">
                <svg viewBox="0 0 24 24">
                    <path d="M21,19V5c0,-1.1 -0.9,-2 -2,-2H5c-1.1,0 -2,0.9 -2,2v14c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2zM8.5,13.5l2.5,3.01L14.5,12l4.5,6H5l3.5,-4.5z"/>
                </svg>
                Images
            </a>
            <a href="#" class="nav-tab" data-type="videos">
                <svg viewBox="0 0 24 24">
                    <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                </svg>
                Videos
            </a>
            <a href="#" class="nav-tab" data-type="news">
                <svg viewBox="0 0 24 24">
                    <path d="M20,11H4V8H20M20,15H13V13H20M20,19H13V17H20M11,19H4V13H11M20.33,4.67L18.67,3L17,4.67L15.33,3L13.67,4.67L12,3L10.33,4.67L8.67,3L7,4.67L5.33,3L3.67,4.67L2,3V19A2,2 0 0,0 4,21H20A2,2 0 0,0 22,19V3L20.33,4.67Z"/>
                </svg>
                News
            </a>
            <a href="#" class="nav-tab" data-type="shopping">
                <svg viewBox="0 0 24 24">
                    <path d="M7,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2zM1,2v2h2l3.6,7.59 -1.35,2.45c-0.16,0.28 -0.25,0.61 -0.25,0.96 0,1.1 0.9,2 2,2h12v-2L7.42,15c-0.14,0 -0.25,-0.11 -0.25,-0.25l0.03,-0.12L8.1,13h7.45c0.75,0 1.41,-0.41 1.75,-1.03L21.7,4H5.21l-0.94,-2L1,2zM17,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2z"/>
                </svg>
                Shopping
            </a>
            <a href="#" class="nav-tab" data-type="books">
                <svg viewBox="0 0 24 24">
                    <path d="M18,2H6C4.9,2 4,2.9 4,4v16c0,1.1 0.9,2 2,2h12c1.1,0 2,-0.9 2,-2V4C20,2.9 19.1,2 18,2zM18,20H6V4h2v7l2.5,-1.5L13,11V4h5V20z"/>
                </svg>
                Books
            </a>
            <a href="#" class="nav-tab" data-type="flights">
                <svg viewBox="0 0 24 24">
                    <path d="M21,16v-2l-8,-5V3.5c0,-0.83 -0.67,-1.5 -1.5,-1.5S10,2.67 10,3.5V9l-8,5v2l8,-2.5V19l-2,1.5V22l3.5,-1 3.5,1v-1.5L13,19v-5.5L21,16z"/>
                </svg>
                Flights
            </a>
            <a href="#" class="nav-tab" data-type="finance">
                <svg viewBox="0 0 24 24">
                    <path d="M11.8,10.9c-2.27,-0.59 -3,-1.2 -3,-2.15 0,-1.09 1.01,-1.85 2.7,-1.85 1.78,0 2.44,0.85 2.5,2.1h2.21c-0.07,-1.72 -1.12,-3.3 -3.21,-3.81V3h-3v2.16c-1.94,0.42 -3.5,1.68 -3.5,3.61 0,2.31 1.91,3.46 4.7,4.13 2.5,0.6 3,1.48 3,2.41 0,0.69 -0.49,1.79 -2.7,1.79 -2.06,0 -2.87,-0.92 -2.98,-2.1h-2.2c0.12,2.19 1.76,3.42 3.68,3.83V21h3v-2.15c1.95,-0.37 3.5,-1.5 3.5,-3.55 0,-2.84 -2.43,-3.81 -4.7,-4.4z"/>
                </svg>
                Finance
            </a>
        </div>
    </nav>
    
    <main class="main-content">
        <div class="results-info" id="results-info"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        
        <div class="results-container" id="results-container"></div>
        
        <div class="pagination" id="pagination"></div>
    </main>
    
    <script src="js/utils.js"></script>
    <script src="js/results.js"></script>
    <script src="js/voice-search.js"></script>
    <script src="js/advanced-search.js"></script>
    <script src="js/search-tools.js"></script>
</body>
</html>
