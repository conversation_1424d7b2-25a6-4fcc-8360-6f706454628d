/**
 * Main Entry Point
 * Initializes the Google Clone application
 */

import app from './core/app.js';
import { APP_CONFIG } from './core/config/app.config.js';
import logger from './core/utils/logger.js';

// Set up global error handling
window.addEventListener('error', (event) => {
  logger.error('Global JavaScript error', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error
  });
});

window.addEventListener('unhandledrejection', (event) => {
  logger.error('Unhandled promise rejection', {
    reason: event.reason,
    promise: event.promise
  });
});

// Performance monitoring
if (APP_CONFIG.DEV.PERFORMANCE_MONITORING) {
  // Monitor Core Web Vitals
  import('./core/utils/performance.js').then(({ setupPerformanceMonitoring }) => {
    setupPerformanceMonitoring();
  });
}

// Development tools
if (APP_CONFIG.DEV.DEBUG) {
  // Expose app instance for debugging
  window.__GOOGLE_CLONE_APP__ = app;
  window.__APP_CONFIG__ = APP_CONFIG;
  
  // Add development helpers
  window.debugApp = () => {
    console.group('🔍 Google Clone Debug Info');
    console.log('App Instance:', app);
    console.log('Configuration:', APP_CONFIG);
    console.log('Components:', app.components);
    console.log('Current Page:', app.currentPage);
    console.log('Is Initialized:', app.isInitialized);
    console.groupEnd();
  };
  
  // Log initialization
  logger.info('Development mode enabled', {
    debug: APP_CONFIG.DEV.DEBUG,
    mockApi: APP_CONFIG.DEV.MOCK_API,
    performanceMonitoring: APP_CONFIG.DEV.PERFORMANCE_MONITORING
  });
}

// Initialize application
app.init().catch((error) => {
  logger.error('Failed to initialize application', error);
  
  // Show fallback error message
  document.body.innerHTML = `
    <div style="
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      font-family: arial, sans-serif;
      text-align: center;
      padding: 20px;
    ">
      <div>
        <h1 style="color: #ea4335; margin-bottom: 20px;">
          Oops! Something went wrong
        </h1>
        <p style="color: #5f6368; margin-bottom: 20px;">
          We're having trouble loading the application. Please try refreshing the page.
        </p>
        <button 
          onclick="window.location.reload()" 
          style="
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
        >
          Refresh Page
        </button>
      </div>
    </div>
  `;
});

export default app;
