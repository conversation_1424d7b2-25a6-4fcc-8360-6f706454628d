<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #3367d6;
        }
        .config-info {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        h1 {
            color: #1a73e8;
            text-align: center;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #4285f4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Google Custom Search API Test</h1>
        
        <div id="config-display" class="config-info">
            Loading configuration...
        </div>
        
        <button class="btn" onclick="testApiConnection()">Test API Connection</button>
        <button class="btn" onclick="testSearchTypes()">Test All Search Types</button>
        <button class="btn" onclick="window.open('results.html', '_blank')">Open Search App</button>
        
        <div id="test-results"></div>
    </div>

    <script src="js/utils.js"></script>
    <script>
        // Display current configuration
        function displayConfig() {
            const configDiv = document.getElementById('config-display');
            if (window.Utils && window.Utils.CONFIG) {
                const config = window.Utils.CONFIG;
                configDiv.innerHTML = `
                    <strong>Current API Configuration:</strong><br>
                    API Key: ${config.GOOGLE_API_KEY ? config.GOOGLE_API_KEY.substring(0, 20) + '...' : 'Not set'}<br>
                    Search Engine ID: ${config.SEARCH_ENGINE_ID || 'Not set'}<br>
                    Base URL: ${config.BASE_URL || 'Not set'}
                `;
            } else {
                configDiv.innerHTML = '<strong>❌ Utils.js not loaded or CONFIG not available</strong>';
            }
        }

        // Test API connection
        async function testApiConnection() {
            const resultsDiv = document.getElementById('test-results');
            showStatus('🔍 Testing API connection...', 'info');
            
            try {
                if (!window.Utils || !window.Utils.verifyApiConfiguration) {
                    throw new Error('Utils.js not loaded or verifyApiConfiguration function not available');
                }
                
                const result = await window.Utils.verifyApiConfiguration();
                
                if (result.working) {
                    showStatus(`✅ API Connection Successful!<br>
                        Status: ${result.message}<br>
                        Details: ${JSON.stringify(result.details, null, 2)}`, 'success');
                } else {
                    showStatus(`❌ API Connection Failed<br>
                        Status: ${result.message}<br>
                        Configured: ${result.configured}`, 'error');
                }
                
            } catch (error) {
                showStatus(`❌ Test Error: ${error.message}`, 'error');
            }
        }

        // Test different search types
        async function testSearchTypes() {
            const resultsDiv = document.getElementById('test-results');
            showStatus('🔍 Testing different search types...', 'info');
            
            const searchTypes = ['web', 'images', 'videos', 'news', 'shopping', 'books'];
            const testQuery = 'artificial intelligence';
            
            let html = '<div class="test-results"><h3>Search Type Tests:</h3>';
            
            for (const type of searchTypes) {
                try {
                    const result = await testSearchType(testQuery, type);
                    html += `
                        <div class="result-item">
                            <strong>${type.toUpperCase()}</strong>: 
                            ${result.success ? 
                                `✅ ${result.totalResults} results in ${result.searchTime}s` : 
                                `❌ ${result.error}`}
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="result-item">
                            <strong>${type.toUpperCase()}</strong>: ❌ Error: ${error.message}
                        </div>
                    `;
                }
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        // Test individual search type
        async function testSearchType(query, searchType) {
            if (!window.Utils || !window.Utils.CONFIG) {
                throw new Error('Utils.js not loaded');
            }
            
            const config = window.Utils.CONFIG;
            const params = new URLSearchParams({
                key: config.GOOGLE_API_KEY,
                cx: config.SEARCH_ENGINE_ID,
                q: query,
                num: 1
            });
            
            // Add search type specific parameters
            if (searchType === 'images') {
                params.append('searchType', 'image');
            } else if (searchType === 'videos') {
                params.append('siteSearch', 'youtube.com OR vimeo.com');
            } else if (searchType === 'news') {
                params.append('siteSearch', 'reuters.com OR bbc.com');
            } else if (searchType === 'shopping') {
                params.append('siteSearch', 'amazon.com OR ebay.com');
            } else if (searchType === 'books') {
                params.append('siteSearch', 'amazon.com OR goodreads.com');
            }
            
            const url = `${config.BASE_URL}?${params.toString()}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`${response.status}: ${errorData.error?.message || 'Unknown error'}`);
            }
            
            const data = await response.json();
            return {
                success: true,
                totalResults: data.searchInformation?.totalResults || '0',
                searchTime: data.searchInformation?.searchTime || '0',
                itemCount: data.items?.length || 0
            };
        }

        // Show status message
        function showStatus(message, type) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            displayConfig();
            
            // Auto-test API connection
            setTimeout(() => {
                testApiConnection();
            }, 1000);
        });
    </script>
</body>
</html>
