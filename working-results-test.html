<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working Results Test</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: arial, sans-serif; background: #fff; color: #202124; }
        
        .header { padding: 6px 20px 0 20px; border-bottom: 1px solid #dadce0; }
        .header-content { display: flex; align-items: center; gap: 30px; min-height: 58px; }
        .logo { text-decoration: none; margin-right: 10px; }
        .search-container { flex: 1; max-width: 584px; position: relative; }
        .search-box { width: 100%; height: 44px; border: 1px solid #dfe1e5; border-radius: 24px; padding: 0 45px 0 16px; font-size: 16px; outline: none; }
        
        .nav-tabs { background: white; padding: 0 20px; }
        .nav-content { display: flex; gap: 0; align-items: center; }
        .nav-tab { padding: 12px 16px; color: #5f6368; text-decoration: none; font-size: 13px; border-bottom: 3px solid transparent; display: flex; align-items: center; gap: 6px; }
        .nav-tab.active { color: #1a73e8; border-bottom-color: #1a73e8; }
        .nav-tab:hover { color: #1a73e8; }
        
        .main-content { max-width: none; padding: 20px; margin-left: 150px; }
        .results-info { color: #70757a; font-size: 13px; margin-bottom: 20px; padding-left: 12px; }
        .loading { text-align: center; padding: 40px; color: #70757a; }
        .spinner { border: 2px solid #f3f3f3; border-top: 2px solid #4285f4; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        
        .result-item { margin-bottom: 28px; max-width: 600px; padding-left: 12px; }
        .result-url { color: #202124; font-size: 14px; margin-bottom: 3px; display: flex; align-items: center; gap: 8px; }
        .result-title { color: #1a0dab; font-size: 20px; text-decoration: none; display: block; margin-bottom: 3px; }
        .result-title:hover { text-decoration: underline; }
        .result-snippet { color: #4d5156; font-size: 14px; line-height: 1.58; }
        
        .result-type-badge { display: inline-block; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; text-transform: uppercase; margin-left: 8px; color: white; }
        .result-type-web { background-color: #4285f4; }
        .result-type-images { background-color: #ea4335; }
        .result-type-videos { background-color: #ff6d01; }
        .result-type-news { background-color: #34a853; }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <span style="color: #4285f4; font-size: 24px; font-weight: bold;">Google</span>
            </a>
            <div class="search-container">
                <input type="text" class="search-box" id="search-input" placeholder="" autocomplete="off">
            </div>
        </div>
    </header>

    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active" data-type="all">All</a>
            <a href="#" class="nav-tab" data-type="web">Web</a>
            <a href="#" class="nav-tab" data-type="images">Images</a>
            <a href="#" class="nav-tab" data-type="videos">Videos</a>
            <a href="#" class="nav-tab" data-type="news">News</a>
        </div>
    </nav>
    
    <main class="main-content">
        <div class="results-info" id="results-info"></div>
        <div class="loading" id="loading"><div class="spinner"></div><div>Searching...</div></div>
        <div class="results-container" id="results-container"></div>
    </main>
    
    <script>
        console.log('🔍 Working Results Test page loaded');
        
        class WorkingSearchApp {
            constructor() {
                this.currentQuery = '';
                this.currentPage = 1;
                this.currentType = 'all';
                this.init();
            }

            init() {
                console.log('🔍 WorkingSearchApp initializing...');
                this.setupEventListeners();
                this.loadInitialResults();
            }

            setupEventListeners() {
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchTab(tab.dataset.type);
                    });
                });
            }

            loadInitialResults() {
                const urlParams = new URLSearchParams(window.location.search);
                const query = urlParams.get('q');
                
                if (query) {
                    this.currentQuery = query;
                    this.currentType = urlParams.get('type') || 'all';
                    document.getElementById('search-input').value = query;
                    this.updateActiveTab();
                    this.displayResults();
                } else {
                    this.hideLoading();
                }
            }

            switchTab(type) {
                if (!this.currentQuery) return;
                this.currentType = type;
                this.updateActiveTab();
                this.displayResults();
            }

            updateActiveTab() {
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === this.currentType);
                });
            }

            async displayResults() {
                this.showLoading();
                
                try {
                    // Always use demo results for this test
                    const demoResults = this.getDemoResults(this.currentQuery, this.currentPage, this.currentType);
                    this.renderResults(demoResults, false);
                    this.hideLoading();
                } catch (error) {
                    console.error('Error displaying results:', error);
                    this.hideLoading();
                }
            }

            getDemoResults(query, start = 1, searchType = 'all') {
                const results = [];
                const startNum = (start - 1) * 10 + 1;

                if (searchType === 'all') {
                    const resultTypes = ['web', 'images', 'news', 'videos'];
                    for (let i = 0; i < 12; i++) {
                        const type = resultTypes[i % resultTypes.length];
                        results.push({
                            title: `[${type.toUpperCase()}] ${query} - Working Demo Result ${startNum + i}`,
                            link: `https://example.com/demo-${type}-result-${startNum + i}`,
                            snippet: `This is a working demo ${type} search result for "${query}". The search functionality is now working correctly!`,
                            displayLink: 'example.com',
                            resultType: type
                        });
                    }
                } else {
                    for (let i = 0; i < 10; i++) {
                        results.push({
                            title: `${query} - Working Demo ${searchType} Result ${startNum + i}`,
                            link: `https://example.com/demo-${searchType}-result-${startNum + i}`,
                            snippet: `This is a working demo ${searchType} search result for "${query}". The search functionality is now working correctly!`,
                            displayLink: 'example.com',
                            resultType: searchType
                        });
                    }
                }

                return {
                    searchInformation: { totalResults: "1234567", searchTime: 0.45 },
                    items: results
                };
            }

            renderResults(data, isRealAPI = false) {
                const resultsInfo = document.getElementById('results-info');
                const resultsContainer = document.getElementById('results-container');

                if (!data || !data.items || data.items.length === 0) {
                    resultsContainer.innerHTML = '<div style="text-align: center; padding: 40px;">No results found</div>';
                    return;
                }

                const totalResults = data.searchInformation?.totalResults || '0';
                const searchTime = data.searchInformation?.searchTime || 0;
                const sourceText = isRealAPI ? '(Real Google API)' : '(Working Demo Data)';

                resultsInfo.innerHTML = `About ${parseInt(totalResults).toLocaleString()} results (${searchTime} seconds) <span style="color: #34a853; font-weight: bold;">${sourceText}</span>`;

                resultsContainer.innerHTML = '';

                data.items.forEach((item, index) => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item';

                    const resultTypeBadge = this.currentType === 'all' && item.resultType ? 
                        `<span class="result-type-badge result-type-${item.resultType}">${item.resultType.toUpperCase()}</span>` : '';

                    resultDiv.innerHTML = `
                        <div class="result-url">
                            <span>${item.displayLink || 'example.com'}</span>
                            ${resultTypeBadge}
                        </div>
                        <a href="${item.link}" class="result-title" target="_blank">${item.title}</a>
                        <div class="result-snippet">${item.snippet}</div>
                    `;

                    resultsContainer.appendChild(resultDiv);
                });
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results-container').style.display = 'none';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results-container').style.display = 'block';
            }
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔍 DOM loaded, initializing WorkingSearchApp');
            window.workingSearchApp = new WorkingSearchApp();
        });

        // Also initialize immediately if DOM is already loaded
        if (document.readyState !== 'loading') {
            console.log('🔍 DOM already loaded, initializing WorkingSearchApp immediately');
            window.workingSearchApp = new WorkingSearchApp();
        }
    </script>
</body>
</html>
