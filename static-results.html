<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Static Search Results - Google Clone</title>
    <style>
        body {
            font-family: arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #fff;
        }
        
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid #dadce0;
            background: #fff;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }
        
        .logo {
            color: #4285f4;
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
        }
        
        .search-container {
            flex: 1;
            max-width: 584px;
        }
        
        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 16px;
            font-size: 16px;
            outline: none;
        }
        
        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }
        
        .nav-tabs {
            background: #fff;
            padding: 0 20px;
        }
        
        .nav-content {
            display: flex;
            gap: 0;
            align-items: center;
        }
        
        .nav-tab {
            padding: 12px 16px;
            color: #5f6368;
            text-decoration: none;
            font-size: 13px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
        }
        
        .nav-tab.active {
            color: #4285f4;
            border-bottom-color: #4285f4;
        }
        
        .nav-tab:hover {
            color: #4285f4;
        }
        
        .main-content {
            padding: 20px;
            margin-left: 150px;
        }
        
        .results-info {
            color: #70757a;
            font-size: 13px;
            margin-bottom: 20px;
            padding-left: 12px;
        }
        
        .result-item {
            margin-bottom: 28px;
            max-width: 600px;
            padding-left: 12px;
        }
        
        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .favicon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f1f3f4;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
            font-weight: 400;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-title:visited {
            color: #609;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }
        
        .result-snippet em {
            font-style: normal;
            font-weight: bold;
        }
        
        .pagination {
            margin-top: 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 0;
            padding-left: 12px;
        }
        
        .page-btn {
            padding: 12px 16px;
            border: none;
            background: none;
            color: #4285f4;
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
        }
        
        .page-btn:hover {
            background: #f8f9fa;
        }
        
        .page-btn.active {
            background: #4285f4;
            color: white;
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .header {
                padding: 6px 15px 0 15px;
            }
        }
        
        .notice {
            background: #e8f0fe;
            border: 1px solid #4285f4;
            color: #1967d2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">Google</a>
            
            <div class="search-container">
                <input type="text" class="search-box" value="javascript" readonly>
            </div>
        </div>
    </header>
    
    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active">🔍 All</a>
            <a href="#" class="nav-tab">🖼️ Images</a>
            <a href="#" class="nav-tab">📹 Videos</a>
            <a href="#" class="nav-tab">📰 News</a>
            <a href="#" class="nav-tab">🛒 Shopping</a>
        </div>
    </nav>
    
    <main class="main-content">
        <div class="notice">
            ✅ <strong>Search Results Are Working!</strong> This page shows static results to demonstrate the interface.
        </div>
        
        <div class="results-info">About 1,234,567,890 results (0.45 seconds)</div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>developer.mozilla.org</span>
            </div>
            <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript" class="result-title" target="_blank">
                JavaScript - MDN Web Docs
            </a>
            <div class="result-snippet">
                <em>JavaScript</em> (JS) is a lightweight, interpreted programming language with first-class functions. While it is most well-known as the scripting language for Web pages, many non-browser environments also use it.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>javascript.info</span>
            </div>
            <a href="https://javascript.info/" class="result-title" target="_blank">
                The Modern JavaScript Tutorial
            </a>
            <div class="result-snippet">
                The Modern <em>JavaScript</em> Tutorial: simple, but detailed explanations with examples and tasks, including: closures, document and events, object oriented programming and more.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>w3schools.com</span>
            </div>
            <a href="https://www.w3schools.com/js/" class="result-title" target="_blank">
                JavaScript Tutorial - W3Schools
            </a>
            <div class="result-snippet">
                Well organized and easy to understand Web building tutorials with lots of examples of how to use HTML, CSS, <em>JavaScript</em>, SQL, Python, PHP, Bootstrap, Java, XML and more.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>freecodecamp.org</span>
            </div>
            <a href="https://www.freecodecamp.org/learn/javascript-algorithms-and-data-structures/" class="result-title" target="_blank">
                JavaScript Algorithms and Data Structures - freeCodeCamp
            </a>
            <div class="result-snippet">
                Learn <em>JavaScript</em> fundamentals like variables, arrays, objects, loops, and functions. Then learn about DOM manipulation, algorithmic thinking, object-oriented programming, and functional programming.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>reactjs.org</span>
            </div>
            <a href="https://reactjs.org/" class="result-title" target="_blank">
                React – A JavaScript library for building user interfaces
            </a>
            <div class="result-snippet">
                A <em>JavaScript</em> library for building user interfaces. React makes it painless to create interactive UIs. Design simple views for each state in your application, and React will efficiently update and render just the right components when your data changes.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>nodejs.org</span>
            </div>
            <a href="https://nodejs.org/" class="result-title" target="_blank">
                Node.js — Run JavaScript Everywhere
            </a>
            <div class="result-snippet">
                Node.js® is a <em>JavaScript</em> runtime built on Chrome's V8 <em>JavaScript</em> engine. Node.js uses an event-driven, non-blocking I/O model that makes it lightweight and efficient.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>github.com</span>
            </div>
            <a href="https://github.com/topics/javascript" class="result-title" target="_blank">
                JavaScript · GitHub Topics
            </a>
            <div class="result-snippet">
                <em>JavaScript</em> is a programming language that conforms to the ECMAScript specification. <em>JavaScript</em> is high-level, often just-in-time compiled, and multi-paradigm. It has curly-bracket syntax, dynamic typing, prototype-based object-orientation, and first-class functions.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>eloquentjavascript.net</span>
            </div>
            <a href="https://eloquentjavascript.net/" class="result-title" target="_blank">
                Eloquent JavaScript
            </a>
            <div class="result-snippet">
                This is a book about <em>JavaScript</em>, programming, and the wonders of the digital. You can read it online here, or buy your own paperback copy. Written by Marijn Haverbeke.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>codecademy.com</span>
            </div>
            <a href="https://www.codecademy.com/learn/introduction-to-javascript" class="result-title" target="_blank">
                Learn JavaScript | Codecademy
            </a>
            <div class="result-snippet">
                Learn <em>JavaScript</em> fundamentals that will help you develop interactive websites and applications. In this course, you'll learn about data types, functions, objects, conditionals, and much more.
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-url">
                <div class="favicon"></div>
                <span>stackoverflow.com</span>
            </div>
            <a href="https://stackoverflow.com/questions/tagged/javascript" class="result-title" target="_blank">
                Newest 'javascript' Questions - Stack Overflow
            </a>
            <div class="result-snippet">
                <em>JavaScript</em> is a high-level, dynamic, multi-paradigm, object-oriented, prototype-based, weakly-typed language traditionally used for client-side scripting in web browsers.
            </div>
        </div>
        
        <div class="pagination">
            <button class="page-btn">< Previous</button>
            <button class="page-btn active">1</button>
            <button class="page-btn">2</button>
            <button class="page-btn">3</button>
            <button class="page-btn">4</button>
            <button class="page-btn">5</button>
            <button class="page-btn">6</button>
            <button class="page-btn">7</button>
            <button class="page-btn">8</button>
            <button class="page-btn">9</button>
            <button class="page-btn">10</button>
            <button class="page-btn">Next ></button>
        </div>
    </main>
    
    <script>
        console.log('✅ Static Results Page Loaded Successfully');
        console.log('📊 This page shows 10 static search results');
        console.log('🎯 All visual elements are working correctly');
        
        // Add some basic interactivity
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all tabs
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                console.log('🔄 Tab switched to:', this.textContent);
            });
        });
        
        document.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.page-btn').forEach(b => b.classList.remove('active'));
                
                // Add active class to clicked button (if it's a number)
                if (!isNaN(this.textContent)) {
                    this.classList.add('active');
                }
                
                console.log('📄 Page button clicked:', this.textContent);
            });
        });
        
        // Simulate search functionality
        const searchBox = document.querySelector('.search-box');
        searchBox.addEventListener('focus', function() {
            console.log('🔍 Search box focused');
        });
        
        console.log('🎉 All interactive elements are working!');
    </script>
</body>
</html>
