// Production Deployment Configuration
// This file contains all the configuration needed for production deployment

const DEPLOYMENT_CONFIG = {
    // Environment Configuration
    ENVIRONMENT: {
        NODE_ENV: 'production',
        API_ENDPOINT: 'https://api.yourdomain.com',
        CDN_URL: 'https://cdn.yourdomain.com',
        DOMAIN: 'search.yourdomain.com',
        HTTPS_ONLY: true
    },

    // Performance Optimization
    PERFORMANCE: {
        ENABLE_COMPRESSION: true,
        ENABLE_CACHING: true,
        CACHE_DURATION: 86400, // 24 hours
        MINIFY_ASSETS: true,
        OPTIMIZE_IMAGES: true,
        LAZY_LOAD_IMAGES: true,
        PRELOAD_CRITICAL_RESOURCES: true
    },

    // Security Configuration
    SECURITY: {
        CONTENT_SECURITY_POLICY: {
            "default-src": "'self'",
            "script-src": "'self' 'unsafe-inline' https://www.googleapis.com",
            "style-src": "'self' 'unsafe-inline'",
            "img-src": "'self' data: https:",
            "connect-src": "'self' https://www.googleapis.com https://customsearch.googleapis.com",
            "font-src": "'self'",
            "object-src": "'none'",
            "base-uri": "'self'",
            "form-action": "'self'"
        },
        HSTS_MAX_AGE: 31536000,
        X_FRAME_OPTIONS: 'DENY',
        X_CONTENT_TYPE_OPTIONS: 'nosniff',
        REFERRER_POLICY: 'strict-origin-when-cross-origin'
    },

    // Analytics Configuration
    ANALYTICS: {
        GOOGLE_ANALYTICS_ID: 'GA_MEASUREMENT_ID',
        ENABLE_PERFORMANCE_MONITORING: true,
        ENABLE_ERROR_TRACKING: true,
        SAMPLE_RATE: 100, // Percentage of sessions to track
        PRIVACY_MODE: true
    },

    // API Configuration
    API: {
        GOOGLE_CUSTOM_SEARCH: {
            API_KEY: process.env.GOOGLE_API_KEY || 'YOUR_API_KEY',
            SEARCH_ENGINE_ID: process.env.SEARCH_ENGINE_ID || 'YOUR_SEARCH_ENGINE_ID',
            RATE_LIMIT: 100, // requests per day
            TIMEOUT: 8000, // milliseconds
            RETRY_ATTEMPTS: 3
        }
    },

    // PWA Configuration
    PWA: {
        ENABLE_SERVICE_WORKER: true,
        CACHE_STRATEGY: 'cache-first',
        OFFLINE_FALLBACK: true,
        BACKGROUND_SYNC: true,
        PUSH_NOTIFICATIONS: false // Enable when ready
    },

    // Build Configuration
    BUILD: {
        OUTPUT_DIR: 'dist',
        ASSET_DIR: 'assets',
        BUNDLE_ANALYZER: false,
        SOURCE_MAPS: false,
        TREE_SHAKING: true,
        CODE_SPLITTING: true
    },

    // Monitoring Configuration
    MONITORING: {
        HEALTH_CHECK_ENDPOINT: '/health',
        METRICS_ENDPOINT: '/metrics',
        LOG_LEVEL: 'info',
        ERROR_REPORTING: true,
        UPTIME_MONITORING: true
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DEPLOYMENT_CONFIG;
} else {
    window.DEPLOYMENT_CONFIG = DEPLOYMENT_CONFIG;
}
