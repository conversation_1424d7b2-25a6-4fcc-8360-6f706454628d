<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .test-btn {
            background: #4285f4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .results {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #4285f4;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>🔧 Search Engine Debug Page</h1>
    
    <div class="debug-section">
        <h3>1. Script Loading Test</h3>
        <div id="script-status">Testing...</div>
    </div>
    
    <div class="debug-section">
        <h3>2. API Configuration Test</h3>
        <div id="api-status">Testing...</div>
    </div>
    
    <div class="debug-section">
        <h3>3. Search Function Test</h3>
        <button class="test-btn" onclick="testSearch()">Test Search Function</button>
        <div id="search-status"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. Demo Results Test</h3>
        <button class="test-btn" onclick="testDemoResults()">Test Demo Results</button>
        <div id="demo-status"></div>
    </div>
    
    <div class="debug-section">
        <h3>5. Manual Search Test</h3>
        <input type="text" id="manual-query" placeholder="Enter search query" value="javascript">
        <button class="test-btn" onclick="manualSearch()">Search</button>
        <div id="manual-results"></div>
    </div>
    
    <div class="debug-section">
        <h3>6. Navigation Test</h3>
        <button class="test-btn" onclick="goToMain()">Go to Main Page</button>
        <button class="test-btn" onclick="goToResults()">Go to Results Page</button>
    </div>
    
    <script src="js/utils.js"></script>
    <script src="js/search.js"></script>
    <script src="js/results.js"></script>
    
    <script>
        // Test script loading
        function testScriptLoading() {
            const status = document.getElementById('script-status');
            let html = '';
            
            if (typeof Utils !== 'undefined') {
                html += '<div class="success">✅ Utils.js loaded successfully</div>';
            } else {
                html += '<div class="error">❌ Utils.js failed to load</div>';
            }
            
            if (typeof SearchEngine !== 'undefined') {
                html += '<div class="success">✅ SearchEngine class loaded</div>';
            } else {
                html += '<div class="error">❌ SearchEngine class not found</div>';
            }
            
            if (typeof ResultsPage !== 'undefined') {
                html += '<div class="success">✅ ResultsPage class loaded</div>';
            } else {
                html += '<div class="error">❌ ResultsPage class not found</div>';
            }
            
            status.innerHTML = html;
        }
        
        // Test API configuration
        function testApiConfiguration() {
            const status = document.getElementById('api-status');
            
            if (typeof Utils !== 'undefined' && Utils.checkApiConfiguration) {
                const isConfigured = Utils.checkApiConfiguration();
                if (isConfigured) {
                    status.innerHTML = '<div class="success">✅ API configured (real results mode)</div>';
                } else {
                    status.innerHTML = '<div class="success">✅ Demo mode enabled (showing sample results)</div>';
                }
            } else {
                status.innerHTML = '<div class="error">❌ Utils.checkApiConfiguration not available</div>';
            }
        }
        
        // Test search function
        async function testSearch() {
            const status = document.getElementById('search-status');
            status.innerHTML = '<div>🔍 Testing search function...</div>';
            
            try {
                if (typeof SearchEngine !== 'undefined') {
                    const searchEngine = new SearchEngine();
                    if (searchEngine.searchGoogle) {
                        const results = await searchEngine.searchGoogle('test', 1, 'web');
                        if (results && results.items) {
                            status.innerHTML = `<div class="success">✅ Search function working! Found ${results.items.length} results</div>`;
                        } else {
                            status.innerHTML = '<div class="error">❌ Search returned no results</div>';
                        }
                    } else {
                        status.innerHTML = '<div class="error">❌ searchGoogle method not found</div>';
                    }
                } else {
                    status.innerHTML = '<div class="error">❌ SearchEngine class not available</div>';
                }
            } catch (error) {
                status.innerHTML = `<div class="error">❌ Search error: ${error.message}</div>`;
            }
        }
        
        // Test demo results
        async function testDemoResults() {
            const status = document.getElementById('demo-status');
            status.innerHTML = '<div>🎭 Testing demo results...</div>';
            
            try {
                if (typeof SearchEngine !== 'undefined') {
                    const searchEngine = new SearchEngine();
                    if (searchEngine.getDemoResults) {
                        const results = searchEngine.getDemoResults('test query');
                        if (results && results.items) {
                            let html = `<div class="success">✅ Demo results working! ${results.items.length} items</div>`;
                            html += '<div class="results">';
                            results.items.forEach((item, index) => {
                                html += `<div class="result-item">
                                    <strong>${item.title}</strong><br>
                                    <small>${item.displayLink}</small><br>
                                    ${item.snippet}
                                </div>`;
                            });
                            html += '</div>';
                            status.innerHTML = html;
                        } else {
                            status.innerHTML = '<div class="error">❌ Demo results returned no items</div>';
                        }
                    } else {
                        status.innerHTML = '<div class="error">❌ getDemoResults method not found</div>';
                    }
                } else {
                    status.innerHTML = '<div class="error">❌ SearchEngine class not available</div>';
                }
            } catch (error) {
                status.innerHTML = `<div class="error">❌ Demo results error: ${error.message}</div>`;
            }
        }
        
        // Manual search test
        async function manualSearch() {
            const query = document.getElementById('manual-query').value.trim();
            const results = document.getElementById('manual-results');
            
            if (!query) {
                results.innerHTML = '<div class="error">❌ Please enter a search query</div>';
                return;
            }
            
            results.innerHTML = '<div>🔍 Searching...</div>';
            
            try {
                if (typeof SearchEngine !== 'undefined') {
                    const searchEngine = new SearchEngine();
                    const searchResults = await searchEngine.searchGoogle(query, 1, 'web');
                    
                    if (searchResults && searchResults.items) {
                        let html = `<div class="success">✅ Found ${searchResults.items.length} results for "${query}"</div>`;
                        html += '<div class="results">';
                        searchResults.items.forEach((item, index) => {
                            html += `<div class="result-item">
                                <strong>${item.title}</strong><br>
                                <small>${item.displayLink || item.link}</small><br>
                                ${item.snippet}
                            </div>`;
                        });
                        html += '</div>';
                        results.innerHTML = html;
                    } else {
                        results.innerHTML = '<div class="error">❌ No results found</div>';
                    }
                } else {
                    results.innerHTML = '<div class="error">❌ SearchEngine not available</div>';
                }
            } catch (error) {
                results.innerHTML = `<div class="error">❌ Search failed: ${error.message}</div>`;
            }
        }
        
        // Navigation functions
        function goToMain() {
            window.location.href = '/';
        }
        
        function goToResults() {
            window.location.href = '/results.html?q=test&start=1&type=web';
        }
        
        // Run tests on page load
        window.addEventListener('load', () => {
            testScriptLoading();
            testApiConfiguration();
        });
    </script>
</body>
</html>
