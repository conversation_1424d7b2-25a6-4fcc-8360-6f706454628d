/**
 * DOM Utilities
 * Enhanced DOM manipulation and query utilities
 */

import logger from './logger.js';

/**
 * Enhanced DOM query with caching and error handling
 */
class DOMQuery {
  constructor() {
    this.cache = new Map();
    this.logger = logger.child('DOM');
  }
  
  /**
   * Get element by ID with caching
   * @param {string} id - Element ID
   * @param {boolean} useCache - Whether to use cache
   * @returns {HTMLElement|null} Element or null
   */
  getElementById(id, useCache = true) {
    if (useCache && this.cache.has(id)) {
      return this.cache.get(id);
    }
    
    const element = document.getElementById(id);
    
    if (!element) {
      this.logger.warn(`Element with ID '${id}' not found`);
    } else if (useCache) {
      this.cache.set(id, element);
    }
    
    return element;
  }
  
  /**
   * Query selector with error handling
   * @param {string} selector - CSS selector
   * @param {HTMLElement} context - Context element
   * @returns {HTMLElement|null} Element or null
   */
  querySelector(selector, context = document) {
    try {
      return context.querySelector(selector);
    } catch (error) {
      this.logger.error(`Invalid selector: ${selector}`, error);
      return null;
    }
  }
  
  /**
   * Query all elements with error handling
   * @param {string} selector - CSS selector
   * @param {HTMLElement} context - Context element
   * @returns {NodeList} NodeList of elements
   */
  querySelectorAll(selector, context = document) {
    try {
      return context.querySelectorAll(selector);
    } catch (error) {
      this.logger.error(`Invalid selector: ${selector}`, error);
      return document.createDocumentFragment().childNodes;
    }
  }
  
  /**
   * Create element with attributes and content
   * @param {string} tagName - Tag name
   * @param {Object} attributes - Element attributes
   * @param {string|HTMLElement|Array} content - Element content
   * @returns {HTMLElement} Created element
   */
  createElement(tagName, attributes = {}, content = null) {
    const element = document.createElement(tagName);
    
    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'dataset') {
        Object.entries(value).forEach(([dataKey, dataValue]) => {
          element.dataset[dataKey] = dataValue;
        });
      } else if (key.startsWith('on') && typeof value === 'function') {
        element.addEventListener(key.slice(2).toLowerCase(), value);
      } else {
        element.setAttribute(key, value);
      }
    });
    
    // Set content
    if (content !== null) {
      if (typeof content === 'string') {
        element.textContent = content;
      } else if (content instanceof HTMLElement) {
        element.appendChild(content);
      } else if (Array.isArray(content)) {
        content.forEach(child => {
          if (typeof child === 'string') {
            element.appendChild(document.createTextNode(child));
          } else if (child instanceof HTMLElement) {
            element.appendChild(child);
          }
        });
      }
    }
    
    return element;
  }
  
  /**
   * Add event listener with automatic cleanup
   * @param {HTMLElement} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   * @param {Object} options - Event options
   * @returns {Function} Cleanup function
   */
  addEventListener(element, event, handler, options = {}) {
    if (!element || typeof handler !== 'function') {
      this.logger.warn('Invalid element or handler for event listener');
      return () => {};
    }
    
    element.addEventListener(event, handler, options);
    
    // Return cleanup function
    return () => {
      element.removeEventListener(event, handler, options);
    };
  }
  
  /**
   * Add class with animation support
   * @param {HTMLElement} element - Target element
   * @param {string} className - Class name to add
   * @param {boolean} animate - Whether to animate
   */
  addClass(element, className, animate = false) {
    if (!element) return;
    
    if (animate) {
      element.style.transition = 'all 0.3s ease';
    }
    
    element.classList.add(className);
  }
  
  /**
   * Remove class with animation support
   * @param {HTMLElement} element - Target element
   * @param {string} className - Class name to remove
   * @param {boolean} animate - Whether to animate
   */
  removeClass(element, className, animate = false) {
    if (!element) return;
    
    if (animate) {
      element.style.transition = 'all 0.3s ease';
    }
    
    element.classList.remove(className);
  }
  
  /**
   * Toggle class with animation support
   * @param {HTMLElement} element - Target element
   * @param {string} className - Class name to toggle
   * @param {boolean} animate - Whether to animate
   * @returns {boolean} Whether class is now present
   */
  toggleClass(element, className, animate = false) {
    if (!element) return false;
    
    if (animate) {
      element.style.transition = 'all 0.3s ease';
    }
    
    return element.classList.toggle(className);
  }
  
  /**
   * Show element with animation
   * @param {HTMLElement} element - Element to show
   * @param {string} display - Display value
   */
  show(element, display = 'block') {
    if (!element) return;
    
    element.style.display = display;
    element.style.opacity = '0';
    
    requestAnimationFrame(() => {
      element.style.transition = 'opacity 0.3s ease';
      element.style.opacity = '1';
    });
  }
  
  /**
   * Hide element with animation
   * @param {HTMLElement} element - Element to hide
   */
  hide(element) {
    if (!element) return;
    
    element.style.transition = 'opacity 0.3s ease';
    element.style.opacity = '0';
    
    setTimeout(() => {
      element.style.display = 'none';
    }, 300);
  }
  
  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    this.logger.debug('DOM cache cleared');
  }
  
  /**
   * Check if element is visible
   * @param {HTMLElement} element - Element to check
   * @returns {boolean} Whether element is visible
   */
  isVisible(element) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
  }
  
  /**
   * Scroll element into view smoothly
   * @param {HTMLElement} element - Element to scroll to
   * @param {Object} options - Scroll options
   */
  scrollIntoView(element, options = {}) {
    if (!element) return;
    
    const defaultOptions = {
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    };
    
    element.scrollIntoView({ ...defaultOptions, ...options });
  }
}

// Create singleton instance
const dom = new DOMQuery();

// Export utility functions
export const $ = (selector, context) => dom.querySelector(selector, context);
export const $$ = (selector, context) => dom.querySelectorAll(selector, context);
export const $id = (id, useCache) => dom.getElementById(id, useCache);
export const createElement = (tagName, attributes, content) => dom.createElement(tagName, attributes, content);

export { DOMQuery };
export default dom;
