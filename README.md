# High-Performance Google Clone

A fast, lightweight Google search clone built with pure JavaScript and optimized for performance. Uses Google Custom Search JSON API for real search results.

## Features

- ⚡ **Ultra-fast loading** - Pure JavaScript, no heavy frameworks
- 🔍 **Real search results** - Google Custom Search JSON API integration
- 📱 **Responsive design** - Works on desktop and mobile
- 🎯 **Autocomplete suggestions** - Real-time search suggestions
- 🖼️ **Multiple search types** - Web, Images, Videos, News
- 💾 **Offline support** - Service Worker with caching
- ♿ **Accessible** - WCAG compliant with keyboard navigation
- 🌙 **Dark mode** - Automatic dark/light theme detection
- 📄 **PWA ready** - Progressive Web App capabilities

## Quick Start

1. **Clone or download** this repository
2. **Configure Google Custom Search API** (see setup instructions below)
3. **Serve the files** using any web server
4. **Open in browser** and start searching!

## Google Custom Search API Setup

To get real search results, you need to configure the Google Custom Search API:

### Step 1: Get a Google API Key

1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select an existing one
3. Enable the "Custom Search API"
4. Go to "Credentials" and create an API key
5. Copy your API key

### Step 2: Create a Custom Search Engine

1. Go to [Google Custom Search Engine](https://cse.google.com/)
2. Click "Add" to create a new search engine
3. Enter `*.com` as the site to search (or specific sites)
4. Create the search engine
5. Go to "Setup" → "Basics" and copy your Search Engine ID

### Step 3: Configure the Application

1. Open `js/utils.js`
2. Replace the placeholder values:

```javascript
const CONFIG = {
    GOOGLE_API_KEY: 'YOUR_ACTUAL_API_KEY_HERE',
    SEARCH_ENGINE_ID: 'YOUR_ACTUAL_SEARCH_ENGINE_ID_HERE',
    // ... other config
};
```

## Local Development

### Using Python (recommended)
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

### Using Node.js
```bash
npx serve .
# or
npx http-server
```

### Using PHP
```bash
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## File Structure

```
├── index.html              # Main search page
├── results.html            # Search results page
├── styles/
│   └── main.css            # Main stylesheet
├── js/
│   ├── utils.js            # Utility functions and config
│   ├── search.js           # Main search functionality
│   ├── autocomplete.js     # Search suggestions
│   └── results.js          # Results page logic
├── manifest.json           # PWA manifest
├── service-worker.js       # Service worker for caching
└── README.md              # This file
```

## Performance Features

### JavaScript Optimizations
- **Debounced search** - Reduces API calls during typing
- **Caching** - LRU cache for search results and suggestions
- **Lazy loading** - Images load only when needed
- **Code splitting** - Separate files for different functionality

### CSS Optimizations
- **Critical CSS inlined** - Above-the-fold styles in HTML
- **CSS Grid/Flexbox** - Modern layout without heavy frameworks
- **Optimized animations** - Hardware-accelerated transforms
- **Responsive images** - Proper sizing for different screens

### Network Optimizations
- **Service Worker** - Caches resources for offline use
- **Resource preloading** - Critical resources loaded early
- **Compression ready** - Optimized for gzip/brotli
- **CDN friendly** - Static assets can be served from CDN

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## API Limits

Google Custom Search API has the following limits:
- **Free tier**: 100 queries per day
- **Paid tier**: Up to 10,000 queries per day

For production use, consider implementing:
- Rate limiting
- User authentication
- Caching strategies
- Alternative search providers

## Customization

### Styling
- Modify `styles/main.css` for visual changes
- CSS custom properties for easy theming
- Dark mode automatically detected

### Functionality
- Add new search types in `js/results.js`
- Customize autocomplete in `js/autocomplete.js`
- Modify caching behavior in `js/utils.js`

### Search Providers
- Replace Google API with other providers
- Implement multiple search backends
- Add search result aggregation

## Security Considerations

- API keys should be restricted by domain
- Implement rate limiting for production
- Sanitize all user inputs (already implemented)
- Use HTTPS in production

## Performance Tips

1. **Enable compression** on your web server (gzip/brotli)
2. **Use a CDN** for static assets
3. **Implement HTTP/2** for better multiplexing
4. **Monitor Core Web Vitals** for performance metrics
5. **Consider server-side rendering** for better SEO

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use this code for any purpose.

## Troubleshooting

### Search not working
- Check if API key and Search Engine ID are configured
- Verify API key has Custom Search API enabled
- Check browser console for error messages

### Slow performance
- Enable compression on your web server
- Check network tab for large resources
- Verify service worker is caching properly

### Styling issues
- Clear browser cache
- Check for CSS conflicts
- Verify responsive design on different screen sizes

## Future Enhancements

- [ ] Voice search integration
- [ ] Search filters and advanced options
- [ ] Search history and bookmarks
- [ ] Multiple language support
- [ ] Search analytics dashboard
- [ ] Social media search integration
