<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Search Test - Google Clone</title>
    <style>
        body {
            font-family: arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .search-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #4285f4;
            border-radius: 8px;
            background: #e8f0fe;
        }
        
        .search-input {
            width: 100%;
            max-width: 500px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 24px;
            font-size: 16px;
            outline: none;
            margin-bottom: 15px;
        }
        
        .search-input:focus {
            border-color: #4285f4;
            box-shadow: 0 2px 5px rgba(66, 133, 244, 0.3);
        }
        
        .search-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        .search-btn:hover {
            background: #3367d6;
        }
        
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .results-section {
            margin: 30px 0;
        }
        
        .results-info {
            color: #70757a;
            font-size: 14px;
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .result-item {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e8eaed;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            animation: fadeIn 0.5s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            margin-bottom: 8px;
            text-decoration: none;
            display: block;
            font-weight: 400;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-url {
            color: #006621;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .result-snippet em {
            font-style: normal;
            font-weight: bold;
            background: #fff3cd;
            padding: 1px 2px;
            border-radius: 2px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4285f4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .demo-notice {
            background: #e7f3ff;
            border: 1px solid #4285f4;
            color: #1967d2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Final Search Results Test</h1>
            <p>This page will <strong>definitely show search results</strong> - either from the API or demo data!</p>
        </div>
        
        <div class="search-section">
            <h3>🧪 Search Test</h3>
            <input type="text" class="search-input" id="search-input" placeholder="Enter search query" value="javascript">
            
            <div class="test-buttons">
                <button class="search-btn" onclick="performSearch()">🔍 Search</button>
                <button class="search-btn" onclick="showDemoResults()">📋 Show Demo Results</button>
                <button class="search-btn" onclick="testAPI()">🧪 Test API</button>
                <button class="search-btn" onclick="clearResults()">🧹 Clear</button>
            </div>
        </div>
        
        <div id="status-container"></div>
        
        <div class="results-section">
            <div id="results-info"></div>
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <div>Searching...</div>
            </div>
            <div id="results-container"></div>
        </div>
    </div>
    
    <script>
        // Configuration
        const CONFIG = {
            API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
            SEARCH_ENGINE_ID: '61201925358ea4e83',
            API_URL: 'https://www.googleapis.com/customsearch/v1',
            TIMEOUT: 3000 // 3 second timeout
        };
        
        let currentQuery = '';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Final Test Page Loaded');
            showStatus('Page loaded successfully! Ready to test search.', 'success');
            
            // Check URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            
            if (query) {
                console.log('🔍 Found query in URL:', query);
                document.getElementById('search-input').value = query;
                showStatus(`Found query "${query}" in URL. Click Search to test!`, 'info');
            } else {
                // Show demo results immediately so user sees something
                showDemoResults();
            }
            
            // Add enter key listener
            document.getElementById('search-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
        
        function showStatus(message, type = 'info') {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results-container').style.display = 'none';
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results-container').style.display = 'block';
        }
        
        async function performSearch() {
            const query = document.getElementById('search-input').value.trim();
            
            if (!query) {
                showStatus('Please enter a search query!', 'warning');
                return;
            }
            
            currentQuery = query;
            console.log('🔍 Starting search for:', query);
            
            showLoading();
            showStatus('🔍 Searching... (will timeout in 3 seconds if API is slow)', 'info');
            
            try {
                // Try API with timeout
                const results = await Promise.race([
                    searchWithAPI(query),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Timeout')), CONFIG.TIMEOUT)
                    )
                ]);
                
                console.log('✅ API search successful');
                hideLoading();
                displayResults(results, true);
                showStatus('✅ Search completed successfully with real API results!', 'success');
                
            } catch (error) {
                console.log('⚠️ API search failed or timed out:', error.message);
                hideLoading();
                showDemoResults(query);
                
                if (error.message === 'Timeout') {
                    showStatus('⏰ API was slow, showing demo results instead (this is normal)', 'warning');
                } else {
                    showStatus('⚠️ API error, showing demo results: ' + error.message, 'warning');
                }
            }
        }
        
        async function searchWithAPI(query) {
            console.log('🌐 Making API request for:', query);
            
            const params = new URLSearchParams({
                key: CONFIG.API_KEY,
                cx: CONFIG.SEARCH_ENGINE_ID,
                q: query,
                num: 5
            });
            
            const url = `${CONFIG.API_URL}?${params.toString()}`;
            console.log('📡 API URL:', url.substring(0, 100) + '...');
            
            const response = await fetch(url);
            console.log('📥 API Response status:', response.status);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }
            
            const data = await response.json();
            console.log('✅ API Response received:', data);
            
            return data;
        }
        
        function showDemoResults(query = 'demo search') {
            console.log('🎭 Showing demo results for:', query);
            currentQuery = query;
            
            const demoData = {
                searchInformation: {
                    totalResults: "1,234,567",
                    searchTime: 0.45
                },
                items: [
                    {
                        title: `${query} - Complete Tutorial and Guide`,
                        link: `https://developer.mozilla.org/search?q=${encodeURIComponent(query)}`,
                        snippet: `Learn everything about ${query} with this comprehensive tutorial. Covers basics to advanced concepts with practical examples and best practices.`,
                        displayLink: "developer.mozilla.org"
                    },
                    {
                        title: `${query} - Stack Overflow Questions and Answers`,
                        link: `https://stackoverflow.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Find solutions and answers related to ${query}. Community-driven Q&A platform with expert developers sharing knowledge and code examples.`,
                        displayLink: "stackoverflow.com"
                    },
                    {
                        title: `${query} - GitHub Projects and Code Examples`,
                        link: `https://github.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Explore open source projects and code repositories for ${query}. Discover libraries, frameworks, and example implementations from the developer community.`,
                        displayLink: "github.com"
                    },
                    {
                        title: `${query} - Video Tutorials and Courses`,
                        link: `https://www.youtube.com/results?search_query=${encodeURIComponent(query)}`,
                        snippet: `Watch comprehensive video tutorials about ${query}. Learn from industry experts with step-by-step guides, live coding sessions, and practical projects.`,
                        displayLink: "youtube.com"
                    },
                    {
                        title: `${query} - Latest News and Articles`,
                        link: `https://medium.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Read the latest articles and insights about ${query}. In-depth analysis, trends, and expert opinions from industry professionals and thought leaders.`,
                        displayLink: "medium.com"
                    }
                ]
            };
            
            displayResults(demoData, false);
            showStatus('📋 Showing demo results (this proves the interface works perfectly!)', 'info');
        }
        
        function displayResults(data, isRealAPI = false) {
            console.log('📋 Displaying results:', data);
            
            const resultsInfo = document.getElementById('results-info');
            const resultsContainer = document.getElementById('results-container');
            
            if (!data || !data.items || data.items.length === 0) {
                resultsInfo.innerHTML = '<div class="status error">No results found</div>';
                resultsContainer.innerHTML = '';
                return;
            }
            
            // Update results info
            const totalResults = data.searchInformation?.totalResults || '0';
            const searchTime = data.searchInformation?.searchTime || 0;
            const sourceText = isRealAPI ? '(Real Google API)' : '(Demo Data)';
            
            resultsInfo.innerHTML = `
                <strong>About ${totalResults} results</strong> (${searchTime} seconds) ${sourceText}
                <br><small>Query: "${currentQuery}"</small>
            `;
            
            // Display results
            let resultsHTML = '';
            
            data.items.forEach((item, index) => {
                const highlightedSnippet = highlightSearchTerms(item.snippet || '', currentQuery);
                
                resultsHTML += `
                    <div class="result-item" style="animation-delay: ${index * 0.1}s">
                        <a href="${escapeHtml(item.link)}" class="result-title" target="_blank" rel="noopener">
                            ${escapeHtml(item.title)}
                        </a>
                        <div class="result-url">${escapeHtml(item.displayLink || extractDomain(item.link))}</div>
                        <div class="result-snippet">${highlightedSnippet}</div>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = resultsHTML;
            console.log('✅ Results displayed successfully');
        }
        
        async function testAPI() {
            console.log('🧪 Testing API connectivity');
            showStatus('🧪 Testing API connectivity...', 'info');
            
            try {
                const testUrl = `${CONFIG.API_URL}?key=${CONFIG.API_KEY}&cx=${CONFIG.SEARCH_ENGINE_ID}&q=test&num=1`;
                
                const response = await fetch(testUrl);
                console.log('📡 API Test response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    const totalResults = data.searchInformation?.totalResults || 'Unknown';
                    showStatus(`✅ API is working! Found ${totalResults} results for "test"`, 'success');
                } else {
                    const errorData = await response.json();
                    showStatus(`❌ API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                console.error('❌ API test failed:', error);
                showStatus(`❌ API test failed: ${error.message}`, 'error');
            }
        }
        
        function clearResults() {
            console.log('🧹 Clearing results');
            document.getElementById('results-info').innerHTML = '';
            document.getElementById('results-container').innerHTML = '';
            document.getElementById('status-container').innerHTML = '';
            showStatus('🧹 Results cleared. Ready for new search!', 'info');
        }
        
        function extractDomain(url) {
            try {
                return new URL(url).hostname.replace('www.', '');
            } catch {
                return url;
            }
        }
        
        function highlightSearchTerms(text, query) {
            if (!query || query.length < 2) return escapeHtml(text);
            
            const terms = query.split(' ').filter(term => term.length > 1);
            let highlightedText = escapeHtml(text);
            
            terms.forEach(term => {
                const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(`(${escapedTerm})`, 'gi');
                highlightedText = highlightedText.replace(regex, '<em>$1</em>');
            });
            
            return highlightedText;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('💥 Global error:', e.error);
            showStatus('❌ JavaScript error: ' + e.message, 'error');
        });
        
        console.log('✅ Final Test script loaded successfully');
    </script>
</body>
</html>
