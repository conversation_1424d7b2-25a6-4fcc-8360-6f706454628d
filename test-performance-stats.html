<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Stats Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        /* Performance Stats Bar */
        .performance-stats {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #495057;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .performance-stats:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .performance-stats-left {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .performance-stats-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .perf-stat {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background: rgba(255,255,255,0.7);
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .perf-stat-icon {
            font-size: 14px;
        }

        .perf-stat-label {
            font-weight: 500;
            color: #343a40;
        }

        .perf-stat-value {
            font-weight: 600;
            color: #007bff;
        }

        .perf-stat.excellent .perf-stat-value {
            color: #28a745;
        }

        .perf-stat.good .perf-stat-value {
            color: #17a2b8;
        }

        .perf-stat.average .perf-stat-value {
            color: #ffc107;
        }

        .perf-stat.slow .perf-stat-value {
            color: #dc3545;
        }

        .performance-toggle {
            background: none;
            border: 1px solid #6c757d;
            color: #6c757d;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .performance-toggle:hover {
            background: #6c757d;
            color: white;
        }

        .performance-details {
            display: none;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #dee2e6;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .performance-details.expanded {
            display: grid;
        }

        .perf-detail-item {
            background: rgba(255,255,255,0.8);
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .perf-detail-label {
            font-size: 11px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 2px;
        }

        .perf-detail-value {
            font-size: 13px;
            font-weight: 600;
            color: #343a40;
        }

        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }

        .test-button:hover {
            background: #3367d6;
        }

        .test-results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Performance Stats Test</h1>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button class="test-button" onclick="simulateSearch()">Simulate Search</button>
            <button class="test-button" onclick="simulateSlowSearch()">Simulate Slow Search</button>
            <button class="test-button" onclick="clearStats()">Clear Stats</button>
        </div>

        <!-- Performance Stats Bar -->
        <div class="performance-stats" id="performance-stats">
            <div class="performance-stats-left">
                <div class="perf-stat" id="api-time-stat">
                    <span class="perf-stat-icon">⚡</span>
                    <span class="perf-stat-label">API:</span>
                    <span class="perf-stat-value" id="api-time-value">--</span>
                </div>
                <div class="perf-stat" id="total-time-stat">
                    <span class="perf-stat-icon">🕐</span>
                    <span class="perf-stat-label">Total:</span>
                    <span class="perf-stat-value" id="total-time-value">--</span>
                </div>
                <div class="perf-stat" id="render-time-stat">
                    <span class="perf-stat-icon">🎨</span>
                    <span class="perf-stat-label">Render:</span>
                    <span class="perf-stat-value" id="render-time-value">--</span>
                </div>
                <div class="perf-stat" id="results-count-stat">
                    <span class="perf-stat-icon">📊</span>
                    <span class="perf-stat-label">Results:</span>
                    <span class="perf-stat-value" id="results-count-value">--</span>
                </div>
            </div>
            <div class="performance-stats-right">
                <button class="performance-toggle" id="performance-toggle" onclick="togglePerformanceDetails()">
                    Details
                </button>
            </div>
            <div class="performance-details" id="performance-details">
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Page Load</div>
                    <div class="perf-detail-value" id="page-load-time">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">DOM Ready</div>
                    <div class="perf-detail-value" id="dom-ready-time">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Search Type</div>
                    <div class="perf-detail-value" id="search-type-value">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Data Source</div>
                    <div class="perf-detail-value" id="data-source-value">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Cache Status</div>
                    <div class="perf-detail-value" id="cache-status-value">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Memory Usage</div>
                    <div class="perf-detail-value" id="memory-usage-value">--</div>
                </div>
            </div>
        </div>

        <div class="test-results">
            <h3>Test Results</h3>
            <div id="test-output">Click a test button to see performance stats in action.</div>
        </div>
    </div>

    <script>
        function togglePerformanceDetails() {
            const details = document.getElementById('performance-details');
            const toggle = document.getElementById('performance-toggle');
            
            if (details && toggle) {
                const isExpanded = details.classList.contains('expanded');
                
                if (isExpanded) {
                    details.classList.remove('expanded');
                    toggle.textContent = 'Details';
                } else {
                    details.classList.add('expanded');
                    toggle.textContent = 'Hide';
                }
            }
        }

        function updateStatValue(elementId, value, performanceClass = '') {
            const element = document.getElementById(elementId);
            if (!element) return;

            element.textContent = value;
            
            // Remove existing performance classes
            element.parentElement.classList.remove('excellent', 'good', 'average', 'slow');
            
            // Add new performance class if provided
            if (performanceClass) {
                element.parentElement.classList.add(performanceClass);
            }
        }

        function getPerformanceClass(timeMs, type) {
            const thresholds = {
                api: { excellent: 200, good: 500, average: 1000 },
                render: { excellent: 50, good: 100, average: 200 },
                total: { excellent: 300, good: 700, average: 1500 }
            };

            const threshold = thresholds[type] || thresholds.total;

            if (timeMs <= threshold.excellent) return 'excellent';
            if (timeMs <= threshold.good) return 'good';
            if (timeMs <= threshold.average) return 'average';
            return 'slow';
        }

        async function simulateSearch() {
            const output = document.getElementById('test-output');
            output.innerHTML = 'Simulating fast search...';

            // Simulate API call
            const apiStart = performance.now();
            await new Promise(resolve => setTimeout(resolve, 150)); // 150ms API call
            const apiEnd = performance.now();

            // Simulate rendering
            const renderStart = performance.now();
            await new Promise(resolve => setTimeout(resolve, 30)); // 30ms render
            const renderEnd = performance.now();

            const apiTime = apiEnd - apiStart;
            const renderTime = renderEnd - renderStart;
            const totalTime = apiTime + renderTime;

            // Update stats
            updateStatValue('api-time-value', `${apiTime.toFixed(0)}ms`, getPerformanceClass(apiTime, 'api'));
            updateStatValue('total-time-value', `${totalTime.toFixed(0)}ms`, getPerformanceClass(totalTime, 'total'));
            updateStatValue('render-time-value', `${renderTime.toFixed(0)}ms`, getPerformanceClass(renderTime, 'render'));
            updateStatValue('results-count-value', '10');

            // Update detailed stats
            document.getElementById('page-load-time').textContent = `${performance.now().toFixed(0)}ms`;
            document.getElementById('dom-ready-time').textContent = 'Complete';
            document.getElementById('search-type-value').textContent = 'ALL';
            document.getElementById('data-source-value').textContent = 'Google API';
            document.getElementById('cache-status-value').textContent = 'No Cache';
            
            if (performance.memory) {
                const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                document.getElementById('memory-usage-value').textContent = `${memoryMB} MB`;
            } else {
                document.getElementById('memory-usage-value').textContent = 'N/A';
            }

            output.innerHTML = `✅ Fast search completed:<br>
                API: ${apiTime.toFixed(0)}ms (${getPerformanceClass(apiTime, 'api')})<br>
                Render: ${renderTime.toFixed(0)}ms (${getPerformanceClass(renderTime, 'render')})<br>
                Total: ${totalTime.toFixed(0)}ms (${getPerformanceClass(totalTime, 'total')})`;
        }

        async function simulateSlowSearch() {
            const output = document.getElementById('test-output');
            output.innerHTML = 'Simulating slow search...';

            // Simulate slow API call
            const apiStart = performance.now();
            await new Promise(resolve => setTimeout(resolve, 1200)); // 1200ms API call
            const apiEnd = performance.now();

            // Simulate slow rendering
            const renderStart = performance.now();
            await new Promise(resolve => setTimeout(resolve, 250)); // 250ms render
            const renderEnd = performance.now();

            const apiTime = apiEnd - apiStart;
            const renderTime = renderEnd - renderStart;
            const totalTime = apiTime + renderTime;

            // Update stats
            updateStatValue('api-time-value', `${apiTime.toFixed(0)}ms`, getPerformanceClass(apiTime, 'api'));
            updateStatValue('total-time-value', `${totalTime.toFixed(0)}ms`, getPerformanceClass(totalTime, 'total'));
            updateStatValue('render-time-value', `${renderTime.toFixed(0)}ms`, getPerformanceClass(renderTime, 'render'));
            updateStatValue('results-count-value', '8');

            // Update detailed stats
            document.getElementById('data-source-value').textContent = 'Demo Data';
            document.getElementById('cache-status-value').textContent = 'Cache Miss';

            output.innerHTML = `⚠️ Slow search completed:<br>
                API: ${apiTime.toFixed(0)}ms (${getPerformanceClass(apiTime, 'api')})<br>
                Render: ${renderTime.toFixed(0)}ms (${getPerformanceClass(renderTime, 'render')})<br>
                Total: ${totalTime.toFixed(0)}ms (${getPerformanceClass(totalTime, 'total')})`;
        }

        function clearStats() {
            updateStatValue('api-time-value', '--');
            updateStatValue('total-time-value', '--');
            updateStatValue('render-time-value', '--');
            updateStatValue('results-count-value', '--');
            
            document.getElementById('test-output').innerHTML = 'Stats cleared. Click a test button to see performance stats in action.';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            document.getElementById('page-load-time').textContent = `${performance.now().toFixed(0)}ms`;
            document.getElementById('dom-ready-time').textContent = 'Complete';
        });
    </script>
</body>
</html>
