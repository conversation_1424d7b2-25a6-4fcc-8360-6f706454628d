<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Custom Search API Diagnostic</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .diagnostic-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #d93025;
            margin-bottom: 10px;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #1557b0;
        }
        .btn.danger {
            background: #d93025;
        }
        .btn.danger:hover {
            background: #b52d20;
        }
        .config-display {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 15px 0;
            word-break: break-all;
        }
        .issue-box {
            background: #fef7e0;
            border: 1px solid #f9c74f;
            border-left: 4px solid #f9844a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 6px;
        }
        .solution-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-left: 4px solid #2e7d32;
            padding: 20px;
            margin: 20px 0;
            border-radius: 6px;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #1a73e8;
        }
        .step h4 {
            margin-top: 0;
            color: #1a73e8;
        }
        .link {
            color: #1a73e8;
            text-decoration: none;
        }
        .link:hover {
            text-decoration: underline;
        }
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="header">
            <h1>🚨 Google Custom Search API Diagnostic</h1>
            <p>Let's diagnose and fix your API configuration issue</p>
        </div>

        <div class="issue-box">
            <h3>🔍 Current Issue Detected</h3>
            <p><strong>Problem:</strong> Your application shows "⚠️ API not available, showing demo results"</p>
            <p><strong>Likely Cause:</strong> Invalid or incorrectly formatted API credentials</p>
        </div>

        <div id="current-config" class="config-display">
            Loading current configuration...
        </div>

        <button class="btn" onclick="diagnoseCurrentConfig()">🔍 Diagnose Current Config</button>
        <button class="btn" onclick="testCurrentAPI()">🧪 Test Current API</button>
        <button class="btn danger" onclick="showSetupGuide()">🛠️ Setup New API</button>

        <div id="diagnostic-results"></div>

        <div id="setup-guide" style="display: none;">
            <div class="solution-box">
                <h3>✅ Solution: Set Up Google Custom Search API</h3>
                <p>Follow these steps to get valid API credentials:</p>
            </div>

            <div class="step">
                <h4>Step 1: Get Google API Key</h4>
                <ol>
                    <li>Go to <a href="https://console.developers.google.com/" target="_blank" class="link">Google Cloud Console</a></li>
                    <li>Create a new project or select existing one</li>
                    <li>Enable "Custom Search API" in the API Library</li>
                    <li>Go to "Credentials" → "Create Credentials" → "API Key"</li>
                    <li>Copy the API key (starts with "AIzaSy...")</li>
                </ol>
            </div>

            <div class="step">
                <h4>Step 2: Create Custom Search Engine</h4>
                <ol>
                    <li>Go to <a href="https://cse.google.com/" target="_blank" class="link">Google Custom Search Engine</a></li>
                    <li>Click "Add" to create a new search engine</li>
                    <li>In "Sites to search", enter: <code>*</code> (for web-wide search)</li>
                    <li>Name your search engine</li>
                    <li>Click "Create" and copy the Search Engine ID</li>
                </ol>
            </div>

            <div class="test-form">
                <h4>Step 3: Test Your New Credentials</h4>
                <div class="form-group">
                    <label for="newApiKey">Your Google API Key:</label>
                    <input type="text" id="newApiKey" placeholder="AIzaSy..." />
                </div>
                <div class="form-group">
                    <label for="newSearchEngineId">Your Search Engine ID:</label>
                    <input type="text" id="newSearchEngineId" placeholder="017576662512468239146:omuauf_lfve" />
                </div>
                <button class="btn" onclick="testNewCredentials()">🧪 Test New Credentials</button>
                <button class="btn" onclick="generateConfigCode()">📝 Generate Config Code</button>
            </div>

            <div id="new-config-code" style="display: none;">
                <h4>Step 4: Update Your Configuration</h4>
                <p>Copy this code and replace the CONFIG section in <code>js/utils.js</code>:</p>
                <div id="generated-config" class="config-display"></div>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script>
        // Display current configuration
        function displayCurrentConfig() {
            const configDiv = document.getElementById('current-config');
            if (window.Utils && window.Utils.CONFIG) {
                const config = window.Utils.CONFIG;
                configDiv.innerHTML = `
                    <strong>Current Configuration in js/utils.js:</strong><br>
                    GOOGLE_API_KEY: "${config.GOOGLE_API_KEY}"<br>
                    SEARCH_ENGINE_ID: "${config.SEARCH_ENGINE_ID}"<br>
                    <br>
                    <strong>Analysis:</strong><br>
                    API Key Format: ${analyzeApiKey(config.GOOGLE_API_KEY)}<br>
                    Search Engine ID Format: ${analyzeSearchEngineId(config.SEARCH_ENGINE_ID)}
                `;
            } else {
                configDiv.innerHTML = '<strong>❌ Utils.js not loaded or CONFIG not available</strong>';
            }
        }

        function analyzeApiKey(key) {
            if (!key) return '❌ Missing';
            if (key.startsWith('AIzaSy') && key.length >= 35) return '✅ Valid format';
            if (key.startsWith('AIzaSy')) return '⚠️ Correct prefix but may be incomplete';
            return '❌ Invalid format (should start with "AIzaSy")';
        }

        function analyzeSearchEngineId(id) {
            if (!id) return '❌ Missing';
            if (id.includes(':')) return '✅ Valid format (contains colon)';
            if (id.length > 10) return '⚠️ Unusual format (missing colon separator)';
            return '❌ Invalid format';
        }

        async function diagnoseCurrentConfig() {
            const resultsDiv = document.getElementById('diagnostic-results');
            showStatus('🔍 Diagnosing current configuration...', 'info');

            try {
                if (!window.Utils || !window.Utils.CONFIG) {
                    throw new Error('Utils.js not loaded properly');
                }

                const config = window.Utils.CONFIG;
                let issues = [];
                let recommendations = [];

                // Check API key
                if (!config.GOOGLE_API_KEY || config.GOOGLE_API_KEY === 'YOUR_GOOGLE_API_KEY_HERE') {
                    issues.push('❌ API Key is missing or placeholder');
                    recommendations.push('Get a valid Google API key from Google Cloud Console');
                } else if (!config.GOOGLE_API_KEY.startsWith('AIzaSy')) {
                    issues.push('❌ API Key has invalid format');
                    recommendations.push('API keys should start with "AIzaSy"');
                } else if (config.GOOGLE_API_KEY.length < 35) {
                    issues.push('⚠️ API Key appears incomplete');
                    recommendations.push('Verify the complete API key was copied');
                }

                // Check Search Engine ID
                if (!config.SEARCH_ENGINE_ID || config.SEARCH_ENGINE_ID === 'YOUR_SEARCH_ENGINE_ID_HERE') {
                    issues.push('❌ Search Engine ID is missing or placeholder');
                    recommendations.push('Create a Custom Search Engine and get the ID');
                } else if (!config.SEARCH_ENGINE_ID.includes(':')) {
                    issues.push('⚠️ Search Engine ID has unusual format');
                    recommendations.push('Search Engine IDs typically contain a colon (:)');
                }

                let html = '<div class="status ' + (issues.length === 0 ? 'success' : 'error') + '">';
                html += '<h4>Diagnostic Results:</h4>';
                
                if (issues.length === 0) {
                    html += '✅ Configuration appears valid. Testing API connection...';
                } else {
                    html += '<strong>Issues Found:</strong><ul>';
                    issues.forEach(issue => html += `<li>${issue}</li>`);
                    html += '</ul><strong>Recommendations:</strong><ul>';
                    recommendations.forEach(rec => html += `<li>${rec}</li>`);
                    html += '</ul>';
                }
                html += '</div>';

                resultsDiv.innerHTML = html;

                // If config looks valid, test the API
                if (issues.length === 0) {
                    setTimeout(testCurrentAPI, 1000);
                }

            } catch (error) {
                showStatus(`❌ Diagnostic Error: ${error.message}`, 'error');
            }
        }

        async function testCurrentAPI() {
            showStatus('🧪 Testing current API configuration...', 'info');

            try {
                if (window.Utils && window.Utils.verifyApiConfiguration) {
                    const result = await window.Utils.verifyApiConfiguration();
                    
                    if (result.working) {
                        showStatus(`✅ API Test Successful!<br>
                            ${result.message}<br>
                            <strong>Your API is working! The issue may be elsewhere.</strong>`, 'success');
                    } else {
                        showStatus(`❌ API Test Failed<br>
                            ${result.message}<br>
                            <strong>This confirms the API credentials are invalid.</strong>`, 'error');
                        document.getElementById('setup-guide').style.display = 'block';
                    }
                } else {
                    throw new Error('API verification function not available');
                }
            } catch (error) {
                showStatus(`❌ API Test Error: ${error.message}`, 'error');
                document.getElementById('setup-guide').style.display = 'block';
            }
        }

        async function testNewCredentials() {
            const apiKey = document.getElementById('newApiKey').value.trim();
            const searchEngineId = document.getElementById('newSearchEngineId').value.trim();

            if (!apiKey || !searchEngineId) {
                showStatus('Please enter both API Key and Search Engine ID', 'error');
                return;
            }

            showStatus('🧪 Testing new credentials...', 'info');

            try {
                const testUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=test&num=1`;
                
                const response = await fetch(testUrl);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    showStatus(`❌ API Test Failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                    return;
                }
                
                const data = await response.json();
                const totalResults = data.searchInformation?.totalResults || '0';
                const searchTime = data.searchInformation?.searchTime || '0';
                
                showStatus(`✅ New Credentials Work!<br>Found ${parseInt(totalResults).toLocaleString()} results in ${searchTime} seconds`, 'success');
                
                // Show the config code
                document.getElementById('new-config-code').style.display = 'block';
                
            } catch (error) {
                showStatus(`❌ Network Error: ${error.message}`, 'error');
            }
        }

        function generateConfigCode() {
            const apiKey = document.getElementById('newApiKey').value.trim();
            const searchEngineId = document.getElementById('newSearchEngineId').value.trim();

            if (!apiKey || !searchEngineId) {
                showStatus('Please test credentials first', 'error');
                return;
            }

            const configCode = `const CONFIG = {
    // Updated API configuration with working credentials
    GOOGLE_API_KEY: '${apiKey}',
    SEARCH_ENGINE_ID: '${searchEngineId}',
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    RESULTS_PER_PAGE: 10,
    MAX_CACHE_SIZE: 50,
    DEBOUNCE_DELAY: 300,
    
    // Fallback configuration for testing
    FALLBACK_API_KEY: '${apiKey}',
    FALLBACK_SEARCH_ENGINE_ID: '${searchEngineId}'
};`;

            document.getElementById('generated-config').textContent = configCode;
            document.getElementById('new-config-code').style.display = 'block';
        }

        function showSetupGuide() {
            document.getElementById('setup-guide').style.display = 'block';
        }

        function showStatus(message, type) {
            const resultsDiv = document.getElementById('diagnostic-results');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            displayCurrentConfig();
            setTimeout(diagnoseCurrentConfig, 500);
        });
    </script>
</body>
</html>
