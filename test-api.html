<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3367d6;
        }
        .results {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .loading {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>Google Custom Search API Test</h1>
    <p>This page tests the API for different search types to identify any issues.</p>

    <div class="test-section">
        <h2>API Configuration Test</h2>
        <button class="test-button" onclick="testApiConfig()">Test API Configuration</button>
        <div id="config-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>Search Type Tests</h2>
        <button class="test-button" onclick="testSearch('web')">Test Web Search</button>
        <button class="test-button" onclick="testSearch('images')">Test Image Search</button>
        <button class="test-button" onclick="testSearch('videos')">Test Video Search</button>
        <button class="test-button" onclick="testSearch('news')">Test News Search</button>
        <button class="test-button" onclick="testSearch('shopping')">Test Shopping Search</button>
        <button class="test-button" onclick="testSearch('books')">Test Books Search</button>
        <div id="search-results" class="results"></div>
    </div>

    <script>
        const API_CONFIG = {
            GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
            SEARCH_ENGINE_ID: '61201925358ea4e83',
            BASE_URL: 'https://www.googleapis.com/customsearch/v1'
        };

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'loading' ? 'loading' : '';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        async function testApiConfig() {
            const resultsDiv = document.getElementById('config-results');
            resultsDiv.innerHTML = '';
            
            log('config-results', 'Testing API configuration...', 'loading');
            
            try {
                const params = new URLSearchParams({
                    key: API_CONFIG.GOOGLE_API_KEY,
                    cx: API_CONFIG.SEARCH_ENGINE_ID,
                    q: 'test',
                    num: 1
                });

                const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                log('config-results', `Making request to: ${url.substring(0, 100)}...`);

                const response = await fetch(url);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
                    log('config-results', `API Error: ${response.status} - ${errorData.error?.message}`, 'error');
                    return;
                }

                const data = await response.json();
                log('config-results', `✅ API working! Total results: ${data.searchInformation?.totalResults}`, 'success');
                log('config-results', `Search time: ${data.searchInformation?.searchTime}s`, 'success');
                
            } catch (error) {
                log('config-results', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testSearch(searchType) {
            const resultsDiv = document.getElementById('search-results');
            
            log('search-results', `Testing ${searchType} search...`, 'loading');
            
            try {
                const params = new URLSearchParams({
                    key: API_CONFIG.GOOGLE_API_KEY,
                    cx: API_CONFIG.SEARCH_ENGINE_ID,
                    q: 'test',
                    start: 1,
                    num: 5
                });

                // Add search type specific parameters
                if (searchType === 'images') {
                    params.append('searchType', 'image');
                    params.append('imgSize', 'large');
                    params.append('imgType', 'photo');
                    params.append('safe', 'active');
                    params.append('filter', '1');
                } else if (searchType === 'videos') {
                    params.append('siteSearch', 'youtube.com');
                    params.append('filter', '1');
                    params.append('lr', 'lang_en');
                } else if (searchType === 'news') {
                    params.append('sort', 'date');
                    params.append('dateRestrict', 'm1');
                    params.append('filter', '1');
                    params.append('lr', 'lang_en');
                } else if (searchType === 'shopping') {
                    params.append('siteSearch', 'amazon.com');
                    params.append('filter', '1');
                    params.append('lr', 'lang_en');
                } else if (searchType === 'books') {
                    params.append('siteSearch', 'books.google.com');
                    params.append('filter', '1');
                    params.append('lr', 'lang_en');
                }

                const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                log('search-results', `${searchType}: ${url.substring(0, 120)}...`);

                const response = await fetch(url);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
                    log('search-results', `❌ ${searchType} failed: ${response.status} - ${errorData.error?.message}`, 'error');
                    return;
                }

                const data = await response.json();
                const resultCount = data.items ? data.items.length : 0;
                log('search-results', `✅ ${searchType} success: ${resultCount} results found`, 'success');
                
                if (data.items && data.items.length > 0) {
                    log('search-results', `First result: ${data.items[0].title}`, 'success');
                }
                
            } catch (error) {
                log('search-results', `❌ ${searchType} error: ${error.message}`, 'error');
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testApiConfig();
            }, 1000);
        });
    </script>
</body>
</html>
