// Search Tools functionality - Time filters, result type filters, etc.

class SearchTools {
    constructor() {
        this.isVisible = false;
        this.activeFilters = {
            time: 'any',
            type: 'any',
            language: 'any',
            region: 'any',
            usage: 'any'
        };
        this.init();
    }
    
    init() {
        this.addSearchToolsButton();
        this.createSearchToolsPanel();
        this.bindEvents();
    }
    
    addSearchToolsButton() {
        // Add to results page navigation
        const navContent = document.querySelector('.nav-content');
        if (navContent) {
            const toolsButton = document.createElement('button');
            toolsButton.id = 'search-tools-btn';
            toolsButton.className = 'nav-tab tools-btn';
            toolsButton.textContent = 'Tools';
            toolsButton.style.marginLeft = 'auto';
            navContent.appendChild(toolsButton);
        }
    }
    
    createSearchToolsPanel() {
        const panel = document.createElement('div');
        panel.id = 'search-tools-panel';
        panel.className = 'search-tools-panel';
        panel.innerHTML = `
            <div class="tools-content">
                <div class="tools-section">
                    <h4>Time</h4>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="time" data-value="any">Any time</button>
                        <button class="filter-btn" data-filter="time" data-value="hour">Past hour</button>
                        <button class="filter-btn" data-filter="time" data-value="day">Past 24 hours</button>
                        <button class="filter-btn" data-filter="time" data-value="week">Past week</button>
                        <button class="filter-btn" data-filter="time" data-value="month">Past month</button>
                        <button class="filter-btn" data-filter="time" data-value="year">Past year</button>
                        <button class="filter-btn" data-filter="time" data-value="custom">Custom range...</button>
                    </div>
                </div>
                
                <div class="tools-section">
                    <h4>Result type</h4>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="type" data-value="any">All results</button>
                        <button class="filter-btn" data-filter="type" data-value="pdf">PDF</button>
                        <button class="filter-btn" data-filter="type" data-value="doc">Word documents</button>
                        <button class="filter-btn" data-filter="type" data-value="xls">Spreadsheets</button>
                        <button class="filter-btn" data-filter="type" data-value="ppt">Presentations</button>
                        <button class="filter-btn" data-filter="type" data-value="books">Books</button>
                        <button class="filter-btn" data-filter="type" data-value="apps">Applications</button>
                    </div>
                </div>
                
                <div class="tools-section">
                    <h4>Language</h4>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="language" data-value="any">Any language</button>
                        <button class="filter-btn" data-filter="language" data-value="en">English</button>
                        <button class="filter-btn" data-filter="language" data-value="es">Spanish</button>
                        <button class="filter-btn" data-filter="language" data-value="fr">French</button>
                        <button class="filter-btn" data-filter="language" data-value="de">German</button>
                        <button class="filter-btn" data-filter="language" data-value="it">Italian</button>
                        <button class="filter-btn" data-filter="language" data-value="pt">Portuguese</button>
                        <button class="filter-btn" data-filter="language" data-value="ru">Russian</button>
                        <button class="filter-btn" data-filter="language" data-value="ja">Japanese</button>
                        <button class="filter-btn" data-filter="language" data-value="ko">Korean</button>
                        <button class="filter-btn" data-filter="language" data-value="zh">Chinese</button>
                    </div>
                </div>
                
                <div class="tools-section">
                    <h4>Region</h4>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="region" data-value="any">Any region</button>
                        <button class="filter-btn" data-filter="region" data-value="us">United States</button>
                        <button class="filter-btn" data-filter="region" data-value="uk">United Kingdom</button>
                        <button class="filter-btn" data-filter="region" data-value="ca">Canada</button>
                        <button class="filter-btn" data-filter="region" data-value="au">Australia</button>
                        <button class="filter-btn" data-filter="region" data-value="de">Germany</button>
                        <button class="filter-btn" data-filter="region" data-value="fr">France</button>
                        <button class="filter-btn" data-filter="region" data-value="es">Spain</button>
                        <button class="filter-btn" data-filter="region" data-value="it">Italy</button>
                        <button class="filter-btn" data-filter="region" data-value="jp">Japan</button>
                        <button class="filter-btn" data-filter="region" data-value="kr">South Korea</button>
                        <button class="filter-btn" data-filter="region" data-value="cn">China</button>
                    </div>
                </div>
                
                <div class="tools-section">
                    <h4>Usage rights</h4>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="usage" data-value="any">Not filtered by license</button>
                        <button class="filter-btn" data-filter="usage" data-value="free">Free to use or share</button>
                        <button class="filter-btn" data-filter="usage" data-value="commercial">Free to use commercially</button>
                        <button class="filter-btn" data-filter="usage" data-value="modify">Free to use, share or modify</button>
                        <button class="filter-btn" data-filter="usage" data-value="commercial-modify">Free to use, share or modify commercially</button>
                    </div>
                </div>
                
                <div class="tools-actions">
                    <button class="btn btn-secondary" id="clear-filters">Clear all filters</button>
                    <button class="btn btn-primary" id="apply-tools-filters">Apply filters</button>
                </div>
            </div>
        `;
        
        // Insert after nav tabs
        const navTabs = document.querySelector('.nav-tabs');
        if (navTabs) {
            navTabs.insertAdjacentElement('afterend', panel);
        }
        
        this.addToolsStyles();
    }
    
    addToolsStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .tools-btn {
                background: none !important;
                border: none !important;
                color: #5f6368 !important;
                cursor: pointer;
                font-size: 14px;
                padding: 12px 16px;
                border-radius: 4px;
                transition: all 0.2s;
            }
            
            .tools-btn:hover {
                background: #f1f3f4 !important;
                color: #202124 !important;
            }
            
            .tools-btn.active {
                background: #e8f0fe !important;
                color: #1a73e8 !important;
            }
            
            .search-tools-panel {
                background: white;
                border-bottom: 1px solid #dadce0;
                padding: 20px;
                display: none;
                max-height: 400px;
                overflow-y: auto;
            }
            
            .search-tools-panel.visible {
                display: block;
            }
            
            .tools-content {
                max-width: 1200px;
                margin: 0 auto;
            }
            
            .tools-section {
                margin-bottom: 25px;
            }
            
            .tools-section h4 {
                font-size: 14px;
                font-weight: 500;
                color: #202124;
                margin-bottom: 10px;
            }
            
            .filter-options {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .filter-btn {
                background: #f8f9fa;
                border: 1px solid #dadce0;
                border-radius: 16px;
                color: #3c4043;
                cursor: pointer;
                font-size: 13px;
                padding: 6px 12px;
                transition: all 0.2s;
                white-space: nowrap;
            }
            
            .filter-btn:hover {
                background: #f1f3f4;
                border-color: #5f6368;
            }
            
            .filter-btn.active {
                background: #1a73e8;
                border-color: #1a73e8;
                color: white;
            }
            
            .tools-actions {
                display: flex;
                gap: 10px;
                justify-content: flex-end;
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid #dadce0;
            }
            
            .btn-secondary {
                background: #f8f9fa;
                border: 1px solid #dadce0;
                color: #3c4043;
            }
            
            .btn-secondary:hover {
                background: #f1f3f4;
            }
            
            .btn-primary {
                background: #1a73e8;
                border: 1px solid #1a73e8;
                color: white;
            }
            
            .btn-primary:hover {
                background: #1557b0;
            }
            
            .active-filters {
                background: #e8f0fe;
                border: 1px solid #4285f4;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 10px 0;
                font-size: 13px;
                color: #1a73e8;
            }
            
            .filter-tag {
                background: #1a73e8;
                color: white;
                border-radius: 12px;
                padding: 2px 8px;
                margin: 0 4px;
                font-size: 12px;
                display: inline-block;
            }
            
            .filter-tag .remove {
                margin-left: 4px;
                cursor: pointer;
                font-weight: bold;
            }
            
            @media (max-width: 768px) {
                .search-tools-panel {
                    padding: 15px;
                }
                
                .filter-options {
                    gap: 6px;
                }
                
                .filter-btn {
                    font-size: 12px;
                    padding: 5px 10px;
                }
                
                .tools-actions {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    bindEvents() {
        // Toggle tools panel
        document.addEventListener('click', (e) => {
            if (e.target.id === 'search-tools-btn') {
                this.togglePanel();
            }
        });
        
        // Filter button clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-btn')) {
                this.handleFilterClick(e.target);
            }
        });
        
        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.id === 'clear-filters') {
                this.clearAllFilters();
            } else if (e.target.id === 'apply-tools-filters') {
                this.applyFilters();
            }
        });
        
        // Close panel when clicking outside
        document.addEventListener('click', (e) => {
            const panel = document.getElementById('search-tools-panel');
            const button = document.getElementById('search-tools-btn');
            
            if (this.isVisible && 
                !panel?.contains(e.target) && 
                !button?.contains(e.target)) {
                this.hidePanel();
            }
        });
    }
    
    togglePanel() {
        if (this.isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }
    
    showPanel() {
        const panel = document.getElementById('search-tools-panel');
        const button = document.getElementById('search-tools-btn');
        
        if (panel) {
            panel.classList.add('visible');
            this.isVisible = true;
        }
        
        if (button) {
            button.classList.add('active');
        }
    }
    
    hidePanel() {
        const panel = document.getElementById('search-tools-panel');
        const button = document.getElementById('search-tools-btn');
        
        if (panel) {
            panel.classList.remove('visible');
            this.isVisible = false;
        }
        
        if (button) {
            button.classList.remove('active');
        }
    }
    
    handleFilterClick(button) {
        const filterType = button.getAttribute('data-filter');
        const filterValue = button.getAttribute('data-value');
        
        // Remove active class from siblings
        const siblings = button.parentElement.querySelectorAll('.filter-btn');
        siblings.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to clicked button
        button.classList.add('active');
        
        // Update active filters
        this.activeFilters[filterType] = filterValue;
        
        // Show active filters
        this.updateActiveFiltersDisplay();
    }
    
    updateActiveFiltersDisplay() {
        let activeFiltersDiv = document.querySelector('.active-filters');
        
        // Remove existing display
        if (activeFiltersDiv) {
            activeFiltersDiv.remove();
        }
        
        // Check if any filters are active
        const hasActiveFilters = Object.values(this.activeFilters).some(value => value !== 'any');
        
        if (hasActiveFilters) {
            activeFiltersDiv = document.createElement('div');
            activeFiltersDiv.className = 'active-filters';
            
            let filtersText = 'Active filters: ';
            Object.entries(this.activeFilters).forEach(([type, value]) => {
                if (value !== 'any') {
                    filtersText += `<span class="filter-tag">${type}: ${value} <span class="remove" data-filter="${type}">×</span></span>`;
                }
            });
            
            activeFiltersDiv.innerHTML = filtersText;
            
            // Insert after results info
            const resultsInfo = document.getElementById('results-info');
            if (resultsInfo) {
                resultsInfo.insertAdjacentElement('afterend', activeFiltersDiv);
            }
        }
    }
    
    clearAllFilters() {
        // Reset all filters
        this.activeFilters = {
            time: 'any',
            type: 'any',
            language: 'any',
            region: 'any',
            usage: 'any'
        };
        
        // Update UI
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-value') === 'any') {
                btn.classList.add('active');
            }
        });
        
        // Remove active filters display
        const activeFiltersDiv = document.querySelector('.active-filters');
        if (activeFiltersDiv) {
            activeFiltersDiv.remove();
        }
    }
    
    applyFilters() {
        // Build filtered query
        const currentQuery = document.getElementById('search-input')?.value || '';
        const filteredQuery = this.buildFilteredQuery(currentQuery);
        
        // Perform search with filters
        if (window.resultsPage) {
            window.resultsPage.updateSearch(filteredQuery, 1, window.resultsPage.currentType);
        }
        
        // Hide panel
        this.hidePanel();
    }
    
    buildFilteredQuery(baseQuery) {
        let query = baseQuery;
        
        // Add file type filter
        if (this.activeFilters.type !== 'any') {
            query += ` filetype:${this.activeFilters.type}`;
        }
        
        // Add time filter (would need API support)
        if (this.activeFilters.time !== 'any') {
            // This would typically be handled by the search API
            console.log('Time filter applied:', this.activeFilters.time);
        }
        
        // Add language filter (would need API support)
        if (this.activeFilters.language !== 'any') {
            console.log('Language filter applied:', this.activeFilters.language);
        }
        
        // Add region filter (would need API support)
        if (this.activeFilters.region !== 'any') {
            console.log('Region filter applied:', this.activeFilters.region);
        }
        
        // Add usage rights filter (would need API support)
        if (this.activeFilters.usage !== 'any') {
            console.log('Usage rights filter applied:', this.activeFilters.usage);
        }
        
        return query.trim();
    }
    
    // Public methods
    getActiveFilters() {
        return { ...this.activeFilters };
    }
    
    setFilters(filters) {
        this.activeFilters = { ...this.activeFilters, ...filters };
        this.updateActiveFiltersDisplay();
    }
    
    hasActiveFilters() {
        return Object.values(this.activeFilters).some(value => value !== 'any');
    }
}

// Initialize search tools when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on results page
    if (window.location.pathname.includes('results.html')) {
        window.searchTools = new SearchTools();
    }
});

// Export for use in other modules
window.SearchTools = SearchTools;
