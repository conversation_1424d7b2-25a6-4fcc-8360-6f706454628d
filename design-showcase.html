<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Clone - Design Showcase</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/google-enhancements.css">
    <style>
        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .showcase-header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .showcase-title {
            font-size: 48px;
            font-weight: 300;
            color: #202124;
            margin-bottom: 20px;
        }
        
        .showcase-subtitle {
            font-size: 18px;
            color: #5f6368;
            margin-bottom: 40px;
        }
        
        .design-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }
        
        .design-card {
            background: white;
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            transition: all 0.3s ease;
        }
        
        .design-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .card-title {
            font-size: 24px;
            font-weight: 400;
            color: #202124;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .card-icon {
            font-size: 28px;
        }
        
        .card-description {
            color: #5f6368;
            line-height: 1.6;
            margin-bottom: 25px;
        }
        
        .demo-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .comparison-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 40px;
            margin: 60px 0;
        }
        
        .comparison-title {
            text-align: center;
            font-size: 32px;
            font-weight: 300;
            color: #202124;
            margin-bottom: 40px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        
        .comparison-item {
            background: white;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .comparison-item h3 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #202124;
        }
        
        .comparison-item.before h3 {
            color: #ea4335;
        }
        
        .comparison-item.after h3 {
            color: #34a853;
        }
        
        .feature-list {
            list-style: none;
            text-align: left;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list .check {
            color: #34a853;
            font-weight: bold;
        }
        
        .feature-list .cross {
            color: #ea4335;
            font-weight: bold;
        }
        
        .cta-section {
            text-align: center;
            background: linear-gradient(135deg, #4285f4, #34a853, #fbbc05, #ea4335);
            color: white;
            padding: 60px 40px;
            border-radius: 12px;
            margin: 60px 0;
        }
        
        .cta-title {
            font-size: 36px;
            font-weight: 300;
            margin-bottom: 20px;
        }
        
        .cta-description {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-button {
            background: white;
            color: #1a73e8;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .cta-button:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        @media (max-width: 768px) {
            .showcase-title {
                font-size: 36px;
            }
            
            .design-grid {
                grid-template-columns: 1fr;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .cta-section {
                padding: 40px 20px;
            }
            
            .cta-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <div class="showcase-header">
            <h1 class="showcase-title">🎨 Pixel-Perfect Google Clone</h1>
            <p class="showcase-subtitle">Enhanced with authentic Google design, animations, and user experience</p>
            
            <!-- Live Google Logo Demo -->
            <div class="google-logo">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </div>
        </div>
        
        <div class="design-grid">
            <div class="design-card">
                <h3 class="card-title">
                    <span class="card-icon">🎨</span>
                    Authentic Google Design
                </h3>
                <p class="card-description">
                    Pixel-perfect recreation of Google's interface with exact colors, typography, spacing, and visual elements.
                </p>
                <div class="demo-section">
                    <div class="search-container" style="max-width: 300px;">
                        <input type="text" class="search-box" placeholder="Try the search box" style="font-size: 14px; height: 36px;">
                        <div class="search-icons">
                            <div class="voice-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                                </svg>
                            </div>
                            <div class="search-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <ul class="feature-list">
                    <li><span class="check">✓</span> Google's exact color palette</li>
                    <li><span class="check">✓</span> Arial font family</li>
                    <li><span class="check">✓</span> Authentic spacing and sizing</li>
                    <li><span class="check">✓</span> Material Design shadows</li>
                </ul>
            </div>
            
            <div class="design-card">
                <h3 class="card-title">
                    <span class="card-icon">⚡</span>
                    Smooth Animations
                </h3>
                <p class="card-description">
                    Google-style animations and transitions that provide smooth, responsive user interactions.
                </p>
                <div class="demo-section">
                    <button class="btn" onclick="this.style.transform='scale(0.95)'; setTimeout(() => this.style.transform='scale(1)', 100)">
                        Google Search
                    </button>
                    <button class="btn" style="margin-left: 10px;">I'm Feeling Lucky</button>
                </div>
                <ul class="feature-list">
                    <li><span class="check">✓</span> Hover effects</li>
                    <li><span class="check">✓</span> Button ripple animations</li>
                    <li><span class="check">✓</span> Smooth transitions</li>
                    <li><span class="check">✓</span> Loading animations</li>
                </ul>
            </div>
            
            <div class="design-card">
                <h3 class="card-title">
                    <span class="card-icon">📱</span>
                    Responsive Design
                </h3>
                <p class="card-description">
                    Perfect adaptation to all screen sizes with Google's mobile-first approach and touch-friendly interactions.
                </p>
                <div class="demo-section">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <div style="width: 60px; height: 40px; background: #4285f4; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">Desktop</div>
                        <div style="width: 40px; height: 40px; background: #34a853; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">Tablet</div>
                        <div style="width: 30px; height: 40px; background: #fbbc05; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">Mobile</div>
                    </div>
                </div>
                <ul class="feature-list">
                    <li><span class="check">✓</span> Mobile-first design</li>
                    <li><span class="check">✓</span> Touch-friendly buttons</li>
                    <li><span class="check">✓</span> Flexible layouts</li>
                    <li><span class="check">✓</span> Optimized typography</li>
                </ul>
            </div>
            
            <div class="design-card">
                <h3 class="card-title">
                    <span class="card-icon">♿</span>
                    Accessibility
                </h3>
                <p class="card-description">
                    Full accessibility support with keyboard navigation, screen readers, and WCAG compliance.
                </p>
                <div class="demo-section">
                    <div style="display: flex; gap: 15px; align-items: center; font-size: 12px;">
                        <span style="background: #e8f5e8; padding: 4px 8px; border-radius: 4px; color: #2e7d32;">WCAG AA</span>
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 4px; color: #1565c0;">Keyboard Nav</span>
                        <span style="background: #fce4ec; padding: 4px 8px; border-radius: 4px; color: #c2185b;">Screen Reader</span>
                    </div>
                </div>
                <ul class="feature-list">
                    <li><span class="check">✓</span> Keyboard navigation</li>
                    <li><span class="check">✓</span> Screen reader support</li>
                    <li><span class="check">✓</span> High contrast support</li>
                    <li><span class="check">✓</span> Focus indicators</li>
                </ul>
            </div>
        </div>
        
        <div class="comparison-section">
            <h2 class="comparison-title">Before vs After Enhancement</h2>
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h3>❌ Before Enhancement</h3>
                    <ul class="feature-list">
                        <li><span class="cross">✗</span> Generic web fonts</li>
                        <li><span class="cross">✗</span> Basic CSS styling</li>
                        <li><span class="cross">✗</span> No animations</li>
                        <li><span class="cross">✗</span> Simple hover effects</li>
                        <li><span class="cross">✗</span> Basic responsive design</li>
                        <li><span class="cross">✗</span> Standard form elements</li>
                        <li><span class="cross">✗</span> No Google branding</li>
                    </ul>
                </div>
                
                <div class="comparison-item after">
                    <h3>✅ After Enhancement</h3>
                    <ul class="feature-list">
                        <li><span class="check">✓</span> Google's Arial font family</li>
                        <li><span class="check">✓</span> Pixel-perfect Google styling</li>
                        <li><span class="check">✓</span> Smooth Google animations</li>
                        <li><span class="check">✓</span> Material Design effects</li>
                        <li><span class="check">✓</span> Google's responsive patterns</li>
                        <li><span class="check">✓</span> Authentic Google components</li>
                        <li><span class="check">✓</span> Official Google logo & colors</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="cta-section">
            <h2 class="cta-title">Experience the Enhanced Google Clone</h2>
            <p class="cta-description">
                Try the pixel-perfect Google experience with all the visual enhancements and smooth interactions.
            </p>
            <a href="/" class="cta-button">Launch Google Clone</a>
        </div>
    </div>
    
    <script>
        // Add some interactive demos
        document.addEventListener('DOMContentLoaded', () => {
            // Animate the Google logo on load
            const logo = document.querySelector('.google-logo');
            if (logo) {
                logo.style.opacity = '0';
                logo.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    logo.style.transition = 'all 0.6s ease-out';
                    logo.style.opacity = '1';
                    logo.style.transform = 'translateY(0)';
                }, 200);
            }
            
            // Add hover effects to design cards
            const cards = document.querySelectorAll('.design-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-4px)';
                });
                
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
