/**
 * Modern CSS Reset
 * Based on modern best practices for cross-browser consistency
 */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/* Set core root defaults */
html {
  font-size: 16px;
  line-height: 1.4;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

html:focus-within {
  scroll-behavior: smooth;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  text-rendering: optimizeSpeed;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/* Make images easier to work with */
img,
picture,
svg {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

/* Remove default input styles */
input {
  border: none;
  outline: none;
  background: none;
}

/* Remove default textarea styles */
textarea {
  border: none;
  outline: none;
  background: none;
  resize: vertical;
}

/* Remove default select styles */
select {
  border: none;
  outline: none;
  background: none;
  appearance: none;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}

/* Remove default table styles */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove default hr styles */
hr {
  border: none;
  height: 1px;
  background-color: var(--border-primary);
  margin: var(--space-4) 0;
}

/* Remove default details/summary styles */
details {
  cursor: pointer;
}

summary {
  cursor: pointer;
  list-style: none;
}

summary::-webkit-details-marker {
  display: none;
}

/* Remove default mark styles */
mark {
  background-color: var(--google-yellow);
  color: var(--text-primary);
  padding: 0 var(--space-1);
}

/* Remove default code styles */
code,
pre {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

/* Remove default blockquote styles */
blockquote {
  margin: var(--space-4) 0;
  padding-left: var(--space-4);
  border-left: 4px solid var(--border-primary);
}

/* Remove default address styles */
address {
  font-style: normal;
}

/* Remove default abbr styles */
abbr[title] {
  text-decoration: underline dotted;
  cursor: help;
}

/* Remove default sub/sup styles */
sub,
sup {
  font-size: 0.75em;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Remove default small styles */
small {
  font-size: 0.875em;
}

/* Remove default strong/b styles */
strong,
b {
  font-weight: var(--font-weight-semibold);
}

/* Remove default em/i styles */
em,
i {
  font-style: italic;
}

/* Remove default del styles */
del {
  text-decoration: line-through;
}

/* Remove default ins styles */
ins {
  text-decoration: underline;
}

/* Focus styles */
:focus {
  outline: 2px solid var(--google-blue);
  outline-offset: var(--focus-ring-offset);
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--google-blue);
  outline-offset: var(--focus-ring-offset);
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor;
  }
}

/* Print styles */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]::after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  
  pre {
    white-space: pre-wrap !important;
  }
  
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  
  thead {
    display: table-header-group;
  }
  
  tr,
  img {
    page-break-inside: avoid;
  }
  
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}
