/**
 * Base Component Class
 * Provides common functionality for all UI components
 */

import logger from '../utils/logger.js';
import dom from '../utils/dom.js';
import { APP_CONFIG } from '../config/app.config.js';

export class BaseComponent {
  constructor(element, options = {}) {
    this.element = element;
    this.options = { ...this.getDefaultOptions(), ...options };
    this.logger = logger.child(this.constructor.name);
    this.eventListeners = [];
    this.children = new Map();
    this.state = {};
    this.isInitialized = false;
    this.isDestroyed = false;
    
    // Bind methods to preserve context
    this.handleEvent = this.handleEvent.bind(this);
    this.render = this.render.bind(this);
    this.destroy = this.destroy.bind(this);
  }
  
  /**
   * Get default options for the component
   * @returns {Object} Default options
   */
  getDefaultOptions() {
    return {
      autoInit: true,
      className: '',
      attributes: {},
      events: {}
    };
  }
  
  /**
   * Initialize the component
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isInitialized || this.isDestroyed) {
      return;
    }
    
    try {
      this.logger.debug('Initializing component');
      
      // Set up element
      this.setupElement();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Initialize child components
      await this.initializeChildren();
      
      // Render initial state
      await this.render();
      
      // Mark as initialized
      this.isInitialized = true;
      
      // Emit initialized event
      this.emit('initialized');
      
      this.logger.debug('Component initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize component', error);
      throw error;
    }
  }
  
  /**
   * Set up the DOM element
   */
  setupElement() {
    if (!this.element) {
      throw new Error('Component element is required');
    }
    
    // Add component class
    if (this.options.className) {
      dom.addClass(this.element, this.options.className);
    }
    
    // Set attributes
    Object.entries(this.options.attributes).forEach(([key, value]) => {
      this.element.setAttribute(key, value);
    });
    
    // Store component reference
    this.element._component = this;
  }
  
  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Set up option-defined events
    Object.entries(this.options.events).forEach(([event, handler]) => {
      this.addEventListener(event, handler);
    });
    
    // Set up default events
    this.setupDefaultEvents();
  }
  
  /**
   * Set up default event listeners (override in subclasses)
   */
  setupDefaultEvents() {
    // Override in subclasses
  }
  
  /**
   * Initialize child components
   * @returns {Promise<void>}
   */
  async initializeChildren() {
    // Override in subclasses to initialize child components
  }
  
  /**
   * Add event listener with automatic cleanup
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   * @param {Object} options - Event options
   */
  addEventListener(event, handler, options = {}) {
    if (!this.element || typeof handler !== 'function') {
      return;
    }
    
    const cleanup = dom.addEventListener(this.element, event, handler, options);
    this.eventListeners.push(cleanup);
  }
  
  /**
   * Generic event handler
   * @param {Event} event - DOM event
   */
  handleEvent(event) {
    const methodName = `on${event.type.charAt(0).toUpperCase()}${event.type.slice(1)}`;
    
    if (typeof this[methodName] === 'function') {
      this[methodName](event);
    }
  }
  
  /**
   * Emit custom event
   * @param {string} eventType - Event type
   * @param {any} detail - Event detail data
   */
  emit(eventType, detail = null) {
    if (!this.element) return;
    
    const event = new CustomEvent(eventType, {
      detail,
      bubbles: true,
      cancelable: true
    });
    
    this.element.dispatchEvent(event);
    this.logger.debug(`Emitted event: ${eventType}`, detail);
  }
  
  /**
   * Update component state
   * @param {Object} newState - New state data
   * @param {boolean} shouldRender - Whether to re-render
   */
  setState(newState, shouldRender = true) {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...newState };
    
    this.logger.debug('State updated', { oldState, newState: this.state });
    
    if (shouldRender) {
      this.render();
    }
    
    this.emit('stateChanged', { oldState, newState: this.state });
  }
  
  /**
   * Get component state
   * @returns {Object} Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Render the component (override in subclasses)
   * @returns {Promise<void>}
   */
  async render() {
    // Override in subclasses
    this.logger.debug('Rendering component');
  }
  
  /**
   * Show the component
   * @param {string} display - Display value
   */
  show(display = 'block') {
    if (this.element) {
      dom.show(this.element, display);
      this.emit('shown');
    }
  }
  
  /**
   * Hide the component
   */
  hide() {
    if (this.element) {
      dom.hide(this.element);
      this.emit('hidden');
    }
  }
  
  /**
   * Toggle component visibility
   * @returns {boolean} Whether component is now visible
   */
  toggle() {
    if (!this.element) return false;
    
    const isVisible = dom.isVisible(this.element);
    
    if (isVisible) {
      this.hide();
    } else {
      this.show();
    }
    
    return !isVisible;
  }
  
  /**
   * Add child component
   * @param {string} name - Child name
   * @param {BaseComponent} component - Child component
   */
  addChild(name, component) {
    this.children.set(name, component);
    this.logger.debug(`Added child component: ${name}`);
  }
  
  /**
   * Get child component
   * @param {string} name - Child name
   * @returns {BaseComponent|null} Child component
   */
  getChild(name) {
    return this.children.get(name) || null;
  }
  
  /**
   * Remove child component
   * @param {string} name - Child name
   */
  removeChild(name) {
    const child = this.children.get(name);
    if (child) {
      child.destroy();
      this.children.delete(name);
      this.logger.debug(`Removed child component: ${name}`);
    }
  }
  
  /**
   * Destroy the component and clean up resources
   */
  destroy() {
    if (this.isDestroyed) {
      return;
    }
    
    this.logger.debug('Destroying component');
    
    // Destroy child components
    this.children.forEach((child, name) => {
      this.removeChild(name);
    });
    
    // Remove event listeners
    this.eventListeners.forEach(cleanup => cleanup());
    this.eventListeners = [];
    
    // Remove component reference
    if (this.element) {
      delete this.element._component;
    }
    
    // Mark as destroyed
    this.isDestroyed = true;
    this.isInitialized = false;
    
    // Emit destroyed event
    this.emit('destroyed');
    
    this.logger.debug('Component destroyed');
  }
  
  /**
   * Check if component is initialized
   * @returns {boolean} Whether component is initialized
   */
  isReady() {
    return this.isInitialized && !this.isDestroyed;
  }
  
  /**
   * Get component info for debugging
   * @returns {Object} Component info
   */
  getDebugInfo() {
    return {
      name: this.constructor.name,
      isInitialized: this.isInitialized,
      isDestroyed: this.isDestroyed,
      state: this.state,
      options: this.options,
      childrenCount: this.children.size,
      eventListenersCount: this.eventListeners.length
    };
  }
}

export default BaseComponent;
