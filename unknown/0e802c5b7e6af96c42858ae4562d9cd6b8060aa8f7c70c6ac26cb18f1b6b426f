/**
 * Results Component Styles
 * Search results display with animations and interactions
 */

.results-component {
  width: 100%;
  max-width: none;
}

/* Results Info */
.results-info {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-5);
  padding-left: var(--space-3);
  line-height: var(--line-height-normal);
}

/* Results Container */
.results-container {
  width: 100%;
}

/* Individual Result Item */
.result-item {
  margin-bottom: var(--result-item-margin);
  max-width: var(--content-max-width);
  padding-left: var(--space-3);
  animation: fadeInUp var(--duration-normal) var(--ease-out) both;
  position: relative;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Result URL */
.result-url {
  color: var(--text-primary);
  font-size: var(--result-url-size);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  line-height: var(--line-height-tight);
  position: relative;
}

.result-url .favicon {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background: var(--bg-tertiary);
  flex-shrink: 0;
}

.result-url .breadcrumb {
  color: var(--text-primary);
  font-size: var(--result-url-size);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-menu {
  width: 20px;
  height: 20px;
  padding: var(--space-1);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  opacity: 0;
  margin-left: auto;
}

.result-item:hover .result-menu {
  opacity: 1;
}

.result-menu:hover {
  background-color: var(--bg-hover);
}

.result-menu svg {
  width: 12px;
  height: 12px;
  fill: var(--text-disabled);
}

/* Result Title */
.result-title {
  color: var(--text-link);
  font-size: var(--result-title-size);
  text-decoration: none;
  display: block;
  margin-bottom: var(--space-1);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-normal);
  transition: var(--transition-fast);
  cursor: pointer;
}

.result-title:hover {
  text-decoration: underline;
  color: var(--text-link-hover);
}

.result-title:visited {
  color: var(--text-link-visited);
}

.result-title:focus {
  outline: 2px solid var(--google-blue);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Result Snippet */
.result-snippet {
  color: var(--text-secondary);
  font-size: var(--result-snippet-size);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-2);
}

.result-snippet em {
  font-style: normal;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* Result Actions */
.result-actions {
  display: flex;
  gap: var(--space-2);
  opacity: 0;
  transition: var(--transition-fast);
}

.result-item:hover .result-actions {
  opacity: 1;
}

.action-btn {
  padding: var(--space-1);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: var(--bg-hover);
}

.action-btn svg {
  width: 16px;
  height: 16px;
  fill: var(--text-disabled);
}

.action-btn:hover svg {
  fill: var(--text-secondary);
}

/* Loading State */
.loading {
  text-align: center;
  padding: var(--space-10);
  color: var(--text-secondary);
}

.spinner {
  border: 2px solid var(--bg-tertiary);
  border-top: 2px solid var(--google-blue);
  border-radius: var(--radius-full);
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-5);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

/* Error State */
.error-state {
  text-align: center;
  padding: var(--space-10);
  max-width: 400px;
  margin: 0 auto;
}

.error-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-4);
  color: var(--google-red);
}

.error-icon svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.error-state h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  font-weight: var(--font-weight-normal);
}

.error-state p {
  color: var(--text-secondary);
  margin-bottom: var(--space-5);
  line-height: var(--line-height-relaxed);
}

.retry-btn {
  background: var(--google-blue);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: var(--transition-fast);
}

.retry-btn:hover {
  background: var(--google-blue);
  filter: brightness(0.9);
}

/* No Results State */
.no-results {
  text-align: center;
  padding: var(--space-10);
  max-width: 500px;
  margin: 0 auto;
}

.no-results-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-4);
  color: var(--text-disabled);
}

.no-results-icon svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.no-results h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  font-weight: var(--font-weight-normal);
}

.no-results > p {
  color: var(--text-secondary);
  margin-bottom: var(--space-5);
  line-height: var(--line-height-relaxed);
}

.suggestions {
  text-align: left;
  background: var(--bg-secondary);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  margin-top: var(--space-4);
}

.suggestions p {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

.suggestions ul {
  list-style: none;
  padding: 0;
}

.suggestions li {
  padding: var(--space-1) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.suggestions li::before {
  content: "•";
  color: var(--google-blue);
  margin-right: var(--space-2);
}

/* Pagination */
.pagination {
  margin-top: var(--space-10);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0;
  padding-left: var(--space-3);
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.page-btn {
  padding: var(--space-3) var(--space-4);
  border: none;
  background: none;
  color: var(--google-blue);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.page-btn:hover {
  background: var(--bg-tertiary);
}

.page-btn:disabled {
  color: var(--text-disabled);
  cursor: not-allowed;
}

.page-number {
  padding: var(--space-3) var(--space-4);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  text-decoration: none;
  min-width: 44px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  background: var(--bg-tertiary);
}

.page-number.current {
  background: var(--google-blue);
  color: white;
}

.google-logo-pagination {
  margin: 0 var(--space-5);
}

.google-logo-pagination svg {
  width: 66px;
  height: 22px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .results-info {
    padding-left: 0;
    margin-bottom: var(--space-4);
  }
  
  .result-item {
    padding-left: 0;
    margin-bottom: var(--space-6);
  }
  
  .result-title {
    font-size: var(--font-size-lg);
  }
  
  .pagination {
    padding-left: 0;
    justify-content: center;
  }
  
  .pagination-nav {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .google-logo-pagination {
    order: -1;
    width: 100%;
    text-align: center;
    margin: 0 0 var(--space-3) 0;
  }
}

@media (max-width: 480px) {
  .result-title {
    font-size: var(--font-size-md);
  }
  
  .result-snippet {
    font-size: var(--font-size-sm);
  }
  
  .page-btn,
  .page-number {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
    min-width: 36px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .result-title {
    color: var(--text-primary);
    text-decoration: underline;
  }
  
  .result-title:visited {
    color: var(--text-secondary);
  }
  
  .result-snippet em {
    background-color: yellow;
    color: black;
    padding: 0 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .result-item {
    animation: none;
  }
  
  .spinner {
    animation: none;
    border-top-color: var(--text-disabled);
  }
  
  .result-title,
  .page-btn,
  .page-number,
  .action-btn,
  .result-menu {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .result-actions,
  .result-menu,
  .pagination,
  .loading,
  .error-state .retry-btn {
    display: none;
  }
  
  .result-item {
    break-inside: avoid;
    margin-bottom: var(--space-4);
  }
  
  .result-title {
    color: black;
    text-decoration: none;
  }
  
  .result-title::after {
    content: " (" attr(href) ")";
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}
