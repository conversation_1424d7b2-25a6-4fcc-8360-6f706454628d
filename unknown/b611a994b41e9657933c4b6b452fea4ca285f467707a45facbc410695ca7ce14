/**
 * Results Component
 * Manages search results display, pagination, and interactions
 */

import BaseComponent from './base.component.js';
import apiService from '../services/api.service.js';
import { APP_CONFIG } from '../config/app.config.js';
import dom, { createElement } from '../utils/dom.js';

export class ResultsComponent extends BaseComponent {
  getDefaultOptions() {
    return {
      ...super.getDefaultOptions(),
      resultsPerPage: APP_CONFIG.SEARCH.RESULTS_PER_PAGE,
      enablePagination: true,
      enableFilters: true,
      animateResults: true,
      className: 'results-component'
    };
  }

  async init() {
    await super.init();
    this.setupContainers();
    this.setupEventListeners();
    this.initializeState();
  }

  setupContainers() {
    // Find or create result containers
    this.resultsInfo = this.element.querySelector('.results-info') ||
                      this.createResultsInfo();
    this.resultsContainer = this.element.querySelector('.results-container') ||
                           this.createResultsContainer();
    this.paginationContainer = this.element.querySelector('.pagination') ||
                              this.createPaginationContainer();
    this.loadingElement = this.element.querySelector('.loading') ||
                         this.createLoadingElement();
  }

  createResultsInfo() {
    const info = createElement('div', {
      className: 'results-info',
      role: 'status',
      'aria-live': 'polite'
    });
    this.element.appendChild(info);
    return info;
  }

  createResultsContainer() {
    const container = createElement('div', {
      className: 'results-container',
      role: 'main',
      'aria-label': 'Search results'
    });
    this.element.appendChild(container);
    return container;
  }

  createPaginationContainer() {
    const pagination = createElement('div', {
      className: 'pagination',
      role: 'navigation',
      'aria-label': 'Search results pagination'
    });
    this.element.appendChild(pagination);
    return pagination;
  }

  createLoadingElement() {
    const loading = createElement('div', {
      className: 'loading',
      style: 'display: none;',
      'aria-hidden': 'true'
    });

    loading.innerHTML = `
      <div class="spinner"></div>
      <div class="loading-text">Searching...</div>
    `;

    this.element.appendChild(loading);
    return loading;
  }

  initializeState() {
    this.setState({
      results: [],
      totalResults: 0,
      currentPage: 1,
      query: '',
      searchType: 'web',
      isLoading: false,
      error: null
    });
  }

  setupDefaultEvents() {
    // Set up result item click tracking
    this.addEventListener('click', this.handleResultClick.bind(this));

    // Set up pagination clicks
    this.addEventListener('click', this.handlePaginationClick.bind(this));
  }

  /**
   * Perform search and display results
   * @param {string} query - Search query
   * @param {Object} options - Search options
   */
  async search(query, options = {}) {
    const searchOptions = {
      start: 1,
      type: 'web',
      ...options
    };

    try {
      this.setState({
        isLoading: true,
        error: null,
        query,
        searchType: searchOptions.type,
        currentPage: Math.ceil(searchOptions.start / this.options.resultsPerPage)
      });

      this.showLoading();

      this.logger.info('Performing search', { query, options: searchOptions });

      const results = await apiService.search(query, searchOptions);

      this.setState({
        results: results.items || [],
        totalResults: results.totalResults || 0,
        searchTime: results.searchTime || 0,
        isLoading: false
      });

      await this.render();
      this.emit('searchComplete', { query, results, options: searchOptions });

    } catch (error) {
      this.logger.error('Search failed', error);
      this.setState({
        isLoading: false,
        error: error.message || 'Search failed'
      });
      this.showError(error);
    }
  }

  /**
   * Render the results
   */
  async render() {
    const { results, totalResults, searchTime, query, isLoading, error } = this.state;

    this.hideLoading();

    if (error) {
      this.showError(error);
      return;
    }

    if (isLoading) {
      this.showLoading();
      return;
    }

    // Update results info
    this.updateResultsInfo(totalResults, searchTime);

    // Render results
    await this.renderResults(results, query);

    // Update pagination
    this.renderPagination();
  }

  updateResultsInfo(totalResults, searchTime) {
    if (!this.resultsInfo) return;

    const formattedTotal = totalResults.toLocaleString();
    const formattedTime = searchTime.toFixed(3);

    this.resultsInfo.textContent = `About ${formattedTotal} results (${formattedTime} seconds)`;
  }

  async renderResults(results, query) {
    if (!this.resultsContainer) return;

    // Clear previous results
    this.resultsContainer.innerHTML = '';

    if (!results || results.length === 0) {
      this.showNoResults();
      return;
    }

    // Create result items
    const fragment = document.createDocumentFragment();

    results.forEach((item, index) => {
      const resultElement = this.createResultItem(item, index, query);
      fragment.appendChild(resultElement);
    });

    this.resultsContainer.appendChild(fragment);

    // Animate results if enabled
    if (this.options.animateResults) {
      this.animateResults();
    }
  }

  createResultItem(item, index, query) {
    const resultDiv = createElement('div', {
      className: 'result-item',
      dataset: { index },
      style: this.options.animateResults ? `animation-delay: ${index * 0.1}s` : ''
    });

    const domain = this.extractDomain(item.link);
    const highlightedSnippet = this.highlightSearchTerms(item.snippet || '', query);
    const favicon = this.getFaviconUrl(domain);

    resultDiv.innerHTML = `
      <div class="result-url">
        <img class="favicon" src="${favicon}" alt="" onerror="this.style.display='none'">
        <span class="breadcrumb">${this.escapeHtml(domain)}</span>
        <button class="result-menu" aria-label="More options" title="More options">
          <svg viewBox="0 0 24 24">
            <path d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"/>
          </svg>
        </button>
      </div>
      <a href="${this.escapeHtml(item.link)}"
         class="result-title"
         target="_blank"
         rel="noopener"
         data-result-index="${index}">
        ${this.escapeHtml(item.title)}
      </a>
      <div class="result-snippet">${highlightedSnippet}</div>
      ${this.createResultActions(item)}
    `;

    return resultDiv;
  }

  createResultActions(item) {
    return `
      <div class="result-actions">
        <button class="action-btn" data-action="save" title="Save result">
          <svg viewBox="0 0 24 24">
            <path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"/>
          </svg>
        </button>
        <button class="action-btn" data-action="share" title="Share result">
          <svg viewBox="0 0 24 24">
            <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A1.92,1.92 0 0,0 19,17.08C18.67,17.08 18.34,17.13 18.04,17.23L18,16.08Z"/>
          </svg>
        </button>
      </div>
    `;
  }

  renderPagination() {
    if (!this.paginationContainer || !this.options.enablePagination) return;

    const { currentPage, totalResults } = this.state;
    const totalPages = Math.min(10, Math.ceil(totalResults / this.options.resultsPerPage));

    if (totalPages <= 1) {
      this.paginationContainer.innerHTML = '';
      return;
    }

    const paginationNav = createElement('div', {
      className: 'pagination-nav'
    });

    // Previous button
    if (currentPage > 1) {
      const prevBtn = this.createPageButton('< Previous', currentPage - 1);
      paginationNav.appendChild(prevBtn);
    }

    // Google logo
    const logoDiv = createElement('div', {
      className: 'google-logo-pagination'
    });
    logoDiv.innerHTML = this.getGoogleLogoSVG();
    paginationNav.appendChild(logoDiv);

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
      const pageBtn = this.createPageNumber(i.toString(), i, i === currentPage);
      paginationNav.appendChild(pageBtn);
    }

    // Next button
    if (currentPage < totalPages) {
      const nextBtn = this.createPageButton('Next >', currentPage + 1);
      paginationNav.appendChild(nextBtn);
    }

    this.paginationContainer.innerHTML = '';
    this.paginationContainer.appendChild(paginationNav);
  }

  createPageButton(text, page) {
    const button = createElement('button', {
      className: 'page-btn',
      dataset: { page },
      'aria-label': `Go to ${text.toLowerCase()}`
    }, text);

    return button;
  }

  createPageNumber(text, page, isActive = false) {
    const link = createElement('a', {
      className: `page-number ${isActive ? 'current' : ''}`,
      href: '#',
      dataset: { page },
      'aria-label': `Page ${text}`,
      'aria-current': isActive ? 'page' : null
    }, text);

    return link;
  }

  animateResults() {
    const resultItems = this.resultsContainer.querySelectorAll('.result-item');

    resultItems.forEach((item, index) => {
      item.style.opacity = '0';
      item.style.transform = 'translateY(20px)';

      setTimeout(() => {
        item.style.transition = 'all 0.3s ease-out';
        item.style.opacity = '1';
        item.style.transform = 'translateY(0)';
      }, index * 100);
    });
  }

  showLoading() {
    if (this.loadingElement) {
      dom.show(this.loadingElement);
    }
    if (this.resultsContainer) {
      dom.hide(this.resultsContainer);
    }
  }

  hideLoading() {
    if (this.loadingElement) {
      dom.hide(this.loadingElement);
    }
    if (this.resultsContainer) {
      dom.show(this.resultsContainer);
    }
  }

  showError(error) {
    if (!this.resultsContainer) return;

    const errorMessage = typeof error === 'string' ? error :
                        error.message || 'An error occurred while searching';

    this.resultsContainer.innerHTML = `
      <div class="error-state">
        <div class="error-icon">
          <svg viewBox="0 0 24 24">
            <path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/>
          </svg>
        </div>
        <h3>Something went wrong</h3>
        <p>${this.escapeHtml(errorMessage)}</p>
        <button class="retry-btn" onclick="window.location.reload()">
          Try again
        </button>
      </div>
    `;
  }

  showNoResults() {
    if (!this.resultsContainer) return;

    this.resultsContainer.innerHTML = `
      <div class="no-results">
        <div class="no-results-icon">
          <svg viewBox="0 0 24 24">
            <path d="M15.5,14H14.71L14.43,13.73C15.41,12.59 16,11.11 16,9.5A6.5,6.5 0 0,0 9.5,3A6.5,6.5 0 0,0 3,9.5A6.5,6.5 0 0,0 9.5,16C11.11,16 12.59,15.41 13.73,14.43L14,14.71V15.5L19,20.49L20.49,19L15.5,14M9.5,14C7.01,14 5,11.99 5,9.5C5,7.01 7.01,5 9.5,5C11.99,5 14,7.01 14,9.5C14,11.99 11.99,14 9.5,14Z"/>
          </svg>
        </div>
        <h3>No results found</h3>
        <p>Try different keywords or check your spelling</p>
        <div class="suggestions">
          <p>Suggestions:</p>
          <ul>
            <li>Make sure all words are spelled correctly</li>
            <li>Try different keywords</li>
            <li>Try more general keywords</li>
            <li>Try fewer keywords</li>
          </ul>
        </div>
      </div>
    `;
  }

  // Event Handlers
  handleResultClick(event) {
    const resultTitle = event.target.closest('.result-title');
    if (resultTitle) {
      const index = resultTitle.dataset.resultIndex;
      const result = this.state.results[index];

      this.logger.userAction('Result Click', {
        query: this.state.query,
        resultIndex: index,
        resultUrl: result?.link,
        resultTitle: result?.title
      });

      this.emit('resultClick', { result, index, query: this.state.query });
    }
  }

  handlePaginationClick(event) {
    event.preventDefault();

    const pageBtn = event.target.closest('[data-page]');
    if (pageBtn) {
      const page = parseInt(pageBtn.dataset.page);
      const start = (page - 1) * this.options.resultsPerPage + 1;

      this.emit('pageChange', {
        page,
        start,
        query: this.state.query,
        type: this.state.searchType
      });
    }
  }

  // Utility Methods
  extractDomain(url) {
    try {
      return new URL(url).hostname.replace('www.', '');
    } catch {
      return url;
    }
  }

  getFaviconUrl(domain) {
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
  }

  highlightSearchTerms(text, query) {
    if (!query || query.length < 2) return this.escapeHtml(text);

    const terms = query.split(' ').filter(term => term.length > 1);
    let highlightedText = this.escapeHtml(text);

    terms.forEach(term => {
      const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`(${escapedTerm})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<em>$1</em>');
    });

    return highlightedText;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  getGoogleLogoSVG() {
    return `
      <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0)">
          <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
          <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
          <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
          <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
          <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
          <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
        </g>
        <defs>
          <clipPath id="clip0">
            <rect width="272" height="92" fill="white"/>
          </clipPath>
        </defs>
      </svg>
    `;
  }

  // Public API
  getCurrentResults() {
    return this.state.results;
  }

  getCurrentQuery() {
    return this.state.query;
  }

  getCurrentPage() {
    return this.state.currentPage;
  }

  getTotalResults() {
    return this.state.totalResults;
  }

  clearResults() {
    this.setState({
      results: [],
      totalResults: 0,
      query: '',
      currentPage: 1
    });

    if (this.resultsContainer) {
      this.resultsContainer.innerHTML = '';
    }

    if (this.resultsInfo) {
      this.resultsInfo.textContent = '';
    }

    if (this.paginationContainer) {
      this.paginationContainer.innerHTML = '';
    }
  }
}

export default ResultsComponent;