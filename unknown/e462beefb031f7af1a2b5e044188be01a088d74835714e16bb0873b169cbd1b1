<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Status Check</title>
    <style>
        body {
            font-family: arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background: #3367d6;
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .config-display {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #b3d9ff;
            margin: 15px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Google Custom Search API Status</h1>
        <p>This page tests the Google Custom Search API configuration and connectivity.</p>
        
        <div class="config-display">
            <h3>📋 API Configuration</h3>
            <div id="config-info"></div>
        </div>
        
        <div class="test-section">
            <h3>🧪 API Tests</h3>
            <button class="test-button" onclick="testBasicConnectivity()">Test Basic Connectivity</button>
            <button class="test-button" onclick="testSimpleSearch()">Test Simple Search</button>
            <button class="test-button" onclick="testWithTimeout()">Test with Timeout</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="test-results"></div>
        
        <div class="test-section">
            <h3>📄 Raw Response</h3>
            <pre id="raw-response">No test run yet...</pre>
        </div>
        
        <div class="test-section">
            <h3>🐛 Debug Log</h3>
            <pre id="debug-log">Debug information will appear here...</pre>
        </div>
    </div>
    
    <script>
        // Configuration
        const API_CONFIG = {
            GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
            SEARCH_ENGINE_ID: '61201925358ea4e83',
            BASE_URL: 'https://www.googleapis.com/customsearch/v1'
        };
        
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            debugLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateDebugLog();
            console.log(`[API Test] ${message}`);
        }
        
        function updateDebugLog() {
            document.getElementById('debug-log').textContent = debugLog.slice(-20).join('\n');
        }
        
        function showStatus(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function showLoading(message) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `<div class="status info"><span class="loading"></span>${message}</div>`;
        }
        
        function displayConfig() {
            const configDiv = document.getElementById('config-info');
            
            const apiKeyDisplay = API_CONFIG.GOOGLE_API_KEY ? 
                API_CONFIG.GOOGLE_API_KEY.substring(0, 15) + '...' : 'Not set';
            
            const searchEngineIdDisplay = API_CONFIG.SEARCH_ENGINE_ID ? 
                API_CONFIG.SEARCH_ENGINE_ID.substring(0, 15) + '...' : 'Not set';
            
            configDiv.innerHTML = `
                <p><strong>API Key:</strong> ${apiKeyDisplay}</p>
                <p><strong>Search Engine ID:</strong> ${searchEngineIdDisplay}</p>
                <p><strong>Base URL:</strong> ${API_CONFIG.BASE_URL}</p>
                <p><strong>Browser:</strong> ${navigator.userAgent.split(' ')[0]}</p>
                <p><strong>Fetch API:</strong> ${typeof fetch !== 'undefined' ? '✅ Available' : '❌ Not available'}</p>
            `;
        }
        
        async function testBasicConnectivity() {
            log('Starting basic connectivity test');
            showLoading('Testing basic API connectivity...');
            
            try {
                // Test with minimal parameters
                const testUrl = `${API_CONFIG.BASE_URL}?key=${API_CONFIG.GOOGLE_API_KEY}&cx=${API_CONFIG.SEARCH_ENGINE_ID}&q=test&num=1`;
                
                log(`Making request to: ${testUrl.substring(0, 100)}...`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
                
                const startTime = performance.now();
                
                const response = await fetch(testUrl, {
                    signal: controller.signal,
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                clearTimeout(timeoutId);
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                log(`Response received in ${responseTime}ms with status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('API response parsed successfully');
                    
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                    
                    const totalResults = data.searchInformation?.totalResults || 'Unknown';
                    const searchTime = data.searchInformation?.searchTime || 'Unknown';
                    
                    showStatus(`✅ API Working! Found ${totalResults} results in ${searchTime}s (Response time: ${responseTime}ms)`, 'success');
                } else {
                    const errorData = await response.json();
                    log(`API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                    
                    document.getElementById('raw-response').textContent = JSON.stringify(errorData, null, 2);
                    
                    showStatus(`❌ API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                }
                
            } catch (error) {
                log(`Connectivity test failed: ${error.message}`, 'error');
                
                if (error.name === 'AbortError') {
                    showStatus('❌ Request timed out after 10 seconds', 'error');
                } else if (error.message.includes('CORS')) {
                    showStatus('❌ CORS error - API might not allow browser requests', 'error');
                } else if (error.message.includes('network')) {
                    showStatus('❌ Network error - Check internet connection', 'error');
                } else {
                    showStatus(`❌ Error: ${error.message}`, 'error');
                }
                
                document.getElementById('raw-response').textContent = `Error: ${error.message}\nStack: ${error.stack}`;
            }
        }
        
        async function testSimpleSearch() {
            log('Starting simple search test');
            showLoading('Testing search with query "javascript"...');
            
            try {
                const params = new URLSearchParams({
                    key: API_CONFIG.GOOGLE_API_KEY,
                    cx: API_CONFIG.SEARCH_ENGINE_ID,
                    q: 'javascript',
                    num: 3
                });
                
                const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                log(`Search URL: ${url.substring(0, 100)}...`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout
                
                const startTime = performance.now();
                
                const response = await fetch(url, {
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                log(`Search response received in ${responseTime}ms with status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('Search response parsed successfully');
                    
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                    
                    if (data.items && data.items.length > 0) {
                        const totalResults = data.searchInformation?.totalResults || 'Unknown';
                        const searchTime = data.searchInformation?.searchTime || 'Unknown';
                        const itemCount = data.items.length;
                        
                        showStatus(`✅ Search Working! Found ${totalResults} results in ${searchTime}s, showing ${itemCount} items (Response time: ${responseTime}ms)`, 'success');
                    } else {
                        showStatus('⚠️ Search returned no results', 'warning');
                    }
                } else {
                    const errorData = await response.json();
                    log(`Search error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                    
                    document.getElementById('raw-response').textContent = JSON.stringify(errorData, null, 2);
                    
                    showStatus(`❌ Search Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                }
                
            } catch (error) {
                log(`Search test failed: ${error.message}`, 'error');
                
                if (error.name === 'AbortError') {
                    showStatus('❌ Search timed out after 15 seconds', 'error');
                } else {
                    showStatus(`❌ Search Error: ${error.message}`, 'error');
                }
                
                document.getElementById('raw-response').textContent = `Error: ${error.message}\nStack: ${error.stack}`;
            }
        }
        
        async function testWithTimeout() {
            log('Starting timeout test');
            showLoading('Testing with 5 second timeout...');
            
            try {
                const params = new URLSearchParams({
                    key: API_CONFIG.GOOGLE_API_KEY,
                    cx: API_CONFIG.SEARCH_ENGINE_ID,
                    q: 'timeout test',
                    num: 1
                });
                
                const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                log(`Timeout test URL: ${url.substring(0, 100)}...`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    log('Aborting request due to timeout', 'warning');
                    controller.abort();
                }, 5000); // 5 second timeout
                
                const startTime = performance.now();
                
                const response = await fetch(url, {
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                log(`Timeout test completed in ${responseTime}ms`);
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                    showStatus(`✅ Request completed within timeout (${responseTime}ms)`, 'success');
                } else {
                    const errorData = await response.json();
                    document.getElementById('raw-response').textContent = JSON.stringify(errorData, null, 2);
                    showStatus(`❌ API Error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`Timeout test failed: ${error.message}`, 'error');
                
                if (error.name === 'AbortError') {
                    showStatus('⏰ Request was aborted due to 5 second timeout', 'warning');
                } else {
                    showStatus(`❌ Timeout Test Error: ${error.message}`, 'error');
                }
                
                document.getElementById('raw-response').textContent = `Error: ${error.message}`;
            }
        }
        
        function clearResults() {
            log('Clearing test results');
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('raw-response').textContent = 'No test run yet...';
            debugLog = [];
            updateDebugLog();
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            log('API Status page loaded');
            displayConfig();
            
            // Auto-run basic test
            setTimeout(() => {
                log('Auto-running basic connectivity test');
                testBasicConnectivity();
            }, 1000);
        });
        
        // Global error handler
        window.addEventListener('error', (e) => {
            log(`Global error: ${e.message}`, 'error');
        });
        
        log('API Status page script loaded successfully');
    </script>
</body>
</html>
