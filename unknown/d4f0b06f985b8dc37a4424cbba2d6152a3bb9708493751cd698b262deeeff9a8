<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fast Search Results - Google Clone</title>
    <style>
        body {
            font-family: arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #fff;
        }
        
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid #dadce0;
            background: #fff;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }
        
        .logo {
            color: #4285f4;
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
        }
        
        .search-container {
            flex: 1;
            max-width: 584px;
        }
        
        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 16px;
            font-size: 16px;
            outline: none;
        }
        
        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }
        
        .search-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .nav-tabs {
            background: #fff;
            padding: 0 20px;
        }
        
        .nav-content {
            display: flex;
            gap: 0;
            align-items: center;
        }
        
        .nav-tab {
            padding: 12px 16px;
            color: #5f6368;
            text-decoration: none;
            font-size: 13px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
        }
        
        .nav-tab.active {
            color: #4285f4;
            border-bottom-color: #4285f4;
        }
        
        .nav-tab:hover {
            color: #4285f4;
        }
        
        .main-content {
            padding: 20px;
            margin-left: 150px;
        }
        
        .status-banner {
            background: #e8f0fe;
            border: 1px solid #4285f4;
            color: #1967d2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        .results-info {
            color: #70757a;
            font-size: 13px;
            margin-bottom: 20px;
            padding-left: 12px;
        }
        
        .result-item {
            margin-bottom: 28px;
            max-width: 600px;
            padding-left: 12px;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .favicon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f1f3f4;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
            font-weight: 400;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-title:visited {
            color: #609;
        }
        
        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }
        
        .result-snippet em {
            font-style: normal;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #fce8e6;
            border: 1px solid #f28b82;
            color: #d93025;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        .timeout-message {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .header {
                padding: 6px 15px 0 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">Google</a>
            
            <div class="search-container">
                <input type="text" class="search-box" id="search-input" placeholder="Search">
                <button class="search-btn" onclick="performSearch()">Search</button>
            </div>
        </div>
    </header>
    
    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active">🔍 All</a>
            <a href="#" class="nav-tab">🖼️ Images</a>
            <a href="#" class="nav-tab">📹 Videos</a>
            <a href="#" class="nav-tab">📰 News</a>
        </div>
    </nav>
    
    <main class="main-content">
        <div id="status-banner" class="status-banner" style="display: none;"></div>
        <div id="results-info" class="results-info"></div>
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        <div id="results-container"></div>
    </main>
    
    <script>
        // Configuration
        const API_CONFIG = {
            GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
            SEARCH_ENGINE_ID: '61201925358ea4e83',
            BASE_URL: 'https://www.googleapis.com/customsearch/v1',
            TIMEOUT: 5000 // 5 second timeout
        };
        
        let currentQuery = '';
        let searchTimeout = null;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Fast Results Page Loaded');
            
            // Check URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            
            if (query) {
                console.log('🔍 Found query in URL:', query);
                document.getElementById('search-input').value = query;
                currentQuery = query;
                performSearch();
            } else {
                // Show demo results immediately
                showDemoResults();
            }
            
            // Add enter key listener
            document.getElementById('search-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
        
        function showStatus(message, type = 'info') {
            const banner = document.getElementById('status-banner');
            banner.textContent = message;
            banner.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                banner.style.display = 'none';
            }, 5000);
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results-container').style.display = 'none';
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results-container').style.display = 'block';
        }
        
        async function performSearch() {
            const query = document.getElementById('search-input').value.trim();
            
            if (!query) {
                showStatus('Please enter a search query', 'warning');
                return;
            }
            
            currentQuery = query;
            console.log('🔍 Searching for:', query);
            
            showLoading();
            showStatus('Searching...', 'info');
            
            // Clear any existing timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Set a timeout to show demo results if API is slow
            searchTimeout = setTimeout(() => {
                console.log('⏰ Search timeout, showing demo results');
                hideLoading();
                showTimeoutMessage();
                showDemoResults(query);
            }, API_CONFIG.TIMEOUT);
            
            try {
                const results = await searchWithAPI(query);
                
                // Clear timeout since we got results
                clearTimeout(searchTimeout);
                
                hideLoading();
                displayResults(results);
                showStatus('✅ Search completed successfully!', 'success');
                
            } catch (error) {
                console.error('❌ Search failed:', error);
                
                // Clear timeout
                clearTimeout(searchTimeout);
                
                hideLoading();
                showStatus('API search failed, showing demo results', 'warning');
                showDemoResults(query);
            }
        }
        
        async function searchWithAPI(query) {
            console.log('🌐 Making API request for:', query);
            
            const params = new URLSearchParams({
                key: API_CONFIG.GOOGLE_API_KEY,
                cx: API_CONFIG.SEARCH_ENGINE_ID,
                q: query,
                num: 10
            });
            
            const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
            console.log('📡 API URL:', url.substring(0, 100) + '...');
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT);
            
            try {
                const response = await fetch(url, {
                    signal: controller.signal,
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                clearTimeout(timeoutId);
                
                console.log('📥 API Response status:', response.status);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
                }
                
                const data = await response.json();
                console.log('✅ API Response received:', data);
                
                return data;
                
            } catch (error) {
                clearTimeout(timeoutId);
                
                if (error.name === 'AbortError') {
                    throw new Error('Request timed out');
                }
                
                throw error;
            }
        }
        
        function showDemoResults(query = 'demo search') {
            console.log('🎭 Showing demo results for:', query);
            
            const demoData = {
                searchInformation: {
                    totalResults: "1,234,567",
                    searchTime: 0.45
                },
                items: [
                    {
                        title: `${query} - Fast Result 1`,
                        link: `https://developer.mozilla.org/search?q=${encodeURIComponent(query)}`,
                        snippet: `Learn about ${query} with comprehensive documentation and examples. This is a demo result showing how the search interface works.`,
                        displayLink: "developer.mozilla.org"
                    },
                    {
                        title: `${query} - Fast Result 2`,
                        link: `https://stackoverflow.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Find answers and solutions related to ${query}. Community-driven Q&A platform with expert answers.`,
                        displayLink: "stackoverflow.com"
                    },
                    {
                        title: `${query} - Fast Result 3`,
                        link: `https://github.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Explore open source projects and code examples for ${query}. Discover repositories and contribute to projects.`,
                        displayLink: "github.com"
                    },
                    {
                        title: `${query} - Fast Result 4`,
                        link: `https://www.youtube.com/results?search_query=${encodeURIComponent(query)}`,
                        snippet: `Watch video tutorials and courses about ${query}. Learn from experts with step-by-step video guides.`,
                        displayLink: "youtube.com"
                    },
                    {
                        title: `${query} - Fast Result 5`,
                        link: `https://medium.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Read articles and blog posts about ${query}. In-depth analysis and insights from industry professionals.`,
                        displayLink: "medium.com"
                    }
                ]
            };
            
            displayResults(demoData);
        }
        
        function displayResults(data) {
            console.log('📋 Displaying results:', data);
            
            const resultsInfo = document.getElementById('results-info');
            const resultsContainer = document.getElementById('results-container');
            
            if (!data || !data.items || data.items.length === 0) {
                resultsInfo.innerHTML = '<div class="error-message">No results found</div>';
                resultsContainer.innerHTML = '';
                return;
            }
            
            // Update results info
            const totalResults = data.searchInformation?.totalResults || '0';
            const searchTime = data.searchInformation?.searchTime || 0;
            resultsInfo.textContent = `About ${totalResults} results (${searchTime} seconds)`;
            
            // Display results
            let resultsHTML = '';
            
            data.items.forEach((item, index) => {
                resultsHTML += `
                    <div class="result-item" style="animation-delay: ${index * 0.1}s">
                        <div class="result-url">
                            <div class="favicon"></div>
                            <span>${escapeHtml(item.displayLink || extractDomain(item.link))}</span>
                        </div>
                        <a href="${escapeHtml(item.link)}" class="result-title" target="_blank" rel="noopener">
                            ${escapeHtml(item.title)}
                        </a>
                        <div class="result-snippet">${highlightSearchTerms(item.snippet || '', currentQuery)}</div>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = resultsHTML;
            console.log('✅ Results displayed successfully');
        }
        
        function showTimeoutMessage() {
            const banner = document.getElementById('status-banner');
            banner.innerHTML = '⏰ <strong>API Timeout:</strong> Search took too long, showing demo results instead.';
            banner.className = 'timeout-message';
            banner.style.display = 'block';
        }
        
        function extractDomain(url) {
            try {
                return new URL(url).hostname.replace('www.', '');
            } catch {
                return url;
            }
        }
        
        function highlightSearchTerms(text, query) {
            if (!query || query.length < 2) return escapeHtml(text);
            
            const terms = query.split(' ').filter(term => term.length > 1);
            let highlightedText = escapeHtml(text);
            
            terms.forEach(term => {
                const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(`(${escapedTerm})`, 'gi');
                highlightedText = highlightedText.replace(regex, '<em>$1</em>');
            });
            
            return highlightedText;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('💥 Global error:', e.error);
            showStatus('JavaScript error occurred', 'error');
        });
        
        console.log('✅ Fast Results script loaded successfully');
    </script>
</body>
</html>
