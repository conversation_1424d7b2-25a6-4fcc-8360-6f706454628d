/**
 * Search Box Component Styles
 * Enhanced search input with autocomplete and icons
 */

.search-container {
  position: relative;
  width: 100%;
  max-width: var(--search-container-max-width);
  margin: 0 auto;
}

.search-box {
  width: 100%;
  height: var(--search-box-height);
  border: var(--input-border);
  border-radius: var(--radius-xl);
  padding: 0 45px 0 var(--space-4);
  font-size: var(--font-size-md);
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  outline: none;
  transition: var(--transition-search);
  box-shadow: 0 2px 5px 1px rgba(64, 60, 67, 0.08);
}

.search-box:focus {
  box-shadow: 0 2px 5px 1px var(--shadow-search);
  border-color: var(--border-focus);
}

.search-box:hover {
  box-shadow: 0 2px 5px 1px rgba(64, 60, 67, 0.12);
}

.search-box::placeholder {
  color: var(--text-disabled);
}

/* Search Icons Container */
.search-icons {
  position: absolute;
  right: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.search-icon,
.voice-icon,
.camera-icon {
  width: var(--search-icon-container-size);
  height: var(--search-icon-container-size);
  padding: var(--space-2);
  cursor: pointer;
  border-radius: var(--radius-full);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
}

.search-icon:hover,
.voice-icon:hover,
.camera-icon:hover {
  background-color: var(--bg-hover);
}

.search-icon:focus,
.voice-icon:focus,
.camera-icon:focus {
  outline: 2px solid var(--google-blue);
  outline-offset: 2px;
}

.search-icon svg,
.voice-icon svg,
.camera-icon svg {
  width: var(--search-icon-size);
  height: var(--search-icon-size);
  fill: var(--text-disabled);
  transition: var(--transition-fast);
}

.voice-icon svg {
  fill: var(--google-blue);
}

.search-icon:hover svg,
.camera-icon:hover svg {
  fill: var(--text-secondary);
}

.voice-icon:hover svg {
  fill: var(--google-blue);
}

/* Suggestions Dropdown */
.suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-top: none;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  max-height: var(--search-suggestions-max-height);
  overflow-y: auto;
  z-index: var(--z-dropdown);
  display: none;
  box-shadow: 0 2px 5px 1px var(--shadow-search);
  animation: slideDown var(--duration-normal) var(--ease-out);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-item {
  padding: var(--space-2) var(--space-4);
  cursor: pointer;
  font-size: var(--font-size-md);
  border-bottom: 1px solid var(--bg-tertiary);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: var(--text-primary);
  font-family: var(--font-family);
  transition: var(--transition-fast);
  min-height: 44px;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background-color: var(--bg-tertiary);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  width: var(--search-icon-size);
  height: var(--search-icon-size);
  fill: var(--text-disabled);
  flex-shrink: 0;
}

.suggestion-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-remove {
  width: var(--search-icon-size);
  height: var(--search-icon-size);
  fill: var(--text-disabled);
  cursor: pointer;
  opacity: 0;
  transition: var(--transition-fast);
  padding: var(--space-1);
  border-radius: var(--radius-sm);
}

.suggestion-item:hover .suggestion-remove {
  opacity: 1;
}

.suggestion-remove:hover {
  background-color: var(--bg-hover);
  fill: var(--text-secondary);
}

/* Voice Search States */
.voice-search-active .voice-icon {
  background-color: var(--google-blue);
}

.voice-search-active .voice-icon svg {
  fill: white;
}

.voice-search-listening .voice-icon {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

/* Image Search States */
.image-search-active .camera-icon {
  background-color: var(--google-green);
}

.image-search-active .camera-icon svg {
  fill: white;
}

/* Loading State */
.search-container.loading .search-box {
  background-image: linear-gradient(
    90deg,
    transparent,
    rgba(66, 133, 244, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Error State */
.search-container.error .search-box {
  border-color: var(--google-red);
  box-shadow: 0 0 0 2px rgba(234, 67, 53, 0.2);
}

/* Disabled State */
.search-container.disabled .search-box {
  background-color: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.search-container.disabled .search-icon,
.search-container.disabled .voice-icon,
.search-container.disabled .camera-icon {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-box {
    height: var(--search-box-height-sm);
    font-size: var(--font-size-base);
    padding: 0 40px 0 var(--space-3);
  }
  
  .search-icons {
    right: var(--space-1);
    gap: var(--space-1);
  }
  
  .search-icon,
  .voice-icon,
  .camera-icon {
    width: 20px;
    height: 20px;
    padding: var(--space-1);
  }
  
  .search-icon svg,
  .voice-icon svg,
  .camera-icon svg {
    width: 14px;
    height: 14px;
  }
  
  .suggestion-item {
    padding: var(--space-3) var(--space-3);
    font-size: var(--font-size-base);
    min-height: 40px;
  }
}

@media (max-width: 480px) {
  .search-container {
    max-width: none;
  }
  
  .search-box {
    border-radius: var(--radius-lg);
  }
  
  .suggestions {
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .search-box {
    border: 2px solid var(--text-primary);
  }
  
  .search-box:focus {
    border-color: var(--google-blue);
    box-shadow: 0 0 0 2px var(--google-blue);
  }
  
  .suggestion-item:hover,
  .suggestion-item.selected {
    background-color: var(--text-primary);
    color: var(--bg-primary);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .search-box,
  .search-icon,
  .voice-icon,
  .camera-icon,
  .suggestion-item,
  .suggestion-remove {
    transition: none;
  }
  
  .suggestions {
    animation: none;
  }
  
  .voice-search-listening .voice-icon {
    animation: none;
  }
  
  .search-container.loading .search-box {
    animation: none;
    background-image: none;
  }
}

/* Print Styles */
@media print {
  .search-icons,
  .suggestions {
    display: none;
  }
  
  .search-box {
    border: 1px solid black;
    box-shadow: none;
  }
}
