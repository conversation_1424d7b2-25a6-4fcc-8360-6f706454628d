/**
 * API Service
 * Centralized API handling with caching, retry logic, and error handling
 */

import { APP_CONFIG } from '../config/app.config.js';
import logger from '../utils/logger.js';

class APIService {
  constructor() {
    this.logger = logger.child('API');
    this.cache = new Map();
    this.requestQueue = new Map();
    this.retryDelays = [1000, 2000, 4000]; // Exponential backoff
  }
  
  /**
   * Make HTTP request with retry logic and caching
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<any>} Response data
   */
  async request(url, options = {}) {
    const startTime = performance.now();
    const cacheKey = this.getCacheKey(url, options);
    
    // Check cache first
    if (options.cache !== false && this.cache.has(cacheKey)) {
      const cachedData = this.cache.get(cacheKey);
      if (this.isCacheValid(cachedData)) {
        this.logger.debug(`Cache hit for ${url}`);
        return cachedData.data;
      } else {
        this.cache.delete(cacheKey);
      }
    }
    
    // Check if request is already in progress
    if (this.requestQueue.has(cacheKey)) {
      this.logger.debug(`Request already in progress for ${url}`);
      return this.requestQueue.get(cacheKey);
    }
    
    // Create request promise
    const requestPromise = this.executeRequest(url, options);
    this.requestQueue.set(cacheKey, requestPromise);
    
    try {
      const data = await requestPromise;
      const duration = performance.now() - startTime;
      
      // Cache successful response
      if (options.cache !== false) {
        this.cacheResponse(cacheKey, data);
      }
      
      this.logger.api(options.method || 'GET', url, 200, duration);
      return data;
      
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logger.api(options.method || 'GET', url, error.status || 500, duration);
      throw error;
      
    } finally {
      this.requestQueue.delete(cacheKey);
    }
  }
  
  /**
   * Execute HTTP request with retry logic
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<any>} Response data
   */
  async executeRequest(url, options = {}) {
    const maxRetries = options.retries || APP_CONFIG.API.RETRY_ATTEMPTS;
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
        }, options.timeout || APP_CONFIG.API.TIMEOUT);
        
        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new APIError(
            `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            url
          );
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return await response.json();
        } else {
          return await response.text();
        }
        
      } catch (error) {
        lastError = error;
        
        if (attempt < maxRetries && this.shouldRetry(error)) {
          const delay = this.retryDelays[Math.min(attempt, this.retryDelays.length - 1)];
          this.logger.warn(`Request failed, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
          await this.delay(delay);
        } else {
          break;
        }
      }
    }
    
    throw lastError;
  }
  
  /**
   * Search using Google Custom Search API
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Search results
   */
  async search(query, options = {}) {
    if (!query || query.trim().length === 0) {
      throw new APIError('Search query cannot be empty', 400);
    }
    
    if (query.length > APP_CONFIG.SEARCH.MAX_QUERY_LENGTH) {
      throw new APIError('Search query too long', 400);
    }
    
    const params = new URLSearchParams({
      key: APP_CONFIG.API.GOOGLE_API_KEY,
      cx: APP_CONFIG.API.SEARCH_ENGINE_ID,
      q: this.buildEnhancedQuery(query.trim(), options.type),
      num: options.num || APP_CONFIG.SEARCH.RESULTS_PER_PAGE,
      start: options.start || 1,
      ...options.params
    });

    // Add search type specific parameters for enhanced results
    this.addSearchTypeParameters(params, options.type, query.trim());
    
    const url = `${APP_CONFIG.API.BASE_URL}?${params.toString()}`;
    
    try {
      const data = await this.request(url, {
        cache: true,
        timeout: APP_CONFIG.API.TIMEOUT
      });
      
      return this.normalizeSearchResults(data);
      
    } catch (error) {
      if (APP_CONFIG.DEV.MOCK_API) {
        this.logger.warn('API failed, returning mock data');
        return this.getMockSearchResults(query, options);
      }
      throw error;
    }
  }
  
  /**
   * Get search suggestions
   * @param {string} query - Partial query
   * @returns {Promise<Array>} Suggestions array
   */
  async getSuggestions(query) {
    if (!query || query.length < 2) {
      return [];
    }
    
    // For now, return cached suggestions or generate simple ones
    // In a real implementation, this would call Google's suggestion API
    return this.generateSuggestions(query);
  }
  
  /**
   * Build enhanced query based on search type
   * @param {string} query - Original query
   * @param {string} searchType - Type of search
   * @returns {string} Enhanced query
   */
  buildEnhancedQuery(query, searchType) {
    if (!searchType || searchType === 'web' || searchType === 'all') {
      return query;
    }

    switch (searchType) {
      case 'images':
        return `${query} high resolution photo image picture photography visual content`;
      case 'videos':
        return `${query} video tutorial watch lesson course documentary educational content streaming`;
      case 'news':
        return `${query} news latest breaking update report today current events journalism`;
      case 'shopping':
        return `${query} buy price product review deal discount store online shopping e-commerce`;
      case 'books':
        return `${query} book author review read summary library isbn academic literature publication`;
      case 'web':
        return `${query} information guide resource official website documentation`;
      default:
        return query;
    }
  }

  /**
   * Add search type specific parameters
   * @param {URLSearchParams} params - URL parameters object
   * @param {string} searchType - Type of search
   * @param {string} originalQuery - Original search query
   */
  addSearchTypeParameters(params, searchType, originalQuery) {
    if (!searchType || searchType === 'web' || searchType === 'all') {
      return;
    }

    switch (searchType) {
      case 'images':
        params.append('searchType', 'image');
        params.append('imgSize', 'xlarge');
        params.append('imgType', 'photo');
        params.append('imgColorType', 'color');
        params.append('safe', 'active');
        params.append('rights', 'cc_publicdomain,cc_attribute,cc_sharealike,cc_noncommercial,cc_nonderived');
        params.append('fileType', 'jpg,png,gif,webp,svg');
        params.append('filter', '1');
        params.append('exactTerms', originalQuery.split(' ').slice(0, 3).join(' '));
        params.append('siteSearch', 'unsplash.com OR pixabay.com OR pexels.com OR wikimedia.org OR flickr.com');
        break;
      case 'videos':
        params.append('siteSearch', 'youtube.com OR vimeo.com OR dailymotion.com OR ted.com OR twitch.tv OR coursera.org OR udemy.com OR khanacademy.org');
        params.append('orTerms', 'video watch tutorial lesson course documentary webinar lecture presentation');
        params.append('dateRestrict', 'y2');
        params.append('filter', '1');
        params.append('excludeTerms', 'spam fake clickbait');
        params.append('lr', 'lang_en');
        break;
      case 'news':
        params.append('siteSearch', 'reuters.com OR bbc.com OR cnn.com OR apnews.com OR npr.org OR theguardian.com OR wsj.com OR nytimes.com OR washingtonpost.com OR bloomberg.com');
        params.append('sort', 'date');
        params.append('dateRestrict', 'd3');
        params.append('orTerms', 'news breaking latest update report today current events');
        params.append('filter', '1');
        params.append('excludeTerms', 'opinion editorial blog personal');
        params.append('lr', 'lang_en');
        break;
      case 'shopping':
        params.append('siteSearch', 'amazon.com OR ebay.com OR walmart.com OR target.com OR bestbuy.com OR etsy.com OR shopify.com OR alibaba.com OR costco.com OR homedepot.com');
        params.append('orTerms', 'buy price product store shop deal discount sale review rating customer feedback');
        params.append('cr', 'countryUS');
        params.append('filter', '1');
        params.append('excludeTerms', 'fake counterfeit scam');
        params.append('dateRestrict', 'm6');
        params.append('lr', 'lang_en');
        break;
      case 'books':
        params.append('siteSearch', 'amazon.com OR goodreads.com OR books.google.com OR worldcat.org OR barnesandnoble.com OR openlibrary.org OR archive.org OR jstor.org OR scholar.google.com');
        params.append('orTerms', 'book author read library isbn publisher review summary academic textbook novel fiction');
        params.append('fileType', 'pdf,epub,mobi,djvu');
        params.append('filter', '1');
        params.append('rights', 'cc_publicdomain,cc_attribute,cc_sharealike');
        params.append('excludeTerms', 'pirated illegal download torrent');
        params.append('lr', 'lang_en');
        break;
    }
  }

  /**
   * Generate cache key for request
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {string} Cache key
   */
  getCacheKey(url, options) {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }
  
  /**
   * Cache response data
   * @param {string} key - Cache key
   * @param {any} data - Response data
   */
  cacheResponse(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: APP_CONFIG.SEARCH.CACHE_DURATION
    });
    
    // Cleanup old cache entries
    if (this.cache.size > APP_CONFIG.PERFORMANCE.CACHE_SIZE_LIMIT) {
      this.cleanupCache();
    }
  }
  
  /**
   * Check if cached data is still valid
   * @param {Object} cachedData - Cached data object
   * @returns {boolean} Whether cache is valid
   */
  isCacheValid(cachedData) {
    return Date.now() - cachedData.timestamp < cachedData.ttl;
  }
  
  /**
   * Clean up expired cache entries
   */
  cleanupCache() {
    const now = Date.now();
    for (const [key, data] of this.cache.entries()) {
      if (now - data.timestamp >= data.ttl) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * Check if error should trigger a retry
   * @param {Error} error - Error object
   * @returns {boolean} Whether to retry
   */
  shouldRetry(error) {
    if (error.name === 'AbortError') return false;
    if (error.status >= 400 && error.status < 500) return false; // Client errors
    return true; // Network errors and server errors
  }
  
  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Normalize search results from API
   * @param {Object} data - Raw API response
   * @returns {Object} Normalized results
   */
  normalizeSearchResults(data) {
    return {
      items: data.items || [],
      totalResults: parseInt(data.searchInformation?.totalResults || 0),
      searchTime: parseFloat(data.searchInformation?.searchTime || 0),
      query: data.queries?.request?.[0]?.searchTerms || '',
      nextPage: data.queries?.nextPage?.[0] || null,
      previousPage: data.queries?.previousPage?.[0] || null
    };
  }
  
  /**
   * Generate mock search results for development
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Object} Mock results
   */
  getMockSearchResults(query, options = {}) {
    const start = options.start || 1;
    const num = options.num || 10;
    const searchType = options.type || 'web';

    const mockItems = Array.from({ length: num }, (_, i) => {
      const baseItem = {
        title: `${query} - ${searchType} Result ${start + i}`,
        link: `https://example.com/${searchType}-result-${start + i}`,
        snippet: `This is a mock ${searchType} search result for "${query}". Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
        displayLink: 'example.com'
      };

      // Add search type specific properties
      if (searchType === 'images') {
        baseItem.image = {
          thumbnailLink: `https://images.unsplash.com/photo-150690592534${i}?w=300&h=200&fit=crop&auto=format`,
          contextLink: baseItem.link
        };
      } else if (searchType === 'videos') {
        baseItem.displayLink = 'youtube.com';
        baseItem.link = `https://youtube.com/watch?v=mock${start + i}`;
        baseItem.snippet = `Watch this ${query} video tutorial. Duration: ${5 + i} minutes.`;
      } else if (searchType === 'shopping') {
        baseItem.displayLink = 'amazon.com';
        baseItem.snippet = `Buy ${query} - Price: $${(Math.random() * 100 + 10).toFixed(2)}. Free shipping available.`;
      } else if (searchType === 'books') {
        baseItem.displayLink = 'goodreads.com';
        baseItem.snippet = `"${query}" book review and summary. Rating: ${(Math.random() * 2 + 3).toFixed(1)}/5 stars.`;
      } else if (searchType === 'news') {
        baseItem.displayLink = 'reuters.com';
        baseItem.snippet = `Breaking news about ${query}. Published ${Math.floor(Math.random() * 24)} hours ago.`;
      }

      return baseItem;
    });

    return {
      items: mockItems,
      totalResults: 1000000,
      searchTime: 0.123,
      query,
      nextPage: start + num <= 100 ? { startIndex: start + num } : null,
      previousPage: start > 1 ? { startIndex: Math.max(1, start - num) } : null
    };
  }
  
  /**
   * Generate simple suggestions
   * @param {string} query - Partial query
   * @returns {Array} Suggestions
   */
  generateSuggestions(query) {
    const commonSuggestions = [
      `${query} tutorial`,
      `${query} examples`,
      `${query} documentation`,
      `${query} best practices`,
      `${query} guide`,
      `what is ${query}`,
      `${query} vs`,
      `${query} 2024`
    ];
    
    return commonSuggestions.slice(0, APP_CONFIG.SEARCH.MAX_SUGGESTIONS);
  }
  
  /**
   * Clear all caches
   */
  clearCache() {
    this.cache.clear();
    this.logger.debug('API cache cleared');
  }
}

/**
 * Custom API Error class
 */
class APIError extends Error {
  constructor(message, status = 500, url = '') {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.url = url;
  }
}

// Create singleton instance
const apiService = new APIService();

export { APIService, APIError };
export default apiService;
