/**
 * Enhanced Logging Utility
 * Provides structured logging with different levels and contexts
 */

import { APP_CONFIG } from '../config/app.config.js';

class Logger {
  constructor() {
    this.levels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    
    this.currentLevel = APP_CONFIG.DEV.DEBUG ? this.levels.DEBUG : this.levels.INFO;
    this.context = 'GoogleClone';
  }
  
  /**
   * Set logging context
   * @param {string} context - Context name
   */
  setContext(context) {
    this.context = context;
    return this;
  }
  
  /**
   * Format log message with timestamp and context
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {any} data - Additional data
   * @returns {string} Formatted message
   */
  formatMessage(level, message, data) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level}] [${this.context}]`;
    
    if (data) {
      return `${prefix} ${message}`;
    }
    
    return `${prefix} ${message}`;
  }
  
  /**
   * Log error message
   * @param {string} message - Error message
   * @param {Error|any} error - Error object or additional data
   */
  error(message, error = null) {
    if (this.currentLevel >= this.levels.ERROR) {
      const formattedMessage = this.formatMessage('ERROR', message);
      
      if (error instanceof Error) {
        console.error(formattedMessage, error);
        
        // Report to error tracking service if configured
        if (APP_CONFIG.DEV.ERROR_REPORTING) {
          this.reportError(message, error);
        }
      } else {
        console.error(formattedMessage, error);
      }
    }
  }
  
  /**
   * Log warning message
   * @param {string} message - Warning message
   * @param {any} data - Additional data
   */
  warn(message, data = null) {
    if (this.currentLevel >= this.levels.WARN) {
      const formattedMessage = this.formatMessage('WARN', message);
      console.warn(formattedMessage, data);
    }
  }
  
  /**
   * Log info message
   * @param {string} message - Info message
   * @param {any} data - Additional data
   */
  info(message, data = null) {
    if (this.currentLevel >= this.levels.INFO) {
      const formattedMessage = this.formatMessage('INFO', message);
      console.info(formattedMessage, data);
    }
  }
  
  /**
   * Log debug message
   * @param {string} message - Debug message
   * @param {any} data - Additional data
   */
  debug(message, data = null) {
    if (this.currentLevel >= this.levels.DEBUG) {
      const formattedMessage = this.formatMessage('DEBUG', message);
      console.debug(formattedMessage, data);
    }
  }
  
  /**
   * Log performance metrics
   * @param {string} operation - Operation name
   * @param {number} duration - Duration in milliseconds
   * @param {any} metadata - Additional metadata
   */
  performance(operation, duration, metadata = null) {
    if (APP_CONFIG.DEV.PERFORMANCE_MONITORING) {
      const message = `Performance: ${operation} took ${duration}ms`;
      this.info(message, metadata);
    }
  }
  
  /**
   * Log API calls
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {number} status - Response status
   * @param {number} duration - Request duration
   */
  api(method, url, status, duration) {
    const message = `API: ${method} ${url} - ${status} (${duration}ms)`;
    
    if (status >= 400) {
      this.error(message);
    } else {
      this.debug(message);
    }
  }
  
  /**
   * Log user interactions
   * @param {string} action - User action
   * @param {any} data - Action data
   */
  userAction(action, data = null) {
    this.debug(`User Action: ${action}`, data);
  }
  
  /**
   * Report error to external service
   * @param {string} message - Error message
   * @param {Error} error - Error object
   */
  reportError(message, error) {
    // Placeholder for error reporting service integration
    // Could integrate with Sentry, LogRocket, etc.
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: message,
        fatal: false
      });
    }
  }
  
  /**
   * Create a child logger with specific context
   * @param {string} context - Child context name
   * @returns {Logger} Child logger instance
   */
  child(context) {
    const childLogger = new Logger();
    childLogger.setContext(`${this.context}:${context}`);
    return childLogger;
  }
  
  /**
   * Group related log messages
   * @param {string} label - Group label
   * @param {Function} callback - Function to execute within group
   */
  group(label, callback) {
    if (this.currentLevel >= this.levels.DEBUG) {
      console.group(this.formatMessage('GROUP', label));
      try {
        callback();
      } finally {
        console.groupEnd();
      }
    } else {
      callback();
    }
  }
  
  /**
   * Log table data
   * @param {any} data - Data to display in table format
   * @param {string} label - Optional label
   */
  table(data, label = 'Data') {
    if (this.currentLevel >= this.levels.DEBUG) {
      this.debug(label);
      console.table(data);
    }
  }
}

// Create singleton instance
const logger = new Logger();

// Export both the class and singleton instance
export { Logger };
export default logger;
