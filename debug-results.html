<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Search Results - Google Clone</title>
    <style>
        body {
            font-family: arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }
        
        .debug-section h3 {
            margin-top: 0;
            color: #4285f4;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #3367d6;
        }
        
        .results-container {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .result-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background: #f8f9fa;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 18px;
            margin-bottom: 5px;
            text-decoration: none;
        }
        
        .result-url {
            color: #006621;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .result-snippet {
            color: #545454;
            font-size: 14px;
            line-height: 1.4;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        
        .search-input {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Search Results Debug Page</h1>
        <p>This page helps debug search functionality and API integration.</p>
        
        <div class="debug-section">
            <h3>🔧 Quick Search Test</h3>
            <input type="text" class="search-input" id="quick-search" placeholder="Enter search query" value="javascript">
            <button class="test-button" onclick="performQuickSearch()">Search</button>
            <button class="test-button" onclick="testAPI()">Test API</button>
            <button class="test-button" onclick="showDemoResults()">Show Demo Results</button>
            <button class="test-button" onclick="clearResults()">Clear</button>
        </div>
        
        <div class="debug-section">
            <h3>📊 Status</h3>
            <div id="status-display"></div>
        </div>
        
        <div class="debug-section">
            <h3>🔍 Search Results</h3>
            <div id="results-info"></div>
            <div id="results-display"></div>
        </div>
        
        <div class="debug-section">
            <h3>📄 Raw API Response</h3>
            <pre id="raw-response">No data yet...</pre>
        </div>
        
        <div class="debug-section">
            <h3>🐛 Console Logs</h3>
            <pre id="console-logs">Console output will appear here...</pre>
        </div>
    </div>
    
    <script>
        // Configuration
        const CONFIG = {
            GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
            SEARCH_ENGINE_ID: '61201925358ea4e83',
            BASE_URL: 'https://www.googleapis.com/customsearch/v1',
            RESULTS_PER_PAGE: 10
        };
        
        // Console logging override
        const originalLog = console.log;
        const originalError = console.error;
        const logs = [];
        
        console.log = function(...args) {
            logs.push('LOG: ' + args.join(' '));
            updateConsoleLogs();
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push('ERROR: ' + args.join(' '));
            updateConsoleLogs();
            originalError.apply(console, args);
        };
        
        function updateConsoleLogs() {
            const consoleElement = document.getElementById('console-logs');
            if (consoleElement) {
                consoleElement.textContent = logs.slice(-20).join('\n');
            }
        }
        
        function updateStatus(message, type = 'success') {
            const statusElement = document.getElementById('status-display');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function performQuickSearch() {
            const query = document.getElementById('quick-search').value;
            if (!query.trim()) {
                updateStatus('Please enter a search query', 'error');
                return;
            }
            
            console.log('Starting quick search for:', query);
            updateStatus('🔍 Searching...', 'warning');
            
            try {
                const results = await searchGoogle(query);
                console.log('Search completed successfully');
                displayResults(results);
                updateStatus('✅ Search completed successfully', 'success');
            } catch (error) {
                console.error('Search failed:', error);
                updateStatus('❌ Search failed: ' + error.message, 'error');
            }
        }
        
        async function testAPI() {
            console.log('Testing API connection...');
            updateStatus('🔧 Testing API...', 'warning');
            
            try {
                const testUrl = `${CONFIG.BASE_URL}?key=${CONFIG.GOOGLE_API_KEY}&cx=${CONFIG.SEARCH_ENGINE_ID}&q=test&num=1`;
                console.log('Test URL:', testUrl);
                
                const response = await fetch(testUrl);
                console.log('API Response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('API test successful');
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                    updateStatus('✅ API connection successful', 'success');
                } else {
                    const errorData = await response.json();
                    console.error('API test failed:', errorData);
                    document.getElementById('raw-response').textContent = JSON.stringify(errorData, null, 2);
                    updateStatus(`❌ API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                console.error('API test failed:', error);
                updateStatus('❌ API test failed: ' + error.message, 'error');
            }
        }
        
        async function searchGoogle(query, start = 1) {
            console.log('searchGoogle called with:', query, start);
            
            const params = new URLSearchParams({
                key: CONFIG.GOOGLE_API_KEY,
                cx: CONFIG.SEARCH_ENGINE_ID,
                q: query,
                start: start,
                num: CONFIG.RESULTS_PER_PAGE
            });
            
            const url = `${CONFIG.BASE_URL}?${params.toString()}`;
            console.log('Making request to:', url);
            
            const response = await fetch(url);
            console.log('Response status:', response.status);
            
            if (!response.ok) {
                const errorData = await response.json();
                console.error('API error response:', errorData);
                throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }
            
            const data = await response.json();
            console.log('API response data:', data);
            
            // Store raw response
            document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
            
            return data;
        }
        
        function showDemoResults() {
            console.log('Showing demo results');
            updateStatus('📋 Showing demo results', 'warning');
            
            const demoData = {
                searchInformation: {
                    totalResults: "1234567",
                    searchTime: 0.45
                },
                items: [
                    {
                        title: "Demo Result 1 - JavaScript Tutorial",
                        link: "https://example.com/javascript-tutorial",
                        snippet: "Learn JavaScript with this comprehensive tutorial. Covers variables, functions, objects, and more.",
                        displayLink: "example.com"
                    },
                    {
                        title: "Demo Result 2 - JavaScript Reference",
                        link: "https://example.com/javascript-reference",
                        snippet: "Complete JavaScript reference documentation with examples and best practices.",
                        displayLink: "example.com"
                    },
                    {
                        title: "Demo Result 3 - JavaScript Examples",
                        link: "https://example.com/javascript-examples",
                        snippet: "Practical JavaScript examples and code snippets for common programming tasks.",
                        displayLink: "example.com"
                    }
                ]
            };
            
            displayResults(demoData);
            document.getElementById('raw-response').textContent = JSON.stringify(demoData, null, 2);
        }
        
        function displayResults(data) {
            console.log('Displaying results:', data);
            
            const resultsInfo = document.getElementById('results-info');
            const resultsDisplay = document.getElementById('results-display');
            
            if (!data || !data.items || data.items.length === 0) {
                resultsInfo.innerHTML = '<div class="status error">No results found</div>';
                resultsDisplay.innerHTML = '';
                return;
            }
            
            // Update results info
            const totalResults = data.searchInformation?.totalResults || '0';
            const searchTime = data.searchInformation?.searchTime || 0;
            resultsInfo.innerHTML = `
                <div class="status success">
                    Found ${parseInt(totalResults).toLocaleString()} results in ${searchTime} seconds
                </div>
            `;
            
            // Display results
            let resultsHTML = '<div class="results-container">';
            
            data.items.forEach((item, index) => {
                resultsHTML += `
                    <div class="result-item">
                        <div class="result-title">
                            <a href="${escapeHtml(item.link)}" target="_blank" rel="noopener">
                                ${escapeHtml(item.title)}
                            </a>
                        </div>
                        <div class="result-url">${escapeHtml(item.displayLink || item.link)}</div>
                        <div class="result-snippet">${escapeHtml(item.snippet || 'No snippet available')}</div>
                    </div>
                `;
            });
            
            resultsHTML += '</div>';
            resultsDisplay.innerHTML = resultsHTML;
            
            console.log('Results displayed successfully');
        }
        
        function clearResults() {
            document.getElementById('results-info').innerHTML = '';
            document.getElementById('results-display').innerHTML = '';
            document.getElementById('raw-response').textContent = 'No data yet...';
            logs.length = 0;
            updateConsoleLogs();
            updateStatus('🧹 Results cleared', 'warning');
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Debug page loaded');
            updateStatus('🚀 Debug page ready', 'success');
            
            // Check URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            
            if (query) {
                document.getElementById('quick-search').value = query;
                console.log('Auto-searching for URL query:', query);
                performQuickSearch();
            }
        });
        
        // Allow Enter key in search input
        document.addEventListener('keypress', (e) => {
            if (e.target.id === 'quick-search' && e.key === 'Enter') {
                performQuickSearch();
            }
        });
    </script>
</body>
</html>
