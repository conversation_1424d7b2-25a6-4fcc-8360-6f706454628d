<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results - Google Clone</title>
    <style>
        :root {
            --google-blue: #4285f4;
            --google-red: #ea4335;
            --google-yellow: #fbbc05;
            --google-green: #34a853;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --text-disabled: #9aa0a6;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-primary: #dadce0;
            --border-secondary: #dfe1e5;
            --font-family: arial, sans-serif;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: var(--font-family);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid var(--border-primary);
            background: var(--bg-primary);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }
        
        .logo svg {
            width: 92px;
            height: 30px;
        }
        
        .search-container {
            flex: 1;
            max-width: 584px;
            position: relative;
        }
        
        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid var(--border-secondary);
            border-radius: 24px;
            padding: 0 45px 0 16px;
            font-size: 16px;
            outline: none;
            font-family: var(--font-family);
        }
        
        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }
        
        .nav-tabs {
            background: var(--bg-primary);
            padding: 0 20px;
        }
        
        .nav-content {
            display: flex;
            gap: 0;
            align-items: center;
        }
        
        .nav-tab {
            padding: 12px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 13px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .nav-tab.active {
            color: var(--google-blue);
            border-bottom-color: var(--google-blue);
        }
        
        .nav-tab:hover {
            color: var(--google-blue);
        }
        
        .nav-tab svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        .main-content {
            padding: 20px;
            margin-left: 150px;
        }
        
        .results-info {
            color: var(--text-secondary);
            font-size: 13px;
            margin-bottom: 20px;
            padding-left: 12px;
        }
        
        .result-item {
            margin-bottom: 28px;
            max-width: 600px;
            padding-left: 12px;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .result-url {
            color: var(--text-primary);
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .favicon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f1f3f4;
        }
        
        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
            font-weight: 400;
        }
        
        .result-title:hover {
            text-decoration: underline;
        }
        
        .result-title:visited {
            color: #609;
        }
        
        .result-snippet {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.58;
        }
        
        .result-snippet em {
            font-style: normal;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--google-blue);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .pagination {
            margin-top: 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 0;
            padding-left: 12px;
        }
        
        .page-btn {
            padding: 12px 16px;
            border: none;
            background: none;
            color: var(--google-blue);
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
        }
        
        .page-btn:hover {
            background: #f8f9fa;
        }
        
        .error-message {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .header {
                padding: 6px 15px 0 15px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            
            <div class="search-container">
                <input type="text" class="search-box" id="search-input" placeholder="">
            </div>
        </div>
    </header>
    
    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active" data-type="web">
                <svg viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                All
            </a>
            <a href="#" class="nav-tab" data-type="images">
                <svg viewBox="0 0 24 24">
                    <path d="M21,19V5c0,-1.1 -0.9,-2 -2,-2H5c-1.1,0 -2,0.9 -2,2v14c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2zM8.5,13.5l2.5,3.01L14.5,12l4.5,6H5l3.5,-4.5z"/>
                </svg>
                Images
            </a>
            <a href="#" class="nav-tab" data-type="videos">
                <svg viewBox="0 0 24 24">
                    <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                </svg>
                Videos
            </a>
            <a href="#" class="nav-tab" data-type="news">
                <svg viewBox="0 0 24 24">
                    <path d="M20,11H4V8H20M20,15H13V13H20M20,19H13V17H20M11,19H4V13H11M20.33,4.67L18.67,3L17,4.67L15.33,3L13.67,4.67L12,3L10.33,4.67L8.67,3L7,4.67L5.33,3L3.67,4.67L2,3V19A2,2 0 0,0 4,21H20A2,2 0 0,0 22,19V3L20.33,4.67Z"/>
                </svg>
                News
            </a>
        </div>
    </nav>
    
    <main class="main-content">
        <div id="results-info" class="results-info"></div>
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        <div id="results-container"></div>
        <div id="pagination" class="pagination"></div>
    </main>
    
    <script>
        // Configuration
        const CONFIG = {
            GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
            SEARCH_ENGINE_ID: '61201925358ea4e83',
            BASE_URL: 'https://www.googleapis.com/customsearch/v1',
            RESULTS_PER_PAGE: 10
        };
        
        // Simple search app that definitely works
        class SimpleSearchApp {
            constructor() {
                this.currentQuery = '';
                this.currentPage = 1;
                this.currentType = 'web';
                this.init();
            }
            
            init() {
                console.log('SimpleSearchApp initializing...');
                this.setupEventListeners();
                this.loadInitialResults();
            }
            
            setupEventListeners() {
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.performSearch(e.target.value);
                        }
                    });
                }
                
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchTab(tab.dataset.type);
                    });
                });
            }
            
            loadInitialResults() {
                const urlParams = new URLSearchParams(window.location.search);
                const query = urlParams.get('q');
                
                console.log('Loading initial results for query:', query);
                
                if (query) {
                    this.currentQuery = query;
                    this.currentType = urlParams.get('type') || 'web';
                    this.currentPage = parseInt(urlParams.get('start')) || 1;
                    
                    // Update search input
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.value = query;
                    }
                    
                    // Update active tab
                    document.querySelectorAll('.nav-tab').forEach(tab => {
                        tab.classList.toggle('active', tab.dataset.type === this.currentType);
                    });
                    
                    // Perform search
                    this.displayResults();
                } else {
                    // Show some default results for demo
                    this.showDemoResults();
                }
            }
            
            performSearch(query) {
                if (!query.trim()) return;
                
                this.currentQuery = query;
                this.currentPage = 1;
                this.updateURL();
                this.displayResults();
            }
            
            switchTab(type) {
                if (!this.currentQuery) return;
                
                this.currentType = type;
                this.currentPage = 1;
                
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === type);
                });
                
                this.updateURL();
                this.displayResults();
            }
            
            async displayResults() {
                console.log('Displaying results for:', this.currentQuery);
                this.showLoading();
                
                try {
                    const results = await this.searchGoogle(this.currentQuery, this.currentPage, this.currentType);
                    console.log('Got results:', results);
                    this.renderResults(results);
                } catch (error) {
                    console.error('Search failed:', error);
                    this.showError('Search failed: ' + error.message);
                } finally {
                    this.hideLoading();
                }
            }
            
            async searchGoogle(query, start = 1, searchType = 'web') {
                console.log('Searching Google for:', query, 'start:', start, 'type:', searchType);
                
                try {
                    const params = new URLSearchParams({
                        key: CONFIG.GOOGLE_API_KEY,
                        cx: CONFIG.SEARCH_ENGINE_ID,
                        q: query,
                        start: start,
                        num: CONFIG.RESULTS_PER_PAGE
                    });
                    
                    if (searchType === 'images') {
                        params.append('searchType', 'image');
                    }
                    
                    const url = `${CONFIG.BASE_URL}?${params.toString()}`;
                    console.log('API URL:', url);
                    
                    const response = await fetch(url);
                    console.log('API Response status:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`API Error: ${response.status} ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    console.log('API Data:', data);
                    
                    return data;
                } catch (error) {
                    console.error('API call failed:', error);
                    // Return demo data as fallback
                    return this.getDemoResults(query, start, searchType);
                }
            }
            
            getDemoResults(query, start = 1, searchType = 'web') {
                console.log('Using demo results for:', query);
                const results = [];
                const startNum = (start - 1) * 10 + 1;
                
                for (let i = 0; i < 10; i++) {
                    results.push({
                        title: `${query} - Search Result ${startNum + i}`,
                        link: `https://example.com/result-${startNum + i}`,
                        snippet: `This is a search result for "${query}". Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`,
                        displayLink: 'example.com'
                    });
                }
                
                return {
                    searchInformation: {
                        totalResults: "1234567",
                        searchTime: 0.45
                    },
                    items: results
                };
            }
            
            showDemoResults() {
                console.log('Showing demo results');
                const demoResults = this.getDemoResults('demo search', 1, 'web');
                this.renderResults(demoResults);
            }
            
            renderResults(data) {
                console.log('Rendering results:', data);
                const resultsInfo = document.getElementById('results-info');
                const resultsContainer = document.getElementById('results-container');
                
                if (!data || !data.items || data.items.length === 0) {
                    this.showNoResults();
                    return;
                }
                
                // Update results info
                const totalResults = data.searchInformation?.totalResults || '0';
                const searchTime = data.searchInformation?.searchTime || 0;
                resultsInfo.textContent = `About ${parseInt(totalResults).toLocaleString()} results (${searchTime} seconds)`;
                
                // Clear previous results
                resultsContainer.innerHTML = '';
                
                // Render results
                data.items.forEach((item, index) => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item';
                    resultDiv.style.animationDelay = `${index * 0.1}s`;
                    
                    const domain = this.extractDomain(item.link);
                    const highlightedSnippet = this.highlightSearchTerms(item.snippet || '', this.currentQuery);
                    
                    resultDiv.innerHTML = `
                        <div class="result-url">
                            <div class="favicon"></div>
                            <span>${this.escapeHtml(item.displayLink || domain)}</span>
                        </div>
                        <a href="${this.escapeHtml(item.link)}" class="result-title" target="_blank" rel="noopener">
                            ${this.escapeHtml(item.title)}
                        </a>
                        <div class="result-snippet">${highlightedSnippet}</div>
                    `;
                    
                    resultsContainer.appendChild(resultDiv);
                });
                
                this.renderPagination();
                console.log('Results rendered successfully');
            }
            
            showNoResults() {
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #5f6368;">
                        <h3>No results found</h3>
                        <p>Try different keywords or check your spelling</p>
                    </div>
                `;
            }
            
            showError(message) {
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ea4335;">
                        <h3>Something went wrong</h3>
                        <p>${this.escapeHtml(message)}</p>
                        <button onclick="app.displayResults()" style="background: #4285f4; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                            Try again
                        </button>
                    </div>
                `;
            }
            
            renderPagination() {
                const pagination = document.getElementById('pagination');
                const totalPages = 10;
                
                if (totalPages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }
                
                let paginationHTML = '';
                
                if (this.currentPage > 1) {
                    paginationHTML += `<button class="page-btn" onclick="app.goToPage(${this.currentPage - 1})">< Previous</button>`;
                }
                
                const startPage = Math.max(1, this.currentPage - 2);
                const endPage = Math.min(totalPages, startPage + 4);
                
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === this.currentPage;
                    paginationHTML += `
                        <button class="page-btn ${isActive ? 'active' : ''}" 
                                onclick="app.goToPage(${i})" 
                                ${isActive ? 'disabled' : ''}>
                            ${i}
                        </button>
                    `;
                }
                
                if (this.currentPage < totalPages) {
                    paginationHTML += `<button class="page-btn" onclick="app.goToPage(${this.currentPage + 1})">Next ></button>`;
                }
                
                pagination.innerHTML = paginationHTML;
            }
            
            goToPage(page) {
                this.currentPage = page;
                this.updateURL();
                this.displayResults();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
            
            showLoading() {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results-container').style.display = 'none';
            }
            
            hideLoading() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results-container').style.display = 'block';
            }
            
            updateURL() {
                const url = new URL(window.location);
                url.searchParams.set('q', this.currentQuery);
                url.searchParams.set('start', this.currentPage.toString());
                url.searchParams.set('type', this.currentType);
                
                window.history.pushState({}, '', url.toString());
                document.title = `${this.currentQuery} - Google Clone`;
            }
            
            extractDomain(url) {
                try {
                    return new URL(url).hostname.replace('www.', '');
                } catch {
                    return url;
                }
            }
            
            highlightSearchTerms(text, query) {
                if (!query || query.length < 2) return this.escapeHtml(text);
                
                const terms = query.split(' ').filter(term => term.length > 1);
                let highlightedText = this.escapeHtml(text);
                
                terms.forEach(term => {
                    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(`(${escapedTerm})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<em>$1</em>');
                });
                
                return highlightedText;
            }
            
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }
        
        // Initialize the app
        console.log('Starting SimpleSearchApp...');
        const app = new SimpleSearchApp();
        window.app = app;
        
        console.log('SimpleSearchApp initialized');
    </script>
</body>
</html>
