<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Search Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .nav-tabs { margin: 20px 0; }
        .nav-tab { 
            display: inline-block; 
            padding: 10px 20px; 
            margin: 0 5px; 
            background: #f0f0f0; 
            border: none; 
            cursor: pointer; 
            border-radius: 5px;
        }
        .nav-tab.active { background: #4285f4; color: white; }
        .result-item { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        .result-title { color: #1a0dab; text-decoration: none; font-size: 18px; }
        .result-snippet { color: #4d5156; margin-top: 5px; }
        .result-type-badge { 
            background: #4285f4; 
            color: white; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-size: 10px; 
            margin-left: 10px;
        }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <h1>Simple Search Test</h1>
    
    <div class="nav-tabs">
        <button class="nav-tab active" data-type="all">All</button>
        <button class="nav-tab" data-type="web">Web</button>
        <button class="nav-tab" data-type="images">Images</button>
        <button class="nav-tab" data-type="news">News</button>
    </div>
    
    <div id="loading" class="loading">Loading...</div>
    <div id="results-container"></div>
    
    <script>
        class SimpleSearchApp {
            constructor() {
                this.currentQuery = '';
                this.currentType = 'all';
                this.init();
            }
            
            init() {
                console.log('SimpleSearchApp initializing...');
                
                // Get URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                this.currentQuery = urlParams.get('q') || 'test';
                this.currentType = urlParams.get('type') || 'all';
                
                console.log('Query:', this.currentQuery, 'Type:', this.currentType);
                
                // Set up event listeners
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        this.switchTab(tab.dataset.type);
                    });
                });
                
                // Update active tab
                this.updateActiveTab();
                
                // Load results
                this.loadResults();
            }
            
            switchTab(type) {
                this.currentType = type;
                this.updateActiveTab();
                this.loadResults();
            }
            
            updateActiveTab() {
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === this.currentType);
                });
            }
            
            loadResults() {
                console.log('Loading results for:', this.currentQuery, 'type:', this.currentType);
                
                // Show loading
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results-container').innerHTML = '';
                
                // Generate demo results
                setTimeout(() => {
                    const results = this.getDemoResults(this.currentQuery, this.currentType);
                    this.renderResults(results);
                    document.getElementById('loading').style.display = 'none';
                }, 500);
            }
            
            getDemoResults(query, searchType) {
                const results = [];
                
                if (searchType === 'all') {
                    const types = ['web', 'images', 'news', 'videos'];
                    for (let i = 0; i < 12; i++) {
                        const type = types[i % types.length];
                        results.push({
                            title: `[${type.toUpperCase()}] ${query} - Demo Result ${i + 1}`,
                            link: `https://example.com/demo-${type}-${i + 1}`,
                            snippet: `This is a demo ${type} search result for "${query}".`,
                            resultType: type
                        });
                    }
                } else {
                    for (let i = 0; i < 10; i++) {
                        results.push({
                            title: `${query} - Demo ${searchType} Result ${i + 1}`,
                            link: `https://example.com/demo-${searchType}-${i + 1}`,
                            snippet: `This is a demo ${searchType} search result for "${query}".`,
                            resultType: searchType
                        });
                    }
                }
                
                return results;
            }
            
            renderResults(results) {
                console.log('Rendering', results.length, 'results');
                
                const container = document.getElementById('results-container');
                container.innerHTML = '';
                
                results.forEach(result => {
                    const div = document.createElement('div');
                    div.className = 'result-item';
                    
                    const badge = this.currentType === 'all' ? 
                        `<span class="result-type-badge">${result.resultType.toUpperCase()}</span>` : '';
                    
                    div.innerHTML = `
                        <a href="${result.link}" class="result-title">${result.title}</a>
                        ${badge}
                        <div class="result-snippet">${result.snippet}</div>
                    `;
                    
                    container.appendChild(div);
                });
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleSearchApp();
        });
    </script>
</body>
</html>
