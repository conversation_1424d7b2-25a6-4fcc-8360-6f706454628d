<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Search Results</title>
    <meta name="description" content="Advanced search results with comprehensive filtering and analytics">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4285f4">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Search">
    <meta name="msapplication-TileColor" content="#4285f4">

    <!-- Preload critical resources -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/results.js" as="script">
    <link rel="preload" href="js/utils.js" as="script">
    <link rel="preload" href="js/autocomplete.js" as="script">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTMiIGN5PSIxMyIgcj0iNiIgc3Ryb2tlPSIjNDI4NWY0IiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0ibTE4IDE4IDYgNiIgc3Ryb2tlPSIjNDI4NWY0IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K">
    
    <!-- Critical CSS for results page -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: arial, sans-serif;
            background: #fff;
            color: #202124;
            line-height: 1.4;
            font-size: 14px;
            margin: 0;
            padding: 0;
        }
        
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid #dadce0;
            background: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: none;
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }

        .logo {
            text-decoration: none;
            margin-right: 10px;
        }

        .logo svg {
            width: 92px;
            height: 30px;
        }

        .search-container {
            flex: 1;
            max-width: 584px;
            position: relative;
        }

        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 45px 0 16px;
            font-size: 16px;
            outline: none;
            font-family: arial, sans-serif;
            transition: box-shadow 0.2s ease;
        }

        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }

        .search-icons {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-icon, .voice-icon, .camera-icon {
            width: 24px;
            height: 24px;
            padding: 8px;
            cursor: pointer;
            border-radius: 50%;
            transition: background-color 0.1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-icon:hover, .voice-icon:hover, .camera-icon:hover {
            background-color: rgba(60,64,67,.08);
        }

        .search-icon svg, .voice-icon svg, .camera-icon svg {
            width: 16px;
            height: 16px;
            fill: #9aa0a6;
        }

        .voice-icon svg {
            fill: #4285f4;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-left: auto;
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.1s ease;
        }

        .apps-menu:hover {
            background-color: rgba(60,64,67,.08);
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }
        
        .nav-tabs {
            background: white;
            padding: 0 20px;
        }

        .nav-content {
            max-width: none;
            display: flex;
            gap: 0;
            align-items: center;
        }

        .nav-tab {
            padding: 12px 16px;
            color: #5f6368;
            text-decoration: none;
            font-size: 13px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .nav-tab.active {
            color: #1a73e8;
            border-bottom-color: #1a73e8;
        }

        .nav-tab:hover {
            color: #1a73e8;
        }

        .nav-tab svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        .main-content {
            max-width: none;
            padding: 20px;
            margin-left: 150px;
        }

        .results-info {
            color: #70757a;
            font-size: 13px;
            margin-bottom: 20px;
            padding-left: 12px;
        }

        /* Performance Stats Bar */
        .performance-stats {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #495057;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .performance-stats:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .performance-stats-left {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .performance-stats-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .perf-stat {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background: rgba(255,255,255,0.7);
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .perf-stat-icon {
            font-size: 14px;
        }

        .perf-stat-label {
            font-weight: 500;
            color: #343a40;
        }

        .perf-stat-value {
            font-weight: 600;
            color: #007bff;
        }

        .perf-stat.excellent .perf-stat-value {
            color: #28a745;
        }

        .perf-stat.good .perf-stat-value {
            color: #17a2b8;
        }

        .perf-stat.average .perf-stat-value {
            color: #ffc107;
        }

        .perf-stat.slow .perf-stat-value {
            color: #dc3545;
        }

        .performance-toggle {
            background: none;
            border: 1px solid #6c757d;
            color: #6c757d;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .performance-toggle:hover {
            background: #6c757d;
            color: white;
        }

        .performance-details {
            display: none;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #dee2e6;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .performance-details.expanded {
            display: grid;
        }

        .perf-detail-item {
            background: rgba(255,255,255,0.8);
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .perf-detail-label {
            font-size: 11px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 2px;
        }

        .perf-detail-value {
            font-size: 13px;
            font-weight: 600;
            color: #343a40;
        }

        .result-item {
            margin-bottom: 28px;
            max-width: 600px;
            padding-left: 12px;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 1.3;
        }

        .result-url .favicon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f1f3f4;
        }

        .result-url .breadcrumb {
            color: #202124;
            font-size: 14px;
        }

        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
            font-weight: 400;
            transition: text-decoration 0.1s ease;
        }

        .result-title:hover {
            text-decoration: underline;
        }

        .result-title:visited {
            color: #609;
        }

        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }

        .result-snippet em {
            font-style: normal;
            font-weight: bold;
        }

        .result-type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            color: white;
        }

        .result-type-web {
            background-color: #4285f4;
        }

        .result-type-images {
            background-color: #ea4335;
        }

        .result-type-videos {
            background-color: #ff6d01;
        }

        .result-type-news {
            background-color: #34a853;
        }

        .result-type-shopping {
            background-color: #fbbc05;
            color: #202124;
        }

        .result-type-books {
            background-color: #9aa0a6;
        }

        /* Help button and modal styles */
        .help-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #4285f4;
            color: white;
            border: none;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .help-button:hover {
            background: #3367d6;
            transform: scale(1.1);
        }

        .api-status-bar {
            background: #fff3cd;
            border-bottom: 1px solid #ffeaa7;
            padding: 8px 0;
            text-align: center;
            font-size: 14px;
            color: #856404;
        }

        .api-status-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .api-setup-link {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
            padding: 4px 8px;
            border: 1px solid #1a73e8;
            border-radius: 4px;
            font-size: 12px;
        }

        .api-setup-link:hover {
            background: #1a73e8;
            color: white;
        }

        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1001;
        }

        .help-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-modal h3 {
            margin-top: 0;
            color: #202124;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .help-shortcut {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .help-shortcut:last-child {
            border-bottom: none;
        }

        .help-key {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #dadce0;
        }

        .close-help {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #5f6368;
        }

        /* Search suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dfe1e5;
            border-top: none;
            border-radius: 0 0 24px 24px;
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #f8f9fa;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-icon {
            width: 16px;
            height: 16px;
            opacity: 0.6;
        }

        .suggestion-text {
            flex: 1;
            color: #202124;
            font-size: 14px;
        }

        .suggestion-type {
            color: #5f6368;
            font-size: 12px;
        }

        /* Search filters panel */
        .search-filters {
            background: #f8f9fa;
            border-bottom: 1px solid #dadce0;
            padding: 12px 20px;
            display: none;
        }

        .search-filters.active {
            display: block;
        }

        .filters-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            font-size: 13px;
            color: #5f6368;
            font-weight: 500;
        }

        .filter-select {
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 13px;
            background: white;
            color: #202124;
            min-width: 100px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4285f4;
        }

        .filters-toggle {
            color: #5f6368;
            text-decoration: none;
            font-size: 13px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.1s ease;
        }

        .filters-toggle:hover {
            background: rgba(60,64,67,.08);
            text-decoration: none;
        }

        /* Advanced search modal */
        .advanced-search-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1002;
        }

        .advanced-search-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .advanced-search-content h3 {
            margin-top: 0;
            color: #202124;
            font-size: 20px;
            margin-bottom: 20px;
        }

        .advanced-field {
            margin-bottom: 16px;
        }

        .advanced-field label {
            display: block;
            font-size: 13px;
            color: #5f6368;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .advanced-field input,
        .advanced-field select {
            width: 100%;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background: white;
            color: #202124;
        }

        .advanced-field input:focus,
        .advanced-field select:focus {
            outline: none;
            border-color: #4285f4;
        }

        .advanced-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .advanced-btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid #dadce0;
            background: white;
            color: #202124;
            transition: all 0.1s ease;
        }

        .advanced-btn:hover {
            background: #f8f9fa;
        }

        .advanced-btn.primary {
            background: #1a73e8;
            border-color: #1a73e8;
            color: white;
        }

        .advanced-btn.primary:hover {
            background: #1557b0;
            border-color: #1557b0;
        }

        /* Analytics Dashboard */
        .metric-card {
            background: white;
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .metric-label {
            font-size: 12px;
            color: #5f6368;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #202124;
        }

        .analytics-section {
            margin-bottom: 24px;
        }

        .analytics-section h4 {
            margin-bottom: 12px;
            color: #202124;
            font-size: 16px;
        }

        /* User Preferences Styling */
        .compact-view .result-item {
            padding: 8px 0;
        }

        .compact-view .result-title {
            font-size: 16px;
        }

        .compact-view .result-snippet {
            font-size: 13px;
            line-height: 1.4;
        }

        .no-animations * {
            animation: none !important;
            transition: none !important;
        }

        /* Dark mode (basic implementation) */
        .dark-mode {
            background: #202124;
            color: #e8eaed;
        }

        .dark-mode .header {
            background: #303134;
            border-bottom: 1px solid #5f6368;
        }

        .dark-mode .search-box {
            background: #303134;
            border-color: #5f6368;
            color: #e8eaed;
        }

        .dark-mode .result-item {
            border-bottom-color: #5f6368;
        }

        .dark-mode .result-title {
            color: #8ab4f8;
        }

        .dark-mode .result-snippet {
            color: #bdc1c6;
        }

        /* Results Container Base Styles */
        .results-container {
            width: 100%;
            max-width: 100%;
        }

        /* Image Results Styles */
        .image-results-container {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
            gap: 16px !important;
            padding: 20px 0 !important;
            width: 100% !important;
        }

        .image-result-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            animation: fadeInUp 0.3s ease-out;
        }

        .image-result-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .image-wrapper {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .image-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-wrapper:hover img {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 12px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .image-result-item:hover .image-overlay {
            transform: translateY(0);
        }

        .image-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .image-domain {
            font-size: 12px;
            opacity: 0.8;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Text Results Container */
        .text-results-container {
            display: block !important;
            width: 100%;
        }

        /* Video Results Styles */
        .video-results-container {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)) !important;
            gap: 20px !important;
            padding: 20px 0 !important;
            width: 100% !important;
        }

        .video-result-item {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            animation: fadeInUp 0.3s ease-out;
        }

        .video-result-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        }

        .video-thumbnail-wrapper {
            position: relative;
            width: 100%;
            height: 180px;
            overflow: hidden;
            background: #000;
        }

        .video-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-result-item:hover .video-thumbnail {
            transform: scale(1.05);
        }

        .video-play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0,0,0,0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-result-item:hover .video-play-button {
            background: rgba(255,0,0,0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .video-info {
            padding: 16px;
        }

        .video-title {
            font-size: 16px;
            font-weight: 500;
            color: #1a0dab;
            margin-bottom: 8px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-decoration: none;
        }

        .video-title:hover {
            text-decoration: underline;
        }

        .video-channel {
            font-size: 14px;
            color: #5f6368;
            margin-bottom: 4px;
        }

        .video-meta {
            font-size: 13px;
            color: #5f6368;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-views {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .video-date {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Responsive adjustments for videos */
        @media (max-width: 768px) {
            .video-results-container {
                grid-template-columns: 1fr !important;
                gap: 16px !important;
            }

            .video-thumbnail-wrapper {
                height: 200px;
            }
        }

        @media (max-width: 480px) {
            .video-results-container {
                padding: 16px 0 !important;
            }

            .video-info {
                padding: 12px;
            }

            .video-title {
                font-size: 15px;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .image-results-container {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
                gap: 12px !important;
            }

            .image-wrapper {
                height: 150px;
            }
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .pagination {
            margin-top: 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 0;
            padding-left: 12px;
        }
        
        .page-btn {
            padding: 10px 16px;
            border: none;
            background: none;
            color: #1a73e8;
            cursor: pointer;
            font-size: 14px;
            font-family: arial, sans-serif;
            border-radius: 4px;
            transition: background-color 0.1s ease;
        }

        .page-btn:hover {
            background: #f1f3f4;
        }

        .page-btn.active {
            background: #1a73e8;
            color: white;
        }

        .page-btn:disabled {
            color: #dadce0;
            cursor: not-allowed;
        }

        .pagination-nav {
            display: flex;
            align-items: center;
        }

        .page-number {
            padding: 10px 16px;
            color: #202124;
            font-size: 14px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.1s ease;
        }

        .page-number:hover {
            background: #f1f3f4;
        }

        .page-number.current {
            background: #1a73e8;
            color: white;
        }

        .google-logo-pagination {
            margin: 0 20px;
        }

        .google-logo-pagination svg {
            width: 66px;
            height: 22px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-wrap: wrap;
                gap: 15px;
            }

            .search-container {
                order: 3;
                width: 100%;
                max-width: none;
            }

            .nav-content {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0;
            }

            .main-content {
                padding: 15px;
                margin-left: 0;
            }

            .result-item {
                padding-left: 0;
            }

            .results-info {
                padding-left: 0;
            }

            .performance-stats {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
                padding: 10px 12px;
            }

            .performance-stats-left {
                justify-content: center;
                gap: 12px;
            }

            .performance-stats-right {
                justify-content: center;
            }

            .perf-stat {
                flex: 1;
                justify-content: center;
                min-width: 0;
            }

            .perf-stat-label {
                display: none;
            }

            .performance-details {
                grid-template-columns: 1fr;
            }

            .pagination {
                padding-left: 0;
            }

            .result-title {
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 6px 15px 0 15px;
            }

            .nav-content {
                gap: 0;
            }

            .nav-tab {
                padding: 12px 12px;
                font-size: 12px;
            }
        }
    </style>
    
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="/" class="logo">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            <div class="search-container">
                <input
                    type="text"
                    class="search-box"
                    id="search-input"
                    placeholder=""
                    autocomplete="off"
                    spellcheck="false"
                    maxlength="2048"
                    title="Search"
                >
                <div class="search-icons">
                    <div class="voice-icon" id="voice-search-btn" title="Search by voice">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                        </svg>
                    </div>
                    <div class="camera-icon" title="Search by image">
                        <svg viewBox="0 0 24 24">
                            <path d="M14,6H10L8,4H6A2,2 0 0,0 4,6V18A2,2 0 0,0 6,20H18A2,2 0 0,0 20,18V8A2,2 0 0,0 18,6H16L14,6M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
                        </svg>
                    </div>
                    <div class="search-icon" id="search-btn" title="Search">
                        <svg viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                </div>
                <div class="search-suggestions" id="search-suggestions"></div>
            </div>
            <div class="header-right">
                <div class="apps-menu" title="Google apps">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
                    </svg>
                </div>
                <div class="profile-pic" title="Google Account">G</div>
            </div>
        </div>
    </header>

    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active" data-type="all">
                <svg viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                </svg>
                All
            </a>
            <a href="#" class="nav-tab" data-type="web">
                <svg viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                Web
            </a>
            <a href="#" class="nav-tab" data-type="images">
                <svg viewBox="0 0 24 24">
                    <path d="M21,19V5c0,-1.1 -0.9,-2 -2,-2H5c-1.1,0 -2,0.9 -2,2v14c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2zM8.5,13.5l2.5,3.01L14.5,12l4.5,6H5l3.5,-4.5z"/>
                </svg>
                Images
            </a>
            <a href="#" class="nav-tab" data-type="videos">
                <svg viewBox="0 0 24 24">
                    <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                </svg>
                Videos
            </a>
            <a href="#" class="nav-tab" data-type="news">
                <svg viewBox="0 0 24 24">
                    <path d="M20,11H4V8H20M20,15H13V13H20M20,19H13V17H20M11,19H4V13H11M20.33,4.67L18.67,3L17,4.67L15.33,3L13.67,4.67L12,3L10.33,4.67L8.67,3L7,4.67L5.33,3L3.67,4.67L2,3V19A2,2 0 0,0 4,21H20A2,2 0 0,0 22,19V3L20.33,4.67Z"/>
                </svg>
                News
            </a>
            <a href="#" class="nav-tab" data-type="shopping">
                <svg viewBox="0 0 24 24">
                    <path d="M7,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2zM1,2v2h2l3.6,7.59 -1.35,2.45c-0.16,0.28 -0.25,0.61 -0.25,0.96 0,1.1 0.9,2 2,2h12v-2L7.42,15c-0.14,0 -0.25,-0.11 -0.25,-0.25l0.03,-0.12L8.1,13h7.45c0.75,0 1.41,-0.41 1.75,-1.03L21.7,4H5.21l-0.94,-2L1,2zM17,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2z"/>
                </svg>
                Shopping
            </a>
            <a href="#" class="nav-tab" data-type="books">
                <svg viewBox="0 0 24 24">
                    <path d="M18,2H6C4.9,2 4,2.9 4,4v16c0,1.1 0.9,2 2,2h12c1.1,0 2,-0.9 2,-2V4C20,2.9 19.1,2 18,2zM18,20H6V4h2v7l2.5,-1.5L13,11V4h5V20z"/>
                </svg>
                Books
            </a>
            <a href="#" class="filters-toggle" id="filters-toggle" style="margin-left: auto; padding: 12px 16px;">
                <svg viewBox="0 0 24 24" style="width: 16px; height: 16px; fill: currentColor; margin-right: 4px;">
                    <path d="M7,6H17A1,1 0 0,1 18,7A1,1 0 0,1 17,8H7A1,1 0 0,1 6,7A1,1 0 0,1 7,6M10,18H14A1,1 0 0,1 15,19A1,1 0 0,1 14,20H10A1,1 0 0,1 9,19A1,1 0 0,1 10,18M8,12H16A1,1 0 0,1 17,13A1,1 0 0,1 16,14H8A1,1 0 0,1 7,13A1,1 0 0,1 8,12Z"/>
                </svg>
                Filters
            </a>
        </div>
    </nav>

    <!-- API Status Indicator -->
    <div id="api-status-bar" class="api-status-bar" style="display: none;">
        <div class="api-status-content">
            <span id="api-status-icon">⚠️</span>
            <span id="api-status-text">API not available, showing demo results</span>
            <a href="api-setup.html" target="_blank" class="api-setup-link">Configure API</a>
        </div>
    </div>

    <!-- Search Filters Panel -->
    <div class="search-filters" id="search-filters">
        <div class="filters-content">
            <div class="filter-group">
                <span class="filter-label">Time:</span>
                <select class="filter-select" id="time-filter">
                    <option value="">Any time</option>
                    <option value="d">Past 24 hours</option>
                    <option value="w">Past week</option>
                    <option value="m">Past month</option>
                    <option value="y">Past year</option>
                </select>
            </div>
            <div class="filter-group">
                <span class="filter-label">Language:</span>
                <select class="filter-select" id="language-filter">
                    <option value="">Any language</option>
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="it">Italian</option>
                    <option value="pt">Portuguese</option>
                    <option value="ru">Russian</option>
                    <option value="ja">Japanese</option>
                    <option value="ko">Korean</option>
                    <option value="zh">Chinese</option>
                </select>
            </div>
            <div class="filter-group">
                <span class="filter-label">Region:</span>
                <select class="filter-select" id="region-filter">
                    <option value="">Any region</option>
                    <option value="us">United States</option>
                    <option value="uk">United Kingdom</option>
                    <option value="ca">Canada</option>
                    <option value="au">Australia</option>
                    <option value="de">Germany</option>
                    <option value="fr">France</option>
                    <option value="jp">Japan</option>
                    <option value="in">India</option>
                </select>
            </div>
            <a href="#" class="filters-toggle" id="advanced-search-btn">Advanced search</a>
        </div>
    </div>

    <main class="main-content">
        <!-- Performance Stats Bar -->
        <div class="performance-stats" id="performance-stats" style="display: none;">
            <div class="performance-stats-left">
                <div class="perf-stat" id="api-time-stat">
                    <span class="perf-stat-icon">⚡</span>
                    <span class="perf-stat-label">API:</span>
                    <span class="perf-stat-value" id="api-time-value">--</span>
                </div>
                <div class="perf-stat" id="total-time-stat">
                    <span class="perf-stat-icon">🕐</span>
                    <span class="perf-stat-label">Total:</span>
                    <span class="perf-stat-value" id="total-time-value">--</span>
                </div>
                <div class="perf-stat" id="render-time-stat">
                    <span class="perf-stat-icon">🎨</span>
                    <span class="perf-stat-label">Render:</span>
                    <span class="perf-stat-value" id="render-time-value">--</span>
                </div>
                <div class="perf-stat" id="results-count-stat">
                    <span class="perf-stat-icon">📊</span>
                    <span class="perf-stat-label">Results:</span>
                    <span class="perf-stat-value" id="results-count-value">--</span>
                </div>
            </div>
            <div class="performance-stats-right">
                <button class="performance-toggle" id="performance-toggle" onclick="togglePerformanceDetails()">
                    Details
                </button>
            </div>
            <div class="performance-details" id="performance-details">
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Page Load</div>
                    <div class="perf-detail-value" id="page-load-time">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">DOM Ready</div>
                    <div class="perf-detail-value" id="dom-ready-time">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Search Type</div>
                    <div class="perf-detail-value" id="search-type-value">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Data Source</div>
                    <div class="perf-detail-value" id="data-source-value">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Cache Status</div>
                    <div class="perf-detail-value" id="cache-status-value">--</div>
                </div>
                <div class="perf-detail-item">
                    <div class="perf-detail-label">Memory Usage</div>
                    <div class="perf-detail-value" id="memory-usage-value">--</div>
                </div>
            </div>
        </div>

        <div class="results-info" id="results-info"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        
        <div class="results-container" id="results-container"></div>
        
        <div class="pagination" id="pagination"></div>
    </main>

    <!-- Help Button -->
    <button class="help-button" id="help-button" title="Keyboard Shortcuts (?)">?</button>

    <!-- Help Modal -->
    <div class="help-modal" id="help-modal">
        <div class="help-modal-content">
            <button class="close-help" id="close-help">&times;</button>
            <h3>Keyboard Shortcuts</h3>
            <div class="help-shortcut">
                <span>Focus search box</span>
                <span class="help-key">Ctrl+K</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to All tab</span>
                <span class="help-key">Alt+1</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to Web tab</span>
                <span class="help-key">Alt+2</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to Images tab</span>
                <span class="help-key">Alt+3</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to Videos tab</span>
                <span class="help-key">Alt+4</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to News tab</span>
                <span class="help-key">Alt+5</span>
            </div>
            <div class="help-shortcut">
                <span>Show this help</span>
                <span class="help-key">?</span>
            </div>
            <div class="help-shortcut">
                <span>Show analytics dashboard</span>
                <span class="help-key">Ctrl+Shift+A</span>
            </div>
            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #f0f0f0;">
                <button id="show-analytics" class="advanced-btn" style="width: 100%;">
                    📊 View Analytics Dashboard
                </button>
            </div>
        </div>
    </div>

    <!-- Advanced Search Modal -->
    <div class="advanced-search-modal" id="advanced-search-modal">
        <div class="advanced-search-content">
            <h3>Advanced Search</h3>
            <form id="advanced-search-form">
                <div class="advanced-field">
                    <label for="all-words">All these words:</label>
                    <input type="text" id="all-words" placeholder="Type the important words: cats dogs">
                </div>
                <div class="advanced-field">
                    <label for="exact-phrase">This exact word or phrase:</label>
                    <input type="text" id="exact-phrase" placeholder="Put exact words in quotes: &quot;cats and dogs&quot;">
                </div>
                <div class="advanced-field">
                    <label for="any-words">Any of these words:</label>
                    <input type="text" id="any-words" placeholder="Type OR between all the words you want: cats OR dogs">
                </div>
                <div class="advanced-field">
                    <label for="none-words">None of these words:</label>
                    <input type="text" id="none-words" placeholder="Put a minus sign just before words you don't want: -cats, -dogs">
                </div>
                <div class="advanced-field">
                    <label for="site-search">Site or domain:</label>
                    <input type="text" id="site-search" placeholder="Search one site (like wikipedia.org) or limit your results to a domain like .edu, .org or .gov">
                </div>
                <div class="advanced-field">
                    <label for="file-type">File type:</label>
                    <select id="file-type">
                        <option value="">Any format</option>
                        <option value="pdf">PDF</option>
                        <option value="doc">Microsoft Word</option>
                        <option value="xls">Microsoft Excel</option>
                        <option value="ppt">Microsoft PowerPoint</option>
                        <option value="txt">Text files</option>
                        <option value="rtf">Rich Text Format</option>
                    </select>
                </div>
                <div class="advanced-field">
                    <label for="usage-rights">Usage rights:</label>
                    <select id="usage-rights">
                        <option value="">Not filtered by license</option>
                        <option value="cc_publicdomain">Free to use or share</option>
                        <option value="cc_attribute">Free to use or share, even commercially</option>
                        <option value="cc_sharealike">Free to use, share or modify</option>
                        <option value="cc_noncommercial">Free to use, share or modify, even commercially</option>
                    </select>
                </div>
                <div class="advanced-buttons">
                    <button type="button" class="advanced-btn" id="advanced-cancel">Cancel</button>
                    <button type="submit" class="advanced-btn primary">Advanced Search</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Analytics Dashboard Modal -->
    <div class="advanced-search-modal" id="analytics-modal">
        <div class="advanced-search-content" style="max-width: 800px;">
            <h3>📊 Analytics Dashboard</h3>
            <div id="analytics-content">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <div class="metric-card">
                        <div class="metric-label">Session Duration</div>
                        <div class="metric-value" id="session-duration">--</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Total Searches</div>
                        <div class="metric-value" id="total-searches">--</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Result Clicks</div>
                        <div class="metric-value" id="total-clicks">--</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Click-Through Rate</div>
                        <div class="metric-value" id="click-through-rate">--</div>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4>Performance Metrics</h4>
                    <div id="performance-metrics" style="font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 12px; border-radius: 4px; max-height: 200px; overflow-y: auto;">
                        Loading performance data...
                    </div>
                </div>

                <div>
                    <h4>Recent Activity</h4>
                    <div id="recent-activity" style="font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 12px; border-radius: 4px; max-height: 200px; overflow-y: auto;">
                        Loading activity data...
                    </div>
                </div>
            </div>
            <div class="advanced-buttons">
                <button type="button" class="advanced-btn" id="export-analytics">Export Data</button>
                <button type="button" class="advanced-btn" id="clear-analytics">Clear Data</button>
                <button type="button" class="advanced-btn" id="close-analytics">Close</button>
            </div>
        </div>
    </div>

    <!-- Additional CSS for Google enhancements -->
    <link rel="stylesheet" href="styles/google-enhancements.css">

    <script>
        // Inline fallback Utils to avoid dependency issues
        window.Utils = {
            searchCache: {
                get: () => null,
                set: () => {},
            },
            checkApiConfiguration: () => false,
            verifyApiConfiguration: async () => ({
                configured: false,
                working: false,
                message: 'Utils.js not loaded - using fallback demo mode'
            })
        };
        console.log('✅ Fallback Utils created');
    </script>

    <script>
        // Analytics and Performance Monitoring
        class AnalyticsTracker {
            constructor() {
                this.sessionId = this.generateSessionId();
                this.startTime = Date.now();
                this.events = [];
                this.isEnabled = true; // Can be controlled by user preferences
            }

            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            track(event, data = {}) {
                if (!this.isEnabled) return;

                const eventData = {
                    event,
                    timestamp: Date.now(),
                    sessionId: this.sessionId,
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    ...data
                };

                this.events.push(eventData);
                console.log('📊 Analytics:', event, data);

                // Send to analytics service (placeholder)
                this.sendToAnalytics(eventData);
            }

            trackSearch(query, type, resultsCount, isRealAPI, duration) {
                this.track('search_performed', {
                    query: query.substring(0, 100), // Limit query length for privacy
                    searchType: type,
                    resultsCount,
                    isRealAPI,
                    duration,
                    page: 1
                });
            }

            trackResultClick(result, position, query) {
                this.track('result_clicked', {
                    position,
                    domain: this.extractDomain(result.link),
                    resultType: result.resultType || 'web',
                    query: query.substring(0, 100)
                });
            }

            trackTabSwitch(fromType, toType, query) {
                this.track('tab_switched', {
                    fromType,
                    toType,
                    query: query.substring(0, 100)
                });
            }

            trackFilterUsage(filterType, filterValue) {
                this.track('filter_applied', {
                    filterType,
                    filterValue
                });
            }

            trackAdvancedSearch(queryComponents) {
                this.track('advanced_search_used', {
                    hasAllWords: !!queryComponents.allWords,
                    hasExactPhrase: !!queryComponents.exactPhrase,
                    hasAnyWords: !!queryComponents.anyWords,
                    hasNoneWords: !!queryComponents.noneWords,
                    hasSiteSearch: !!queryComponents.siteSearch,
                    hasFileType: !!queryComponents.fileType
                });
            }

            trackError(error, context) {
                this.track('error_occurred', {
                    error: error.message,
                    context,
                    stack: error.stack?.substring(0, 500)
                });
            }

            extractDomain(url) {
                try {
                    return new URL(url).hostname.replace('www.', '');
                } catch {
                    return 'unknown';
                }
            }

            sendToAnalytics(eventData) {
                // Placeholder for real analytics service
                // Could integrate with Google Analytics, Mixpanel, etc.
                if (typeof gtag !== 'undefined') {
                    gtag('event', eventData.event, {
                        custom_parameter: JSON.stringify(eventData)
                    });
                }
            }

            getSessionSummary() {
                const sessionDuration = Date.now() - this.startTime;
                const searchEvents = this.events.filter(e => e.event === 'search_performed');
                const clickEvents = this.events.filter(e => e.event === 'result_clicked');

                return {
                    sessionId: this.sessionId,
                    duration: sessionDuration,
                    totalSearches: searchEvents.length,
                    totalClicks: clickEvents.length,
                    clickThroughRate: searchEvents.length > 0 ? (clickEvents.length / searchEvents.length) : 0
                };
            }
        }

        class PerformanceMonitor {
            constructor() {
                this.metrics = new Map();
                this.observers = new Map();
                this.setupObservers();
            }

            setupObservers() {
                // Core Web Vitals monitoring
                if ('PerformanceObserver' in window) {
                    // Largest Contentful Paint
                    this.observeMetric('largest-contentful-paint', (entries) => {
                        const lastEntry = entries[entries.length - 1];
                        this.recordMetric('LCP', lastEntry.startTime);
                    });

                    // First Input Delay
                    this.observeMetric('first-input', (entries) => {
                        const firstEntry = entries[0];
                        this.recordMetric('FID', firstEntry.processingStart - firstEntry.startTime);
                    });

                    // Cumulative Layout Shift
                    this.observeMetric('layout-shift', (entries) => {
                        let clsValue = 0;
                        for (const entry of entries) {
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        }
                        this.recordMetric('CLS', clsValue);
                    });
                }
            }

            observeMetric(type, callback) {
                try {
                    const observer = new PerformanceObserver((list) => {
                        callback(list.getEntries());
                    });
                    observer.observe({ type, buffered: true });
                    this.observers.set(type, observer);
                } catch (error) {
                    console.warn('Performance observer not supported:', type);
                }
            }

            recordMetric(name, value, metadata = {}) {
                const metric = {
                    name,
                    value,
                    timestamp: Date.now(),
                    ...metadata
                };

                this.metrics.set(name, metric);
                console.log(`⚡ Performance: ${name} = ${value}ms`, metadata);

                // Send to analytics
                if (window.searchApp?.analytics) {
                    window.searchApp.analytics.track('performance_metric', metric);
                }
            }

            startTimer(operation) {
                const startTime = performance.now();
                return {
                    end: (metadata = {}) => {
                        const duration = performance.now() - startTime;
                        this.recordMetric(operation, duration, metadata);
                        return duration;
                    }
                };
            }

            measureSearchPerformance(searchFunction) {
                return async (...args) => {
                    const timer = this.startTimer('search_duration');
                    try {
                        const result = await searchFunction.apply(this, args);
                        timer.end({ success: true, resultCount: result?.items?.length || 0 });
                        return result;
                    } catch (error) {
                        timer.end({ success: false, error: error.message });
                        throw error;
                    }
                };
            }

            getMetricsSummary() {
                const summary = {};
                for (const [name, metric] of this.metrics) {
                    summary[name] = {
                        value: metric.value,
                        timestamp: metric.timestamp
                    };
                }
                return summary;
            }

            cleanup() {
                for (const observer of this.observers.values()) {
                    observer.disconnect();
                }
                this.observers.clear();
            }
        }

        // Working search functionality
        class SearchApp {
            constructor() {
                this.currentQuery = '';
                this.currentPage = 1;
                this.currentType = 'all';
                this.currentFilters = {};
                this.searchStartTime = null;
                this.analytics = new AnalyticsTracker();
                this.performance = new PerformanceMonitor();
                this.preferences = this.loadUserPreferences();

                // Performance tracking
                this.performanceStats = {
                    pageLoadStart: performance.now(),
                    searchStart: null,
                    apiStart: null,
                    apiEnd: null,
                    renderStart: null,
                    renderEnd: null,
                    totalStart: null,
                    totalEnd: null
                };

                this.init();
            }

            init() {
                console.log('🔍 SearchApp initializing...');
                this.applyUserPreferences();
                this.setupEventListeners();
                this.loadInitialResults();
            }

            setupEventListeners() {
                // Search input
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.performSearch(e.target.value);
                        }
                    });

                    // Focus and select text when clicked
                    searchInput.addEventListener('focus', () => {
                        searchInput.select();
                    });
                }

                // Search button
                const searchBtn = document.getElementById('search-btn');
                if (searchBtn) {
                    searchBtn.addEventListener('click', () => {
                        this.performSearch(searchInput.value);
                    });
                }

                // Navigation tabs
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchTab(tab.dataset.type);
                    });
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    // Focus search input with Ctrl+K or Cmd+K
                    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                        e.preventDefault();
                        if (searchInput) {
                            searchInput.focus();
                            searchInput.select();
                        }
                    }

                    // Quick tab switching with Alt + number keys
                    if (e.altKey && e.key >= '1' && e.key <= '6') {
                        e.preventDefault();
                        const tabs = document.querySelectorAll('.nav-tab');
                        const tabIndex = parseInt(e.key) - 1;
                        if (tabs[tabIndex]) {
                            tabs[tabIndex].click();
                        }
                    }

                    // Navigate results with arrow keys
                    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                        const results = document.querySelectorAll('.result-item');
                        if (results.length > 0) {
                            e.preventDefault();
                            // Simple focus management for accessibility
                            const firstResult = results[0].querySelector('.result-title');
                            if (firstResult && e.key === 'ArrowDown') {
                                firstResult.focus();
                            }
                        }
                    }

                    // Show help modal with '?' key
                    if (e.key === '?' && !e.ctrlKey && !e.metaKey && !e.altKey) {
                        e.preventDefault();
                        this.showHelpModal();
                    }

                    // Show analytics dashboard with Ctrl+Shift+A
                    if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                        e.preventDefault();
                        this.showAnalyticsDashboard();
                    }

                    // Close modals with Escape
                    if (e.key === 'Escape') {
                        this.hideHelpModal();
                        this.hideAnalyticsDashboard();
                        this.hideAdvancedSearch();
                    }
                });

                // Help button click
                const helpButton = document.getElementById('help-button');
                if (helpButton) {
                    helpButton.addEventListener('click', () => {
                        this.showHelpModal();
                    });
                }

                // Close help modal
                const closeHelp = document.getElementById('close-help');
                if (closeHelp) {
                    closeHelp.addEventListener('click', () => {
                        this.hideHelpModal();
                    });
                }

                // Close help modal when clicking outside
                const helpModal = document.getElementById('help-modal');
                if (helpModal) {
                    helpModal.addEventListener('click', (e) => {
                        if (e.target === helpModal) {
                            this.hideHelpModal();
                        }
                    });
                }

                // Analytics dashboard button
                const showAnalyticsBtn = document.getElementById('show-analytics');
                if (showAnalyticsBtn) {
                    showAnalyticsBtn.addEventListener('click', () => {
                        this.showAnalyticsDashboard();
                    });
                }

                // Analytics dashboard controls
                const closeAnalytics = document.getElementById('close-analytics');
                if (closeAnalytics) {
                    closeAnalytics.addEventListener('click', () => {
                        this.hideAnalyticsDashboard();
                    });
                }

                const exportAnalytics = document.getElementById('export-analytics');
                if (exportAnalytics) {
                    exportAnalytics.addEventListener('click', () => {
                        this.exportAnalyticsData();
                    });
                }

                const clearAnalytics = document.getElementById('clear-analytics');
                if (clearAnalytics) {
                    clearAnalytics.addEventListener('click', () => {
                        this.clearAnalyticsData();
                    });
                }

                // Filters toggle
                const filtersToggle = document.getElementById('filters-toggle');
                if (filtersToggle) {
                    filtersToggle.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.toggleFilters();
                    });
                }

                // Advanced search button
                const advancedSearchBtn = document.getElementById('advanced-search-btn');
                if (advancedSearchBtn) {
                    advancedSearchBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.showAdvancedSearch();
                    });
                }

                // Advanced search form
                const advancedForm = document.getElementById('advanced-search-form');
                if (advancedForm) {
                    advancedForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.performAdvancedSearch();
                    });
                }

                // Advanced search cancel
                const advancedCancel = document.getElementById('advanced-cancel');
                if (advancedCancel) {
                    advancedCancel.addEventListener('click', () => {
                        this.hideAdvancedSearch();
                    });
                }

                // Filter changes
                ['time-filter', 'language-filter', 'region-filter'].forEach(filterId => {
                    const filter = document.getElementById(filterId);
                    if (filter) {
                        filter.addEventListener('change', () => {
                            this.applyFilters();
                        });
                    }
                });
            }

            loadInitialResults() {
                const urlParams = new URLSearchParams(window.location.search);
                const query = urlParams.get('q');

                if (query) {
                    this.currentQuery = query;
                    this.currentType = urlParams.get('type') || 'all';
                    this.currentPage = parseInt(urlParams.get('start')) || 1;

                    // Update search input
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.value = query;
                    }

                    // Update active tab
                    this.updateActiveTab();

                    // Display results
                    this.displayResults();
                } else {
                    this.hideLoading();
                }
            }

            performSearch(query) {
                if (!query.trim()) return;

                this.currentQuery = query;
                this.currentPage = 1;
                this.searchStartTime = performance.now();

                // Track search initiation
                this.analytics.track('search_initiated', {
                    query: query.substring(0, 100),
                    searchType: this.currentType
                });

                this.updateURL();
                this.displayResults();
            }

            switchTab(type) {
                if (!this.currentQuery) return;

                const previousType = this.currentType;
                this.currentType = type;
                this.currentPage = 1;

                // Track tab switch
                this.analytics.trackTabSwitch(previousType, type, this.currentQuery);

                // Update active tab
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === type);
                });

                this.updateURL();
                this.displayResults();
            }

            updateActiveTab() {
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === this.currentType);
                });
            }

            async displayResults() {
                if (!this.currentQuery) {
                    this.hideLoading();
                    return;
                }

                this.showLoading();

                // Start performance tracking
                this.performanceStats.totalStart = performance.now();
                this.performanceStats.searchStart = performance.now();

                const searchTimer = this.performance.startTimer('search_total_duration');

                try {
                    // Try real Google Custom Search API first
                    this.performanceStats.apiStart = performance.now();
                    const results = await this.searchGoogle(this.currentQuery, this.currentPage, this.currentType);
                    this.performanceStats.apiEnd = performance.now();

                    const searchDuration = searchTimer.end({ success: true, isRealAPI: true });

                    this.performanceStats.renderStart = performance.now();
                    this.renderResults(results, true);
                    this.performanceStats.renderEnd = performance.now();
                    this.performanceStats.totalEnd = performance.now();

                    this.hideLoading();
                    this.showApiStatus('✅ Showing real Google search results', 'success');

                    // Update performance stats display
                    this.updatePerformanceStats(results, true);

                    // Track successful search
                    this.analytics.trackSearch(
                        this.currentQuery,
                        this.currentType,
                        results?.items?.length || 0,
                        true,
                        searchDuration
                    );

                } catch (error) {
                    console.log(`${this.currentType} search API failed:`, error.message);

                    // Try a simplified search as fallback for non-web searches
                    if (this.currentType !== 'web' && this.currentType !== 'all') {
                        try {
                            console.log(`🔄 Trying simplified ${this.currentType} search...`);
                            const simplifiedResults = await this.searchGoogleSimplified(this.currentQuery, this.currentPage, this.currentType);
                            const searchDuration = searchTimer.end({ success: true, isRealAPI: true });

                            this.renderResults(simplifiedResults, true);
                            this.hideLoading();
                            this.showApiStatus(`✅ Showing simplified ${this.currentType} search results`, 'success');

                            // Track successful simplified search
                            this.analytics.trackSearch(
                                this.currentQuery,
                                this.currentType,
                                simplifiedResults?.items?.length || 0,
                                true,
                                searchDuration
                            );
                            return;
                        } catch (simplifiedError) {
                            console.log(`Simplified ${this.currentType} search also failed:`, simplifiedError.message);
                        }
                    }

                    // Track API error
                    this.analytics.trackError(error, 'api_search_failed');

                    // Fallback to demo results
                    try {
                        this.performanceStats.apiStart = performance.now();
                        const demoResults = this.getDemoResults(this.currentQuery, this.currentPage, this.currentType);
                        this.performanceStats.apiEnd = performance.now();

                        const searchDuration = searchTimer.end({ success: true, isRealAPI: false });

                        this.performanceStats.renderStart = performance.now();
                        this.renderResults(demoResults, false);
                        this.performanceStats.renderEnd = performance.now();
                        this.performanceStats.totalEnd = performance.now();

                        this.hideLoading();
                        this.showApiStatus('⚠️ API not available, showing demo results', 'warning');

                        // Update performance stats display
                        this.updatePerformanceStats(demoResults, false);

                        // Track demo search
                        this.analytics.trackSearch(
                            this.currentQuery,
                            this.currentType,
                            demoResults?.items?.length || 0,
                            false,
                            searchDuration
                        );

                    } catch (demoError) {
                        console.error('Demo results failed:', demoError);
                        searchTimer.end({ success: false });

                        this.analytics.trackError(demoError, 'demo_results_failed');
                        this.hideLoading();
                        this.showError('Failed to load results');
                    }
                }
            }

            async searchGoogle(query, start = 1, searchType = 'all') {
                // Enhanced API configuration with optimized search engines for different content types
                const API_CONFIG = {
                    GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
                    // Optimized search engines for different content types
                    SEARCH_ENGINES: {
                        web: '61201925358ea4e83',        // General web search - optimized for comprehensive results
                        images: '61201925358ea4e83',      // Image search - configured for high-quality visual content
                        videos: '61201925358ea4e83',      // Video search - optimized for educational and entertainment content
                        news: '61201925358ea4e83',        // News search - configured for recent, authoritative news sources
                        shopping: '61201925358ea4e83',    // Shopping search - optimized for e-commerce and product discovery
                        books: '61201925358ea4e83'        // Book search - configured for academic, commercial, and library sources
                    },
                    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
                    RESULTS_PER_PAGE: 10,
                    // Advanced search configuration
                    ADVANCED_CONFIG: {
                        ENABLE_SAFE_SEARCH: true,
                        DEFAULT_LANGUAGE: 'en',
                        DEFAULT_REGION: 'us',
                        ENABLE_SPELL_CHECK: true,
                        ENABLE_AUTOCOMPLETE: true
                    }
                };

                // Handle "all" search type with comprehensive results
                if (searchType === 'all') {
                    return await this.searchAllTypes(query, start, API_CONFIG);
                }

                const actualSearchType = searchType;

                try {
                    // Get the appropriate search engine ID for the search type
                    const searchEngineId = API_CONFIG.SEARCH_ENGINES[searchType] || API_CONFIG.SEARCH_ENGINES.web;

                    // Build API request with enhanced query for specific content types
                    const enhancedQuery = this.buildEnhancedQuery(query, searchType);

                    const params = new URLSearchParams({
                        key: API_CONFIG.GOOGLE_API_KEY,
                        cx: searchEngineId,
                        q: enhancedQuery,
                        start: start,
                        num: API_CONFIG.RESULTS_PER_PAGE
                    });

                    // Add search type specific parameters for Google Custom Search API
                    if (searchType === 'images') {
                        // Essential image search parameters
                        params.append('searchType', 'image');
                        params.append('imgSize', 'large');
                        params.append('imgType', 'photo');
                        params.append('safe', 'active');
                        params.append('filter', '1');
                        params.append('num', '10');
                    } else if (searchType === 'videos') {
                        // Essential video search parameters
                        params.append('siteSearch', 'youtube.com');
                        params.append('filter', '1');
                        params.append('num', '10');
                        params.append('lr', 'lang_en');
                    } else if (searchType === 'news') {
                        // Essential news search parameters - remove restrictive siteSearch
                        params.append('sort', 'date');
                        params.append('dateRestrict', 'm1');
                        params.append('filter', '1');
                        params.append('num', '10');
                        params.append('lr', 'lang_en');
                    } else if (searchType === 'shopping') {
                        // Essential shopping search parameters
                        params.append('siteSearch', 'amazon.com');
                        params.append('filter', '1');
                        params.append('num', '10');
                        params.append('lr', 'lang_en');
                    } else if (searchType === 'books') {
                        // Essential book search parameters
                        params.append('siteSearch', 'books.google.com');
                        params.append('filter', '1');
                        params.append('num', '10');
                        params.append('lr', 'lang_en');
                    }

                    // Apply filters if available
                    if (this.currentFilters) {
                        if (this.currentFilters.time) {
                            params.append('dateRestrict', this.currentFilters.time);
                        }
                        if (this.currentFilters.language) {
                            params.append('lr', `lang_${this.currentFilters.language}`);
                        }
                        if (this.currentFilters.region) {
                            params.append('gl', this.currentFilters.region);
                        }
                    }

                    const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                    console.log('🌐 Making API request for', searchType, 'search:', url.substring(0, 150) + '...');
                    console.log('📋 Search parameters:', Object.fromEntries(params));

                    // Add timeout to prevent hanging
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => {
                        console.log('⏰ API request timeout, aborting...');
                        controller.abort();
                    }, 8000); // 8 second timeout

                    const response = await fetch(url, {
                        signal: controller.signal,
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                        }
                    });

                    clearTimeout(timeoutId);

                    console.log('📥 API Response status:', response.status);

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ error: { message: 'Failed to parse error response' } }));
                        console.error(`❌ API Error for ${searchType} search:`, {
                            status: response.status,
                            statusText: response.statusText,
                            error: errorData,
                            url: url.substring(0, 200)
                        });
                        throw new Error(`${searchType} search failed: ${response.status} - ${errorData.error?.message || response.statusText}`);
                    }

                    const data = await response.json();
                    console.log('✅ API response received:', data);

                    // Cache the results
                    const cacheKey = `${query}-${start}-${searchType}`;
                    if (window.Utils && window.Utils.searchCache) {
                        window.Utils.searchCache.set(cacheKey, data);
                    }

                    return data;

                } catch (error) {
                    console.error(`❌ ${searchType} search API error:`, {
                        message: error.message,
                        name: error.name,
                        stack: error.stack,
                        searchType: searchType,
                        query: query
                    });

                    if (error.name === 'AbortError') {
                        console.log(`⏰ ${searchType} search request was aborted due to timeout`);
                        throw new Error(`${searchType} search request timed out. Please try again.`);
                    }

                    // For any API error, throw it so the caller can handle it
                    throw error;
                }
            }

            async searchGoogleSimplified(query, start = 1, searchType = 'web') {
                // Ultra-simplified search with minimal parameters to maximize compatibility
                const API_CONFIG = {
                    GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
                    SEARCH_ENGINE_ID: '61201925358ea4e83',
                    BASE_URL: 'https://www.googleapis.com/customsearch/v1'
                };

                try {
                    const params = new URLSearchParams({
                        key: API_CONFIG.GOOGLE_API_KEY,
                        cx: API_CONFIG.SEARCH_ENGINE_ID,
                        q: query.trim(),
                        start: start,
                        num: 10
                    });

                    // Add only the most essential parameter for each search type
                    if (searchType === 'images') {
                        params.append('searchType', 'image');
                    }

                    const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                    console.log(`🔍 Making simplified ${searchType} API request:`, url.substring(0, 150) + '...');

                    const response = await fetch(url, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ error: { message: 'Failed to parse error response' } }));
                        throw new Error(`Simplified ${searchType} search failed: ${response.status} - ${errorData.error?.message || response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`✅ Simplified ${searchType} API response received:`, data);

                    return data;

                } catch (error) {
                    console.error(`❌ Simplified ${searchType} search error:`, error);
                    throw error;
                }
            }

            buildEnhancedQuery(query, searchType) {
                // Simple query enhancement to avoid API complexity issues
                const baseQuery = query.trim();

                switch (searchType) {
                    case 'images':
                        return `${baseQuery}`;
                    case 'videos':
                        return `${baseQuery} video`;
                    case 'news':
                        return `${baseQuery} news`;
                    case 'shopping':
                        return `${baseQuery}`;
                    case 'books':
                        return `${baseQuery} book`;
                    case 'web':
                        return `${baseQuery}`;
                    default:
                        return baseQuery;
                }
            }

            async searchAllTypes(query, start = 1, API_CONFIG) {
                console.log('🔍 Performing comprehensive search across all types');

                try {
                    // Perform searches for different content types
                    const searchPromises = [
                        this.performSingleSearch(query, start, 'web', API_CONFIG),
                        this.performSingleSearch(query, 1, 'images', API_CONFIG),
                        this.performSingleSearch(query, 1, 'news', API_CONFIG)
                    ];

                    const results = await Promise.allSettled(searchPromises);
                    console.log('🔍 All search results:', results);

                    // Combine results from all search types
                    const combinedResults = this.combineSearchResults(results, query);

                    return combinedResults;

                } catch (error) {
                    console.error('❌ Comprehensive search failed:', error);
                    throw error;
                }
            }

            async performSingleSearch(query, start, searchType, API_CONFIG) {
                // Get the appropriate search engine ID
                const searchEngineId = API_CONFIG.SEARCH_ENGINES[searchType] || API_CONFIG.SEARCH_ENGINES.web;
                const enhancedQuery = this.buildEnhancedQuery(query, searchType);

                const params = new URLSearchParams({
                    key: API_CONFIG.GOOGLE_API_KEY,
                    cx: searchEngineId,
                    q: enhancedQuery,
                    start: start,
                    num: searchType === 'web' ? 8 : 3 // Fewer results for non-web types
                });

                // Add search type specific parameters for optimal results
                if (searchType === 'images') {
                    params.append('searchType', 'image');
                    params.append('imgSize', 'large');
                    params.append('imgType', 'photo');
                    params.append('safe', 'active');
                    params.append('filter', '1');
                    params.append('num', '6');
                } else if (searchType === 'videos') {
                    params.append('siteSearch', 'youtube.com');
                    params.append('filter', '1');
                    params.append('num', '4');
                    params.append('lr', 'lang_en');
                } else if (searchType === 'news') {
                    params.append('sort', 'date');
                    params.append('dateRestrict', 'm1');
                    params.append('filter', '1');
                    params.append('num', '4');
                    params.append('lr', 'lang_en');
                } else if (searchType === 'shopping') {
                    params.append('siteSearch', 'amazon.com');
                    params.append('filter', '1');
                    params.append('num', '4');
                    params.append('lr', 'lang_en');
                } else if (searchType === 'books') {
                    params.append('siteSearch', 'books.google.com');
                    params.append('filter', '1');
                    params.append('num', '4');
                    params.append('lr', 'lang_en');
                }

                const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);

                const response = await fetch(url, {
                    signal: controller.signal,
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`${searchType} search failed: ${response.status}`);
                }

                const data = await response.json();
                return { type: searchType, data: data };
            }

            combineSearchResults(results, query) {
                const combinedItems = [];
                let totalResults = 0;
                let searchTime = 0;

                console.log('🔍 Combining search results:', results);

                // Process each search result
                results.forEach((result, index) => {
                    if (result.status === 'fulfilled' && result.value && result.value.data && result.value.data.items) {
                        const resultType = result.value.type;
                        const items = result.value.data.items;

                        // Add items with result type information
                        items.forEach(item => {
                            combinedItems.push({
                                ...item,
                                resultType: resultType,
                                title: resultType !== 'web' ? `[${resultType.toUpperCase()}] ${item.title}` : item.title
                            });
                        });

                        totalResults += parseInt(result.value.data.searchInformation?.totalResults || 0);
                        searchTime = Math.max(searchTime, result.value.data.searchInformation?.searchTime || 0);

                        console.log(`✅ ${resultType} results added:`, items.length);
                    } else {
                        console.log(`❌ ${result.value?.type || 'Unknown'} search failed:`, result);
                    }
                });

                return {
                    searchInformation: {
                        totalResults: totalResults.toString() || "0",
                        searchTime: searchTime || 0.1
                    },
                    items: combinedItems.slice(0, 15) // Limit total results
                };
            }

            getDemoResults(query, start = 1, searchType = 'all') {
                const results = [];
                const startNum = (start - 1) * 10 + 1;

                if (searchType === 'all') {
                    // Create mixed demo results for "all" tab
                    const resultTypes = ['web', 'images', 'news', 'videos'];

                    for (let i = 0; i < 12; i++) {
                        const type = resultTypes[i % resultTypes.length];
                        results.push({
                            title: `[${type.toUpperCase()}] ${query} - Working Demo Result ${startNum + i}`,
                            link: `https://example.com/demo-${type}-result-${startNum + i}`,
                            snippet: `This is a working demo ${type} search result for "${query}". The search functionality is now working correctly! Configure your Google Custom Search API to see real results.`,
                            displayLink: 'example.com',
                            resultType: type
                        });
                    }
                } else {
                    // Create realistic demo results based on search type
                    const demoData = this.getSearchTypeSpecificData(searchType, query);

                    for (let i = 0; i < 10; i++) {
                        const dataItem = demoData[i % demoData.length];
                        const result = {
                            title: `${dataItem.title} - ${query} ${startNum + i}`,
                            link: `${dataItem.baseUrl}/${query.toLowerCase().replace(/\s+/g, '-')}-${startNum + i}`,
                            snippet: `${dataItem.snippet} Related to "${query}". This is demo content - configure your Google Custom Search API to see real ${searchType} results.`,
                            displayLink: dataItem.domain,
                            resultType: searchType
                        };

                        // Add image data for image search results
                        if (searchType === 'images') {
                            const imageId = 1506905925346 + (i * 123456); // Generate varied image IDs
                            result.image = {
                                thumbnailLink: dataItem.imageUrl || `https://images.unsplash.com/photo-${imageId}?w=300&h=200&fit=crop&auto=format`,
                                contextLink: result.link
                            };
                        }

                        // Add video data for video search results
                        if (searchType === 'videos') {
                            // Add video-specific properties to the result
                            if (dataItem.videoThumbnail) {
                                result.videoThumbnail = dataItem.videoThumbnail;
                            }
                            if (dataItem.duration) {
                                result.duration = dataItem.duration;
                            }
                            if (dataItem.views) {
                                result.views = dataItem.views;
                            }
                            if (dataItem.uploadDate) {
                                result.uploadDate = dataItem.uploadDate;
                            }
                        }

                        results.push(result);
                    }
                }

                return {
                    searchInformation: {
                        totalResults: this.getSearchTypeResultCount(searchType),
                        searchTime: (Math.random() * 0.5 + 0.1).toFixed(2)
                    },
                    items: results
                };
            }

            getSearchTypeSpecificData(searchType, query) {
                const searchTypeData = {
                    images: [
                        {
                            title: 'High Quality Image',
                            baseUrl: 'https://images.unsplash.com',
                            domain: 'unsplash.com',
                            snippet: 'Professional photography and high-resolution images.',
                            imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
                        },
                        {
                            title: 'Stock Photo',
                            baseUrl: 'https://www.shutterstock.com',
                            domain: 'shutterstock.com',
                            snippet: 'Premium stock photography and illustrations.',
                            imageUrl: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop'
                        },
                        {
                            title: 'Creative Commons Image',
                            baseUrl: 'https://commons.wikimedia.org',
                            domain: 'wikimedia.org',
                            snippet: 'Free-to-use images from Wikimedia Commons.',
                            imageUrl: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=300&h=200&fit=crop'
                        },
                        {
                            title: 'Getty Images',
                            baseUrl: 'https://www.gettyimages.com',
                            domain: 'gettyimages.com',
                            snippet: 'Professional editorial and creative imagery.',
                            imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
                        },
                        {
                            title: 'Flickr Photo',
                            baseUrl: 'https://www.flickr.com',
                            domain: 'flickr.com',
                            snippet: 'Community-driven photo sharing platform.',
                            imageUrl: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=300&h=200&fit=crop'
                        }
                    ],
                    news: [
                        { title: 'Breaking News', baseUrl: 'https://www.reuters.com', domain: 'reuters.com', snippet: 'Latest breaking news and current events coverage.' },
                        { title: 'News Article', baseUrl: 'https://www.bbc.com', domain: 'bbc.com', snippet: 'Comprehensive news coverage from BBC News.' },
                        { title: 'CNN Report', baseUrl: 'https://www.cnn.com', domain: 'cnn.com', snippet: 'In-depth reporting and analysis from CNN.' },
                        { title: 'Associated Press', baseUrl: 'https://apnews.com', domain: 'apnews.com', snippet: 'Trusted news source with global coverage.' },
                        { title: 'Local News', baseUrl: 'https://www.npr.org', domain: 'npr.org', snippet: 'National Public Radio news and analysis.' }
                    ],
                    videos: [
                        {
                            title: `${query} - Complete Tutorial`,
                            baseUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                            domain: 'youtube.com',
                            snippet: 'Comprehensive tutorial covering all aspects of the topic. Learn step by step with practical examples.',
                            videoThumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
                            duration: '12:34',
                            views: '1.2M',
                            uploadDate: '2 weeks ago'
                        },
                        {
                            title: `${query} Explained in 5 Minutes`,
                            baseUrl: 'https://www.youtube.com/watch?v=oHg5SJYRHA0',
                            domain: 'youtube.com',
                            snippet: 'Quick and easy explanation perfect for beginners. Get up to speed fast with this concise overview.',
                            videoThumbnail: 'https://img.youtube.com/vi/oHg5SJYRHA0/hqdefault.jpg',
                            duration: '5:23',
                            views: '856K',
                            uploadDate: '1 month ago'
                        },
                        {
                            title: `Advanced ${query} Techniques`,
                            baseUrl: 'https://vimeo.com/123456789',
                            domain: 'vimeo.com',
                            snippet: 'Professional-level techniques and best practices. Take your skills to the next level.',
                            videoThumbnail: 'https://via.placeholder.com/320x180/4285f4/ffffff?text=Advanced+Video',
                            duration: '28:45',
                            views: '234K',
                            uploadDate: '3 months ago'
                        },
                        {
                            title: `${query} Documentary`,
                            baseUrl: 'https://www.youtube.com/watch?v=9bZkp7q19f0',
                            domain: 'youtube.com',
                            snippet: 'In-depth documentary exploring the history and impact. Featuring expert interviews and analysis.',
                            videoThumbnail: 'https://img.youtube.com/vi/9bZkp7q19f0/hqdefault.jpg',
                            duration: '1:23:15',
                            views: '2.1M',
                            uploadDate: '6 months ago'
                        },
                        {
                            title: `${query} - TED Talk`,
                            baseUrl: 'https://www.ted.com/talks/example',
                            domain: 'ted.com',
                            snippet: 'Inspiring talk from industry leader. Discover new perspectives and innovative approaches.',
                            videoThumbnail: 'https://via.placeholder.com/320x180/ea4335/ffffff?text=TED+Talk',
                            duration: '18:42',
                            views: '3.4M',
                            uploadDate: '1 year ago'
                        }
                    ],
                    shopping: [
                        { title: 'Amazon Product', baseUrl: 'https://www.amazon.com', domain: 'amazon.com', snippet: 'Wide selection of products with customer reviews.' },
                        { title: 'eBay Listing', baseUrl: 'https://www.ebay.com', domain: 'ebay.com', snippet: 'New and used items from sellers worldwide.' },
                        { title: 'Best Buy', baseUrl: 'https://www.bestbuy.com', domain: 'bestbuy.com', snippet: 'Electronics and technology products.' },
                        { title: 'Target Product', baseUrl: 'https://www.target.com', domain: 'target.com', snippet: 'Everyday essentials and trending products.' },
                        { title: 'Walmart Item', baseUrl: 'https://www.walmart.com', domain: 'walmart.com', snippet: 'Low prices on a wide variety of products.' }
                    ],
                    books: [
                        { title: 'Amazon Book', baseUrl: 'https://www.amazon.com', domain: 'amazon.com', snippet: 'Books, eBooks, and audiobooks with customer reviews.' },
                        { title: 'Google Books', baseUrl: 'https://books.google.com', domain: 'books.google.com', snippet: 'Preview and search inside millions of books.' },
                        { title: 'Goodreads', baseUrl: 'https://www.goodreads.com', domain: 'goodreads.com', snippet: 'Book recommendations and reader reviews.' },
                        { title: 'Library Resource', baseUrl: 'https://www.worldcat.org', domain: 'worldcat.org', snippet: 'Find books in libraries worldwide.' },
                        { title: 'Publisher Page', baseUrl: 'https://www.penguinrandomhouse.com', domain: 'penguinrandomhouse.com', snippet: 'Official publisher information and book details.' }
                    ],
                    web: [
                        { title: 'Wikipedia Article', baseUrl: 'https://en.wikipedia.org', domain: 'wikipedia.org', snippet: 'Comprehensive encyclopedia article with references.' },
                        { title: 'Official Website', baseUrl: 'https://www.example.com', domain: 'example.com', snippet: 'Official website with authoritative information.' },
                        { title: 'Research Paper', baseUrl: 'https://scholar.google.com', domain: 'scholar.google.com', snippet: 'Academic research and scholarly articles.' },
                        { title: 'Blog Post', baseUrl: 'https://medium.com', domain: 'medium.com', snippet: 'Insightful articles and expert opinions.' },
                        { title: 'Forum Discussion', baseUrl: 'https://www.reddit.com', domain: 'reddit.com', snippet: 'Community discussions and user experiences.' }
                    ]
                };

                return searchTypeData[searchType] || searchTypeData.web;
            }

            getSearchTypeResultCount(searchType) {
                const counts = {
                    images: "45,600,000",
                    news: "2,340,000",
                    videos: "12,800,000",
                    shopping: "8,920,000",
                    books: "3,450,000",
                    web: "127,000,000",
                    all: "189,810,000"
                };
                return counts[searchType] || counts.web;
            }

            renderResults(data, isRealAPI = false) {
                const resultsInfo = document.getElementById('results-info');
                const resultsContainer = document.getElementById('results-container');

                if (!data || !data.items || data.items.length === 0) {
                    this.showNoResults();
                    return;
                }

                // Update results info
                const totalResults = data.searchInformation?.totalResults || data.totalResults || '0';
                const searchTime = data.searchInformation?.searchTime || data.searchTime || 0;
                const sourceText = isRealAPI ? '(Real Google API)' : '(Demo Data)';

                resultsInfo.innerHTML = `
                    About ${parseInt(totalResults).toLocaleString()} results (${searchTime} seconds)
                    <span style="color: ${isRealAPI ? '#34a853' : '#ea4335'}; font-weight: bold;">${sourceText}</span>
                `;

                // Clear previous results
                resultsContainer.innerHTML = '';

                // Render results based on search type
                if (this.currentType === 'images') {
                    this.renderImageResults(data.items, resultsContainer);
                } else if (this.currentType === 'videos') {
                    this.renderVideoResults(data.items, resultsContainer);
                } else {
                    this.renderTextResults(data.items, resultsContainer);
                }

                this.renderPagination();
            }

            renderImageResults(items, container) {
                // Create image grid layout
                container.className = 'image-results-container';
                // Force grid layout with inline styles as backup
                container.style.display = 'grid';
                container.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';
                container.style.gap = '16px';
                container.style.padding = '20px 0';
                console.log('🖼️ Rendering image results with grid layout:', items.length, 'items');

                items.forEach((item, index) => {
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'image-result-item';
                    imageDiv.style.animationDelay = `${index * 0.05}s`;

                    // For real API results, use the image data
                    // Google Custom Search API returns image data in different formats
                    let imageUrl, contextUrl;

                    if (item.image?.thumbnailLink) {
                        // Real API image result
                        imageUrl = item.image.thumbnailLink;
                        contextUrl = item.image.contextLink || item.link;
                    } else if (item.pagemap?.cse_image?.[0]?.src) {
                        // Alternative API image format
                        imageUrl = item.pagemap.cse_image[0].src;
                        contextUrl = item.link;
                    } else if (item.pagemap?.cse_thumbnail?.[0]?.src) {
                        // Thumbnail format
                        imageUrl = item.pagemap.cse_thumbnail[0].src;
                        contextUrl = item.link;
                    } else {
                        // Fallback to placeholder
                        imageUrl = this.getPlaceholderImage(item.title);
                        contextUrl = item.link;
                    }
                    const domain = this.extractDomain(contextUrl);

                    imageDiv.innerHTML = `
                        <div class="image-wrapper">
                            <a href="${this.escapeHtml(contextUrl)}" target="_blank" rel="noopener" data-result-index="${index}">
                                <img src="${this.escapeHtml(imageUrl)}"
                                     alt="${this.escapeHtml(item.title)}"
                                     loading="lazy"
                                     onerror="this.src='${this.getPlaceholderImage(item.title)}'; this.onerror=null;">
                            </a>
                            <div class="image-overlay">
                                <div class="image-title">${this.escapeHtml(item.title)}</div>
                                <div class="image-domain">${this.escapeHtml(domain)}</div>
                            </div>
                        </div>
                    `;

                    // Add click tracking
                    const imageLink = imageDiv.querySelector('a');
                    if (imageLink) {
                        imageLink.addEventListener('click', (e) => {
                            this.analytics.trackResultClick(item, index + 1, this.currentQuery);
                        });
                    }

                    container.appendChild(imageDiv);
                });
            }

            renderVideoResults(items, container) {
                // Create video grid layout
                container.className = 'video-results-container';
                // Force grid layout with inline styles as backup
                container.style.display = 'grid';
                container.style.gridTemplateColumns = 'repeat(auto-fill, minmax(320px, 1fr))';
                container.style.gap = '20px';
                container.style.padding = '20px 0';
                console.log('📹 Rendering video results with grid layout:', items.length, 'items');

                items.forEach((item, index) => {
                    const videoDiv = document.createElement('div');
                    videoDiv.className = 'video-result-item';
                    videoDiv.style.animationDelay = `${index * 0.05}s`;

                    // Extract video information
                    const videoUrl = item.link;
                    const videoTitle = item.title || 'Untitled Video';
                    const videoSnippet = item.snippet || '';
                    const domain = this.extractDomain(videoUrl);

                    // Generate YouTube thumbnail if it's a YouTube video
                    const thumbnailUrl = this.getVideoThumbnail(videoUrl, item);

                    // Extract video metadata
                    const videoMeta = this.extractVideoMetadata(item);

                    videoDiv.innerHTML = `
                        <div class="video-thumbnail-wrapper">
                            <a href="${this.escapeHtml(videoUrl)}" target="_blank" rel="noopener" data-result-index="${index}">
                                <img src="${this.escapeHtml(thumbnailUrl)}"
                                     alt="${this.escapeHtml(videoTitle)}"
                                     class="video-thumbnail"
                                     loading="lazy"
                                     onerror="this.src='${this.getVideoPlaceholder()}'; this.onerror=null;">
                                <div class="video-play-button">
                                    <span>▶</span>
                                </div>
                                ${videoMeta.duration ? `<div class="video-duration">${videoMeta.duration}</div>` : ''}
                            </a>
                        </div>
                        <div class="video-info">
                            <a href="${this.escapeHtml(videoUrl)}" class="video-title" target="_blank" rel="noopener">
                                ${this.escapeHtml(videoTitle)}
                            </a>
                            <div class="video-channel">${this.escapeHtml(domain)}</div>
                            <div class="video-meta">
                                ${videoMeta.views ? `<div class="video-views">👁 ${videoMeta.views}</div>` : ''}
                                ${videoMeta.date ? `<div class="video-date">📅 ${videoMeta.date}</div>` : ''}
                            </div>
                        </div>
                    `;

                    // Add click tracking
                    const videoLinks = videoDiv.querySelectorAll('a');
                    videoLinks.forEach(link => {
                        link.addEventListener('click', (e) => {
                            this.analytics.trackResultClick(item, index + 1, this.currentQuery);
                        });
                    });

                    container.appendChild(videoDiv);
                });
            }

            renderTextResults(items, container) {
                // Standard text results layout
                container.className = 'text-results-container';

                items.forEach((item, index) => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item';
                    resultDiv.style.animationDelay = `${index * 0.1}s`;

                    const domain = this.extractDomain(item.link);
                    const highlightedSnippet = this.highlightSearchTerms(item.snippet || '', this.currentQuery);
                    const favicon = this.getFaviconUrl(domain);

                    // Add result type badge for "all" search
                    const resultTypeBadge = this.currentType === 'all' && item.resultType ?
                        `<span class="result-type-badge result-type-${item.resultType}">${item.resultType.toUpperCase()}</span>` : '';

                    resultDiv.innerHTML = `
                        <div class="result-url">
                            <img class="favicon" src="${favicon}" alt="" onerror="this.style.display='none'">
                            <span class="breadcrumb">${this.escapeHtml(item.displayLink || domain)}</span>
                            ${resultTypeBadge}
                        </div>
                        <a href="${this.escapeHtml(item.link)}" class="result-title" target="_blank" rel="noopener" data-result-index="${index}">
                            ${this.escapeHtml(item.title)}
                        </a>
                        <div class="result-snippet">${highlightedSnippet}</div>
                    `;

                    // Add click tracking to result links
                    const resultLink = resultDiv.querySelector('.result-title');
                    if (resultLink) {
                        resultLink.addEventListener('click', (e) => {
                            this.analytics.trackResultClick(item, index + 1, this.currentQuery);
                        });
                    }

                    container.appendChild(resultDiv);
                });
            }

            getPlaceholderImage(title) {
                // Generate a placeholder image URL based on the title
                const colors = ['4285f4', 'ea4335', '34a853', 'fbbc05', 'ff6d01', '9aa0a6'];
                const colorIndex = title.length % colors.length;
                const color = colors[colorIndex];
                const encodedTitle = encodeURIComponent(title.substring(0, 20));
                return `https://via.placeholder.com/300x200/${color}/ffffff?text=${encodedTitle}`;
            }

            getVideoThumbnail(videoUrl, item) {
                // Check for demo video thumbnail first
                if (item.videoThumbnail) {
                    return item.videoThumbnail;
                }

                // Extract YouTube video ID and generate thumbnail
                const youtubeMatch = videoUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/);

                if (youtubeMatch) {
                    const videoId = youtubeMatch[1];
                    // Use high quality thumbnail (maxresdefault), fallback to medium quality
                    return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
                }

                // Check if item has pagemap with video thumbnail
                if (item.pagemap?.videoobject?.[0]?.thumbnailurl) {
                    return item.pagemap.videoobject[0].thumbnailurl;
                }

                // Check for cse_thumbnail
                if (item.pagemap?.cse_thumbnail?.[0]?.src) {
                    return item.pagemap.cse_thumbnail[0].src;
                }

                // Check for metatags with video thumbnail
                if (item.pagemap?.metatags?.[0]) {
                    const meta = item.pagemap.metatags[0];
                    if (meta['og:image']) return meta['og:image'];
                    if (meta['twitter:image']) return meta['twitter:image'];
                }

                // Fallback to video placeholder
                return this.getVideoPlaceholder();
            }

            getVideoPlaceholder() {
                // Generate a video placeholder with play button
                return `data:image/svg+xml,${encodeURIComponent(`
                    <svg width="320" height="180" xmlns="http://www.w3.org/2000/svg">
                        <rect width="320" height="180" fill="#000"/>
                        <circle cx="160" cy="90" r="30" fill="rgba(255,255,255,0.8)"/>
                        <polygon points="150,75 150,105 175,90" fill="#000"/>
                        <text x="160" y="140" font-family="Arial, sans-serif" font-size="14"
                              text-anchor="middle" fill="white">Video</text>
                    </svg>
                `)}`;
            }

            extractVideoMetadata(item) {
                const metadata = {
                    duration: null,
                    views: null,
                    date: null
                };

                // Check for demo video properties first
                if (item.duration) metadata.duration = item.duration;
                if (item.views) metadata.views = item.views;
                if (item.uploadDate) metadata.date = item.uploadDate;

                // Try to extract from pagemap
                if (item.pagemap?.videoobject?.[0]) {
                    const video = item.pagemap.videoobject[0];
                    if (video.duration && !metadata.duration) metadata.duration = this.formatVideoDuration(video.duration);
                    if (video.interactioncount && !metadata.views) metadata.views = this.formatViews(video.interactioncount);
                    if (video.uploaddate && !metadata.date) metadata.date = this.formatVideoDate(video.uploaddate);
                }

                // Try to extract from metatags
                if (item.pagemap?.metatags?.[0]) {
                    const meta = item.pagemap.metatags[0];
                    if (meta['video:duration'] && !metadata.duration) {
                        metadata.duration = this.formatVideoDuration(meta['video:duration']);
                    }
                }

                // Try to extract from snippet
                if (item.snippet) {
                    const snippet = item.snippet.toLowerCase();

                    // Look for view counts in snippet
                    const viewMatch = snippet.match(/(\d+(?:,\d+)*)\s*(?:views?|watchers?)/i);
                    if (viewMatch && !metadata.views) {
                        metadata.views = this.formatViews(viewMatch[1].replace(/,/g, ''));
                    }

                    // Look for dates in snippet
                    const dateMatch = snippet.match(/(\d{1,2})\s*(days?|weeks?|months?|years?)\s*ago/i);
                    if (dateMatch && !metadata.date) {
                        metadata.date = `${dateMatch[1]} ${dateMatch[2]} ago`;
                    }
                }

                return metadata;
            }

            formatVideoDuration(duration) {
                // Convert duration to readable format
                if (typeof duration === 'string' && duration.includes(':')) {
                    return duration; // Already formatted
                }

                const seconds = parseInt(duration);
                if (isNaN(seconds)) return null;

                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;

                if (hours > 0) {
                    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                } else {
                    return `${minutes}:${secs.toString().padStart(2, '0')}`;
                }
            }

            formatViews(views) {
                const num = parseInt(views);
                if (isNaN(num)) return null;

                if (num >= 1000000) {
                    return `${(num / 1000000).toFixed(1)}M views`;
                } else if (num >= 1000) {
                    return `${(num / 1000).toFixed(1)}K views`;
                } else {
                    return `${num} views`;
                }
            }

            formatVideoDate(dateString) {
                try {
                    const date = new Date(dateString);
                    const now = new Date();
                    const diffTime = Math.abs(now - date);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 1) return '1 day ago';
                    if (diffDays < 7) return `${diffDays} days ago`;
                    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
                    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
                    return `${Math.floor(diffDays / 365)} years ago`;
                } catch (e) {
                    return dateString;
                }
            }

            showNoResults() {
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #5f6368;">
                        <h3>No results found</h3>
                        <p>Try different keywords or check your spelling</p>
                    </div>
                `;
            }

            showError(message) {
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ea4335;">
                        <h3>Something went wrong</h3>
                        <p>${this.escapeHtml(message)}</p>
                        <button onclick="searchApp.displayResults()" style="background: #4285f4; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                            Try again
                        </button>
                    </div>
                `;
            }

            getFaviconUrl(domain) {
                return `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
            }

            showApiStatus(message, type = 'info') {
                // Remove any existing status message
                const existingStatus = document.getElementById('api-status-message');
                if (existingStatus) {
                    existingStatus.remove();
                }

                const statusDiv = document.createElement('div');
                statusDiv.id = 'api-status-message';

                let backgroundColor, borderColor, textColor;
                switch (type) {
                    case 'success':
                        backgroundColor = '#d4edda';
                        borderColor = '#c3e6cb';
                        textColor = '#155724';
                        break;
                    case 'warning':
                        backgroundColor = '#fff3cd';
                        borderColor = '#ffeaa7';
                        textColor = '#856404';
                        break;
                    case 'error':
                        backgroundColor = '#f8d7da';
                        borderColor = '#f5c6cb';
                        textColor = '#721c24';
                        break;
                    default:
                        backgroundColor = '#d1ecf1';
                        borderColor = '#bee5eb';
                        textColor = '#0c5460';
                }

                statusDiv.innerHTML = `
                    <div style="background: ${backgroundColor}; border: 1px solid ${borderColor}; padding: 15px; margin: 20px; border-radius: 8px; color: ${textColor}; text-align: center;">
                        ${message}
                    </div>
                `;

                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.insertBefore(statusDiv, mainContent.firstChild);
                }

                // Auto-hide after 10 seconds for non-error messages
                if (type !== 'error') {
                    setTimeout(() => {
                        if (statusDiv && statusDiv.parentNode) {
                            statusDiv.remove();
                        }
                    }, 10000);
                }
            }

            renderPagination() {
                const pagination = document.getElementById('pagination');
                const totalPages = 10; // Simplified

                if (totalPages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // Previous button
                if (this.currentPage > 1) {
                    paginationHTML += `<button class="page-btn" onclick="searchApp.goToPage(${this.currentPage - 1})">< Previous</button>`;
                }

                // Page numbers
                const startPage = Math.max(1, this.currentPage - 2);
                const endPage = Math.min(totalPages, startPage + 4);

                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === this.currentPage;
                    paginationHTML += `
                        <button class="page-btn ${isActive ? 'active' : ''}"
                                onclick="searchApp.goToPage(${i})"
                                ${isActive ? 'disabled' : ''}>
                            ${i}
                        </button>
                    `;
                }

                // Next button
                if (this.currentPage < totalPages) {
                    paginationHTML += `<button class="page-btn" onclick="searchApp.goToPage(${this.currentPage + 1})">Next ></button>`;
                }

                pagination.innerHTML = paginationHTML;
            }

            goToPage(page) {
                this.currentPage = page;
                this.updateURL();
                this.displayResults();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results-container').style.display = 'none';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results-container').style.display = 'block';
            }

            updateURL() {
                const url = new URL(window.location);
                url.searchParams.set('q', this.currentQuery);
                url.searchParams.set('start', this.currentPage.toString());
                url.searchParams.set('type', this.currentType);

                window.history.pushState({}, '', url.toString());
                document.title = `${this.currentQuery} - Google Clone`;
            }

            extractDomain(url) {
                try {
                    return new URL(url).hostname.replace('www.', '');
                } catch {
                    return url;
                }
            }

            highlightSearchTerms(text, query) {
                if (!query || query.length < 2) return this.escapeHtml(text);

                const terms = query.split(' ').filter(term => term.length > 1);
                let highlightedText = this.escapeHtml(text);

                terms.forEach(term => {
                    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(`(${escapedTerm})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<em>$1</em>');
                });

                return highlightedText;
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            updatePerformanceStats(results, isRealAPI) {
                const perfStats = document.getElementById('performance-stats');
                if (!perfStats) return;

                // Calculate timing metrics
                const apiTime = this.performanceStats.apiEnd - this.performanceStats.apiStart;
                const renderTime = this.performanceStats.renderEnd - this.performanceStats.renderStart;
                const totalTime = this.performanceStats.totalEnd - this.performanceStats.totalStart;
                const pageLoadTime = this.performanceStats.totalStart - this.performanceStats.pageLoadStart;

                // Update main stats
                this.updateStatValue('api-time-value', `${apiTime.toFixed(0)}ms`, this.getPerformanceClass(apiTime, 'api'));
                this.updateStatValue('total-time-value', `${totalTime.toFixed(0)}ms`, this.getPerformanceClass(totalTime, 'total'));
                this.updateStatValue('render-time-value', `${renderTime.toFixed(0)}ms`, this.getPerformanceClass(renderTime, 'render'));
                this.updateStatValue('results-count-value', results?.items?.length || 0);

                // Update detailed stats
                document.getElementById('page-load-time').textContent = `${pageLoadTime.toFixed(0)}ms`;
                document.getElementById('dom-ready-time').textContent = `${(document.readyState === 'complete' ? 'Complete' : 'Loading')}`;
                document.getElementById('search-type-value').textContent = this.currentType.toUpperCase();
                document.getElementById('data-source-value').textContent = isRealAPI ? 'Google API' : 'Demo Data';
                document.getElementById('cache-status-value').textContent = 'No Cache';

                // Memory usage (if available)
                if (performance.memory) {
                    const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                    document.getElementById('memory-usage-value').textContent = `${memoryMB} MB`;
                } else {
                    document.getElementById('memory-usage-value').textContent = 'N/A';
                }

                // Show the performance stats bar
                perfStats.style.display = 'flex';
            }

            updateStatValue(elementId, value, performanceClass = '') {
                const element = document.getElementById(elementId);
                if (!element) return;

                element.textContent = value;

                // Remove existing performance classes
                element.parentElement.classList.remove('excellent', 'good', 'average', 'slow');

                // Add new performance class if provided
                if (performanceClass) {
                    element.parentElement.classList.add(performanceClass);
                }
            }

            getPerformanceClass(timeMs, type) {
                const thresholds = {
                    api: { excellent: 200, good: 500, average: 1000 },
                    render: { excellent: 50, good: 100, average: 200 },
                    total: { excellent: 300, good: 700, average: 1500 }
                };

                const threshold = thresholds[type] || thresholds.total;

                if (timeMs <= threshold.excellent) return 'excellent';
                if (timeMs <= threshold.good) return 'good';
                if (timeMs <= threshold.average) return 'average';
                return 'slow';
            }

            showApiWarning() {
                console.log('⚠️ Showing API warning');
                // This method is called but we don't need to show a warning since we have fallback demo results
            }

            showHelpModal() {
                const helpModal = document.getElementById('help-modal');
                if (helpModal) {
                    helpModal.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // Prevent background scrolling
                }
            }

            hideHelpModal() {
                const helpModal = document.getElementById('help-modal');
                if (helpModal) {
                    helpModal.style.display = 'none';
                    document.body.style.overflow = 'auto'; // Restore scrolling
                }
            }

            toggleFilters() {
                const filtersPanel = document.getElementById('search-filters');
                const filtersToggle = document.getElementById('filters-toggle');

                if (filtersPanel && filtersToggle) {
                    const isActive = filtersPanel.classList.contains('active');

                    if (isActive) {
                        filtersPanel.classList.remove('active');
                        filtersToggle.style.background = '';
                    } else {
                        filtersPanel.classList.add('active');
                        filtersToggle.style.background = 'rgba(60,64,67,.08)';
                    }
                }
            }

            showAdvancedSearch() {
                const modal = document.getElementById('advanced-search-modal');
                if (modal) {
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden';

                    // Pre-fill with current search query
                    const allWordsInput = document.getElementById('all-words');
                    if (allWordsInput && this.currentQuery) {
                        allWordsInput.value = this.currentQuery;
                    }
                }
            }

            hideAdvancedSearch() {
                const modal = document.getElementById('advanced-search-modal');
                if (modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            }

            performAdvancedSearch() {
                const allWords = document.getElementById('all-words').value.trim();
                const exactPhrase = document.getElementById('exact-phrase').value.trim();
                const anyWords = document.getElementById('any-words').value.trim();
                const noneWords = document.getElementById('none-words').value.trim();
                const siteSearch = document.getElementById('site-search').value.trim();
                const fileType = document.getElementById('file-type').value;

                // Build advanced search query
                let query = '';

                if (allWords) {
                    query += allWords + ' ';
                }

                if (exactPhrase) {
                    query += `"${exactPhrase}" `;
                }

                if (anyWords) {
                    const words = anyWords.split(/\s+/);
                    query += `(${words.join(' OR ')}) `;
                }

                if (noneWords) {
                    const words = noneWords.split(/\s+/);
                    words.forEach(word => {
                        query += `-${word} `;
                    });
                }

                if (siteSearch) {
                    query += `site:${siteSearch} `;
                }

                if (fileType) {
                    query += `filetype:${fileType} `;
                }

                query = query.trim();

                if (query) {
                    // Track advanced search usage
                    this.analytics.trackAdvancedSearch({
                        allWords,
                        exactPhrase,
                        anyWords,
                        noneWords,
                        siteSearch,
                        fileType
                    });

                    this.currentQuery = query;
                    this.currentPage = 1;

                    // Update search input
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.value = query;
                    }

                    this.hideAdvancedSearch();
                    this.updateURL();
                    this.displayResults();
                }
            }

            applyFilters() {
                // Get filter values
                const timeFilter = document.getElementById('time-filter').value;
                const languageFilter = document.getElementById('language-filter').value;
                const regionFilter = document.getElementById('region-filter').value;

                // Track filter usage
                if (timeFilter) this.analytics.trackFilterUsage('time', timeFilter);
                if (languageFilter) this.analytics.trackFilterUsage('language', languageFilter);
                if (regionFilter) this.analytics.trackFilterUsage('region', regionFilter);

                // Store filters for use in search
                this.currentFilters = {
                    time: timeFilter,
                    language: languageFilter,
                    region: regionFilter
                };

                // Re-run search with filters
                if (this.currentQuery) {
                    this.displayResults();
                }
            }

            showAnalyticsDashboard() {
                const modal = document.getElementById('analytics-modal');
                if (modal) {
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                    this.updateAnalyticsDashboard();
                }
            }

            hideAnalyticsDashboard() {
                const modal = document.getElementById('analytics-modal');
                if (modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            }

            updateAnalyticsDashboard() {
                // Update session summary
                const sessionSummary = this.analytics.getSessionSummary();

                document.getElementById('session-duration').textContent =
                    this.formatDuration(sessionSummary.duration);
                document.getElementById('total-searches').textContent =
                    sessionSummary.totalSearches;
                document.getElementById('total-clicks').textContent =
                    sessionSummary.totalClicks;
                document.getElementById('click-through-rate').textContent =
                    (sessionSummary.clickThroughRate * 100).toFixed(1) + '%';

                // Update performance metrics
                const performanceMetrics = this.performance.getMetricsSummary();
                const performanceContainer = document.getElementById('performance-metrics');

                let performanceHTML = '';
                for (const [name, metric] of Object.entries(performanceMetrics)) {
                    performanceHTML += `${name}: ${metric.value.toFixed(2)}ms\n`;
                }
                performanceContainer.textContent = performanceHTML || 'No performance data available';

                // Update recent activity
                const recentEvents = this.analytics.events.slice(-10).reverse();
                const activityContainer = document.getElementById('recent-activity');

                let activityHTML = '';
                recentEvents.forEach(event => {
                    const time = new Date(event.timestamp).toLocaleTimeString();
                    activityHTML += `[${time}] ${event.event}\n`;
                });
                activityContainer.textContent = activityHTML || 'No recent activity';
            }

            formatDuration(ms) {
                const seconds = Math.floor(ms / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);

                if (hours > 0) {
                    return `${hours}h ${minutes % 60}m`;
                } else if (minutes > 0) {
                    return `${minutes}m ${seconds % 60}s`;
                } else {
                    return `${seconds}s`;
                }
            }

            exportAnalyticsData() {
                const data = {
                    sessionSummary: this.analytics.getSessionSummary(),
                    performanceMetrics: this.performance.getMetricsSummary(),
                    events: this.analytics.events,
                    exportTime: new Date().toISOString()
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `search-analytics-${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.analytics.track('analytics_exported');
            }

            clearAnalyticsData() {
                if (confirm('Are you sure you want to clear all analytics data? This cannot be undone.')) {
                    this.analytics.events = [];
                    this.performance.metrics.clear();
                    this.updateAnalyticsDashboard();
                    this.analytics.track('analytics_cleared');
                }
            }

            loadUserPreferences() {
                const defaultPreferences = {
                    resultsPerPage: 10,
                    defaultSearchType: 'all',
                    enableAnalytics: true,
                    enablePerformanceMonitoring: true,
                    autoSuggestions: true,
                    animationsEnabled: true,
                    compactView: false,
                    darkMode: false,
                    language: 'en',
                    region: 'us'
                };

                try {
                    const saved = localStorage.getItem('google_clone_preferences');
                    return saved ? { ...defaultPreferences, ...JSON.parse(saved) } : defaultPreferences;
                } catch (error) {
                    console.warn('Failed to load user preferences:', error);
                    return defaultPreferences;
                }
            }

            saveUserPreferences() {
                try {
                    localStorage.setItem('google_clone_preferences', JSON.stringify(this.preferences));
                    this.analytics.track('preferences_saved');
                } catch (error) {
                    console.warn('Failed to save user preferences:', error);
                }
            }

            applyUserPreferences() {
                // Apply analytics preference
                this.analytics.isEnabled = this.preferences.enableAnalytics;

                // Apply default search type
                if (this.preferences.defaultSearchType !== 'all') {
                    this.currentType = this.preferences.defaultSearchType;
                    this.updateActiveTab();
                }

                // Apply compact view
                if (this.preferences.compactView) {
                    document.body.classList.add('compact-view');
                }

                // Apply dark mode
                if (this.preferences.darkMode) {
                    document.body.classList.add('dark-mode');
                }

                // Apply animations preference
                if (!this.preferences.animationsEnabled) {
                    document.body.classList.add('no-animations');
                }
            }
        }

        // Performance details toggle function
        function togglePerformanceDetails() {
            const details = document.getElementById('performance-details');
            const toggle = document.getElementById('performance-toggle');

            if (details && toggle) {
                const isExpanded = details.classList.contains('expanded');

                if (isExpanded) {
                    details.classList.remove('expanded');
                    toggle.textContent = 'Details';
                } else {
                    details.classList.add('expanded');
                    toggle.textContent = 'Hide';
                }
            }
        }

        // Initialize the app
        const searchApp = new SearchApp();

        // Make app globally available
        window.searchApp = searchApp;

        // Initialize API status checking
        async function initializeApiStatus() {
            const statusBar = document.getElementById('api-status-bar');
            const statusIcon = document.getElementById('api-status-icon');
            const statusText = document.getElementById('api-status-text');

            try {
                // Check if Utils is loaded and has the enhanced API checking
                if (window.Utils && typeof window.Utils.verifyApiConfiguration === 'function' && window.Utils.CONFIG) {
                    console.log('🔍 Checking API configuration...');
                    const apiStatus = await window.Utils.verifyApiConfiguration();

                    if (apiStatus.configured && apiStatus.working) {
                        // API is working - hide status bar
                        statusBar.style.display = 'none';
                        console.log('✅ API is working:', apiStatus.message);
                    } else {
                        // API not working - show warning
                        statusBar.style.display = 'block';
                        statusIcon.textContent = '⚠️';
                        statusText.textContent = apiStatus.message || 'API not available, showing demo results';
                        console.log('⚠️ API issue:', apiStatus.message);
                    }
                } else {
                    // Try a direct API test if Utils is not fully loaded
                    console.log('🔍 Utils.js not fully loaded, testing API directly...');
                    try {
                        const testResult = await testApiDirectly();
                        if (testResult.success) {
                            statusBar.style.display = 'none';
                            console.log('✅ Direct API test successful');
                        } else {
                            statusBar.style.display = 'block';
                            statusIcon.textContent = '⚠️';
                            statusText.textContent = 'API not available - using demo results';
                            console.log('⚠️ Direct API test failed:', testResult.message);
                        }
                    } catch (directTestError) {
                        statusBar.style.display = 'block';
                        statusIcon.textContent = '⚠️';
                        statusText.textContent = 'API configuration not loaded - using demo results';
                        console.log('⚠️ Utils.js not loaded and direct test failed');
                    }
                }
            } catch (error) {
                console.error('❌ Error checking API status:', error);
                statusBar.style.display = 'block';
                statusIcon.textContent = '❌';
                statusText.textContent = 'Error checking API status - using demo results';
            }
        }

        async function testApiDirectly() {
            try {
                const API_KEY = 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ';
                const SEARCH_ENGINE_ID = '61201925358ea4e83';

                const params = new URLSearchParams({
                    key: API_KEY,
                    cx: SEARCH_ENGINE_ID,
                    q: 'test',
                    num: 1
                });

                const response = await fetch(`https://www.googleapis.com/customsearch/v1?${params.toString()}`);

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Direct API test successful:', data);
                    return {
                        success: true,
                        message: `API working - ${data.searchInformation?.totalResults || 0} results available`
                    };
                } else {
                    const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
                    console.error('❌ Direct API test failed:', response.status, errorData);
                    return {
                        success: false,
                        message: `API Error ${response.status}: ${errorData.error?.message || 'Unknown error'}`
                    };
                }
            } catch (error) {
                console.error('❌ Direct API test exception:', error);
                return {
                    success: false,
                    message: `Network error: ${error.message}`
                };
            }
        }

        // Initialize API status when page loads
        document.addEventListener('DOMContentLoaded', initializeApiStatus);

        // Also check when Utils.js loads (if it loads after this script)
        if (document.readyState === 'loading') {
            window.addEventListener('load', () => {
                setTimeout(initializeApiStatus, 2000); // Give Utils.js more time to load
            });
        } else {
            setTimeout(initializeApiStatus, 1000); // Give Utils.js time to load even if DOM is ready
        }
    </script>

    <!-- Load external JavaScript files -->
    <script src="js/utils.js"></script>
    <script src="js/autocomplete.js"></script>

    <!-- PWA Installation and Service Worker -->
    <script>
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    const registration = await navigator.serviceWorker.register('/service-worker.js');
                    console.log('✅ Service Worker registered:', registration.scope);

                    // Check for updates
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                showUpdateNotification();
                            }
                        });
                    });
                } catch (error) {
                    console.log('❌ Service Worker registration failed:', error);
                }
            });
        }

        // PWA Installation Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallPrompt();
        });

        function showInstallPrompt() {
            // Create install button
            const installBtn = document.createElement('button');
            installBtn.innerHTML = '📱 Install App';
            installBtn.className = 'install-prompt';
            installBtn.style.cssText = `
                position: fixed;
                top: 20px;
                right: 80px;
                background: #4285f4;
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                cursor: pointer;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
            `;

            installBtn.addEventListener('click', async () => {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    const { outcome } = await deferredPrompt.userChoice;

                    if (window.searchApp?.analytics) {
                        window.searchApp.analytics.track('pwa_install_prompt', { outcome });
                    }

                    deferredPrompt = null;
                    installBtn.remove();
                }
            });

            // Auto-hide after 10 seconds
            setTimeout(() => {
                if (installBtn.parentNode) {
                    installBtn.style.animation = 'slideOut 0.3s ease-out';
                    setTimeout(() => installBtn.remove(), 300);
                }
            }, 10000);

            document.body.appendChild(installBtn);
        }

        function showUpdateNotification() {
            const updateBtn = document.createElement('button');
            updateBtn.innerHTML = '🔄 Update Available';
            updateBtn.className = 'update-prompt';
            updateBtn.style.cssText = `
                position: fixed;
                bottom: 80px;
                left: 20px;
                background: #34a853;
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                cursor: pointer;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
            `;

            updateBtn.addEventListener('click', () => {
                if (navigator.serviceWorker.controller) {
                    navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' });
                    window.location.reload();
                }
            });

            document.body.appendChild(updateBtn);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Performance monitoring for PWA
        if (window.searchApp?.performance) {
            // Monitor PWA-specific metrics
            window.addEventListener('appinstalled', () => {
                window.searchApp.analytics.track('pwa_installed');
                window.searchApp.performance.recordMetric('pwa_install_time', performance.now());
            });

            // Monitor offline/online status
            window.addEventListener('online', () => {
                window.searchApp.analytics.track('connection_restored');
            });

            window.addEventListener('offline', () => {
                window.searchApp.analytics.track('connection_lost');
            });
        }
    </script>
</body>
</html>
