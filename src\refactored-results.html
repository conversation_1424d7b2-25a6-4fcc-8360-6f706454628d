<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Google Clone - Search Results">
    <meta name="robots" content="noindex, nofollow">
    
    <title>Search Results - Google Clone</title>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://www.googleapis.com">
    <link rel="dns-prefetch" href="https://www.googleapis.com">
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="styles/base/variables.css">
    <link rel="stylesheet" href="styles/base/reset.css">
    <link rel="stylesheet" href="styles/components/search-box.css">
    <link rel="stylesheet" href="styles/components/results.css">
    <link rel="stylesheet" href="styles/components/navigation.css">
    <link rel="stylesheet" href="styles/layouts/results.css">
    
    <!-- Preload critical resources -->
    <link rel="modulepreload" href="src/main.js">
    <link rel="modulepreload" href="src/core/app.js">
    
    <!-- Performance optimizations -->
    <meta name="theme-color" content="#4285f4">
    <meta name="color-scheme" content="light">
    
    <style>
        /* Critical inline styles for results page */
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid var(--border-primary);
            background: var(--bg-primary);
            position: sticky;
            top: 0;
            z-index: var(--z-sticky);
        }
        
        .header-content {
            max-width: none;
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }
        
        .logo {
            text-decoration: none;
            margin-right: 10px;
        }
        
        .logo svg {
            width: 92px;
            height: 30px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-left: auto;
        }
        
        .apps-menu {
            width: 24px;
            height: 24px;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.1s ease;
            border: none;
            background: none;
        }
        
        .apps-menu:hover {
            background-color: var(--bg-hover);
        }
        
        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--google-blue), var(--google-green), var(--google-yellow), var(--google-red));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }
        
        .nav-tabs {
            background: var(--bg-primary);
            padding: 0 20px;
        }
        
        .main-content {
            max-width: none;
            padding: 20px;
            margin-left: var(--results-margin-left);
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .header {
                padding: 6px 15px 0 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header -->
    <header class="header" role="banner">
        <div class="header-content">
            <a href="refactored-index.html" class="logo">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Google">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            
            <div class="search-container" id="header-search-container" role="search">
                <!-- Search box will be initialized by JavaScript -->
            </div>
            
            <div class="header-right">
                <button class="apps-menu" id="apps-menu" title="Google apps" aria-label="Google apps">
                    <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                        <path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
                    </svg>
                </button>
                <div class="profile-pic" title="Google Account" aria-label="Google Account">G</div>
            </div>
        </div>
    </header>
    
    <!-- Navigation Tabs -->
    <nav class="nav-tabs" id="navigation-tabs" role="navigation" aria-label="Search types">
        <!-- Navigation will be initialized by JavaScript -->
    </nav>
    
    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <!-- Results Component Container -->
        <div class="results-component" id="results-component">
            <!-- Results will be initialized by JavaScript -->
        </div>
    </main>
    
    <!-- Error Message Container -->
    <div id="error-message" class="error-message" style="display: none;" role="alert" aria-live="assertive"></div>
    
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;" aria-hidden="true">
        <div class="spinner"></div>
        <div class="loading-text">Loading...</div>
    </div>
    
    <!-- Accessibility Live Region -->
    <div id="search-live-region" aria-live="polite" aria-atomic="true" style="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;"></div>
    
    <!-- JavaScript -->
    <script type="module">
        // Enhanced initialization for results page
        import app from './src/main.js';
        import { APP_CONFIG } from './src/core/config/app.config.js';
        import SearchBoxComponent from './src/core/components/search-box.component.js';
        import NavigationComponent from './src/core/components/navigation.component.js';
        import ResultsComponent from './src/core/components/results.component.js';
        
        // Wait for app to be ready
        document.addEventListener('app:ready', async () => {
            try {
                // Initialize header search box
                const headerSearchContainer = document.getElementById('header-search-container');
                if (headerSearchContainer) {
                    const headerSearchBox = new SearchBoxComponent(headerSearchContainer, {
                        enableAutocomplete: true,
                        showVoiceSearch: true,
                        showImageSearch: true
                    });
                    await headerSearchBox.init();
                    app.addComponent('headerSearchBox', headerSearchBox);
                }
                
                // Initialize navigation
                const navigationTabs = document.getElementById('navigation-tabs');
                if (navigationTabs) {
                    const navigation = new NavigationComponent(navigationTabs, {
                        activeType: new URLSearchParams(window.location.search).get('type') || 'web'
                    });
                    await navigation.init();
                    app.addComponent('navigation', navigation);
                }
                
                // Initialize results
                const resultsComponent = document.getElementById('results-component');
                if (resultsComponent) {
                    const results = new ResultsComponent(resultsComponent, {
                        enablePagination: true,
                        animateResults: true
                    });
                    await results.init();
                    app.addComponent('results', results);
                }
                
                // Set up component interactions
                setupComponentInteractions();
                
                // Load initial results if query exists
                const urlParams = new URLSearchParams(window.location.search);
                const query = urlParams.get('q');
                if (query) {
                    const searchOptions = {
                        start: parseInt(urlParams.get('start')) || 1,
                        type: urlParams.get('type') || 'web'
                    };
                    
                    // Update header search box
                    const headerSearchBox = app.getComponent('headerSearchBox');
                    if (headerSearchBox) {
                        headerSearchBox.setValue(query);
                    }
                    
                    // Perform search
                    const results = app.getComponent('results');
                    if (results) {
                        await results.search(query, searchOptions);
                    }
                }
                
            } catch (error) {
                console.error('Failed to initialize results page components:', error);
            }
        });
        
        function setupComponentInteractions() {
            const headerSearchBox = app.getComponent('headerSearchBox');
            const navigation = app.getComponent('navigation');
            const results = app.getComponent('results');
            
            // Header search box events
            if (headerSearchBox) {
                headerSearchBox.addEventListener('search', async (event) => {
                    const { query } = event.detail;
                    const activeType = navigation ? navigation.getActiveTab() : 'web';
                    
                    if (results) {
                        await results.search(query, { type: activeType, start: 1 });
                        updateURL(query, { type: activeType, start: 1 });
                    }
                });
            }
            
            // Navigation tab events
            if (navigation) {
                navigation.addEventListener('tabChange', async (event) => {
                    const { type } = event.detail;
                    const query = results ? results.getCurrentQuery() : '';
                    
                    if (query && results) {
                        await results.search(query, { type, start: 1 });
                        updateURL(query, { type, start: 1 });
                    }
                });
            }
            
            // Results pagination events
            if (results) {
                results.addEventListener('pageChange', async (event) => {
                    const { query, type, start } = event.detail;
                    
                    await results.search(query, { type, start });
                    updateURL(query, { type, start });
                    
                    // Scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
                
                results.addEventListener('resultClick', (event) => {
                    const { result, index, query } = event.detail;
                    
                    // Track result click for analytics
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'search_result_click', {
                            search_term: query,
                            result_position: index + 1,
                            result_url: result.link
                        });
                    }
                });
            }
        }
        
        function updateURL(query, options) {
            const url = new URL(window.location);
            url.searchParams.set('q', query);
            url.searchParams.set('start', options.start.toString());
            url.searchParams.set('type', options.type);
            
            window.history.pushState({}, '', url.toString());
            
            // Update page title
            document.title = `${query} - Google Clone`;
        }
        
        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            window.location.reload();
        });
    </script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    
    <!-- Analytics (placeholder) -->
    <script>
        // Google Analytics or other analytics code would go here
        // Currently disabled in development
        if (typeof gtag !== 'undefined') {
            gtag('config', 'GA_MEASUREMENT_ID', {
                page_title: document.title,
                page_location: window.location.href
            });
        }
    </script>
</body>
</html>
