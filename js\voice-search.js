// Voice Search functionality using Web Speech API

class VoiceSearch {
    constructor() {
        this.recognition = null;
        this.isListening = false;
        this.isSupported = false;
        this.init();
    }
    
    init() {
        // Check if Speech Recognition is supported
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            this.isSupported = true;
            this.setupSpeechRecognition();
            this.addVoiceButton();
        } else {
            console.warn('Speech Recognition not supported in this browser');
        }
    }
    
    setupSpeechRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        // Configuration
        this.recognition.continuous = false;
        this.recognition.interimResults = true;
        this.recognition.lang = 'en-US';
        this.recognition.maxAlternatives = 1;
        
        // Event handlers
        this.recognition.onstart = () => {
            this.isListening = true;
            this.updateVoiceButton(true);
            this.showListeningIndicator();
        };
        
        this.recognition.onresult = (event) => {
            let finalTranscript = '';
            let interimTranscript = '';
            
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }
            
            // Update search input with transcript
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                if (finalTranscript) {
                    searchInput.value = finalTranscript.trim();
                    this.performVoiceSearch(finalTranscript.trim());
                } else if (interimTranscript) {
                    searchInput.value = interimTranscript.trim();
                }
            }
        };
        
        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.handleVoiceError(event.error);
            this.stopListening();
        };
        
        this.recognition.onend = () => {
            this.stopListening();
        };
    }
    
    addVoiceButton() {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer) return;
        
        const voiceButton = document.createElement('button');
        voiceButton.id = 'voice-search-btn';
        voiceButton.className = 'voice-btn';
        voiceButton.setAttribute('aria-label', 'Search by voice');
        voiceButton.setAttribute('title', 'Search by voice');
        
        voiceButton.innerHTML = `
            <svg class="voice-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
        `;
        
        voiceButton.addEventListener('click', () => {
            if (this.isListening) {
                this.stopListening();
            } else {
                this.startListening();
            }
        });
        
        // Insert before search icon
        const searchIcon = document.getElementById('search-btn');
        if (searchIcon) {
            searchContainer.insertBefore(voiceButton, searchIcon);
        } else {
            searchContainer.appendChild(voiceButton);
        }
        
        // Add CSS styles
        this.addVoiceStyles();
    }
    
    addVoiceStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .voice-btn {
                position: absolute;
                right: 45px;
                top: 50%;
                transform: translateY(-50%);
                background: none;
                border: none;
                cursor: pointer;
                padding: 8px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;
                z-index: 10;
            }
            
            .voice-btn:hover {
                background-color: #f1f3f4;
            }
            
            .voice-btn.listening {
                background-color: #ea4335;
                animation: pulse 1.5s infinite;
            }
            
            .voice-btn.listening .voice-icon {
                color: white;
            }
            
            .voice-icon {
                width: 16px;
                height: 16px;
                color: #5f6368;
            }
            
            @keyframes pulse {
                0% { transform: translateY(-50%) scale(1); }
                50% { transform: translateY(-50%) scale(1.1); }
                100% { transform: translateY(-50%) scale(1); }
            }
            
            .listening-indicator {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px 30px;
                border-radius: 8px;
                z-index: 1000;
                text-align: center;
                font-size: 16px;
            }
            
            .listening-indicator .mic-animation {
                width: 40px;
                height: 40px;
                margin: 0 auto 10px;
                background: #ea4335;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: pulse 1s infinite;
            }
            
            .listening-indicator .mic-animation svg {
                width: 20px;
                height: 20px;
                color: white;
            }
            
            .voice-error {
                background: #fce8e6;
                color: #d93025;
                border: 1px solid #f28b82;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
                font-size: 14px;
                text-align: center;
            }
        `;
        document.head.appendChild(style);
    }
    
    startListening() {
        if (!this.isSupported || !this.recognition) {
            this.showUnsupportedMessage();
            return;
        }
        
        try {
            this.recognition.start();
        } catch (error) {
            console.error('Failed to start voice recognition:', error);
            this.handleVoiceError('start_failed');
        }
    }
    
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
        this.isListening = false;
        this.updateVoiceButton(false);
        this.hideListeningIndicator();
    }
    
    updateVoiceButton(listening) {
        const voiceBtn = document.getElementById('voice-search-btn');
        if (voiceBtn) {
            if (listening) {
                voiceBtn.classList.add('listening');
                voiceBtn.setAttribute('title', 'Stop listening');
            } else {
                voiceBtn.classList.remove('listening');
                voiceBtn.setAttribute('title', 'Search by voice');
            }
        }
    }
    
    showListeningIndicator() {
        // Remove existing indicator
        this.hideListeningIndicator();
        
        const indicator = document.createElement('div');
        indicator.id = 'listening-indicator';
        indicator.className = 'listening-indicator';
        indicator.innerHTML = `
            <div class="mic-animation">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                    <line x1="12" y1="19" x2="12" y2="23"></line>
                    <line x1="8" y1="23" x2="16" y2="23"></line>
                </svg>
            </div>
            <div>Listening...</div>
            <div style="font-size: 12px; margin-top: 5px;">Speak now</div>
        `;
        
        document.body.appendChild(indicator);
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (this.isListening) {
                this.stopListening();
            }
        }, 10000);
    }
    
    hideListeningIndicator() {
        const indicator = document.getElementById('listening-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    performVoiceSearch(query) {
        if (!query) return;
        
        // Use existing search functionality
        if (window.searchEngine) {
            window.searchEngine.performSearch(query);
        } else if (window.resultsPage) {
            window.resultsPage.updateSearch(query, 1, window.resultsPage.currentType);
        }
        
        // Hide listening indicator
        this.hideListeningIndicator();
    }
    
    handleVoiceError(error) {
        let message = 'Voice search failed. Please try again.';
        
        switch (error) {
            case 'no-speech':
                message = 'No speech detected. Please try again.';
                break;
            case 'audio-capture':
                message = 'Microphone not available. Please check permissions.';
                break;
            case 'not-allowed':
                message = 'Microphone access denied. Please allow microphone access.';
                break;
            case 'network':
                message = 'Network error. Please check your connection.';
                break;
            case 'start_failed':
                message = 'Could not start voice recognition. Please try again.';
                break;
        }
        
        this.showErrorMessage(message);
    }
    
    showErrorMessage(message) {
        // Remove existing error
        const existingError = document.querySelector('.voice-error');
        if (existingError) {
            existingError.remove();
        }
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'voice-error';
        errorDiv.textContent = message;
        
        const searchContainer = document.querySelector('.search-container');
        if (searchContainer) {
            searchContainer.appendChild(errorDiv);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }
    }
    
    showUnsupportedMessage() {
        this.showErrorMessage('Voice search is not supported in this browser. Try Chrome, Edge, or Safari.');
    }
    
    // Public methods
    isVoiceSupported() {
        return this.isSupported;
    }
    
    getCurrentLanguage() {
        return this.recognition ? this.recognition.lang : 'en-US';
    }
    
    setLanguage(lang) {
        if (this.recognition) {
            this.recognition.lang = lang;
        }
    }
}

// Initialize voice search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.voiceSearch = new VoiceSearch();
});

// Export for use in other modules
window.VoiceSearch = VoiceSearch;
