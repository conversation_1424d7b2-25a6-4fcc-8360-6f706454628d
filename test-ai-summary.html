<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Summary Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        /* AI Summary Section */
        .ai-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: white;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .ai-summary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .ai-summary-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            position: relative;
            z-index: 1;
        }

        .ai-summary-icon {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .ai-summary-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .ai-summary-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ai-summary-content {
            position: relative;
            z-index: 1;
            line-height: 1.6;
            font-size: 15px;
        }

        .ai-summary-loading {
            display: flex;
            align-items: center;
            gap: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        .ai-summary-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .ai-summary-error {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ai-summary-retry {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-left: auto;
            transition: background 0.2s ease;
        }

        .ai-summary-retry:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .ai-summary-sources {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
        }

        .ai-summary-sources a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            margin-right: 12px;
        }

        .ai-summary-sources a:hover {
            text-decoration: underline;
        }

        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }

        .test-button:hover {
            background: #3367d6;
        }

        .test-results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI Summary Test</h1>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button class="test-button" onclick="showLoadingState()">Show Loading</button>
            <button class="test-button" onclick="showSuccessState()">Show Success</button>
            <button class="test-button" onclick="showErrorState()">Show Error</button>
            <button class="test-button" onclick="hideAISummary()">Hide Summary</button>
        </div>

        <!-- AI Summary Section -->
        <div class="ai-summary" id="ai-summary">
            <div class="ai-summary-header">
                <div class="ai-summary-icon">🤖</div>
                <h3 class="ai-summary-title">AI Summary</h3>
                <span class="ai-summary-badge">Powered by Gemini</span>
            </div>
            <div class="ai-summary-content" id="ai-summary-content">
                <div class="ai-summary-loading">
                    <div class="ai-summary-spinner"></div>
                    <span>Generating AI summary with Gemini...</span>
                </div>
            </div>
            <div class="ai-summary-sources" id="ai-summary-sources" style="display: none;">
                <strong>Sources:</strong>
            </div>
        </div>

        <div class="test-results">
            <h3>Test AI Summary States</h3>
            <p>Use the buttons above to test different states of the AI summary component:</p>
            <ul>
                <li><strong>Loading:</strong> Shows spinner and loading message</li>
                <li><strong>Success:</strong> Shows generated summary with sources</li>
                <li><strong>Error:</strong> Shows error message with retry button</li>
                <li><strong>Hide:</strong> Hides the entire summary section</li>
            </ul>
        </div>
    </div>

    <script>
        function showLoadingState() {
            const aiSummary = document.getElementById('ai-summary');
            const aiSummaryContent = document.getElementById('ai-summary-content');
            const aiSummarySources = document.getElementById('ai-summary-sources');

            aiSummary.style.display = 'block';
            aiSummaryContent.innerHTML = `
                <div class="ai-summary-loading">
                    <div class="ai-summary-spinner"></div>
                    <span>Generating AI summary with Gemini...</span>
                </div>
            `;
            aiSummarySources.style.display = 'none';
        }

        function showSuccessState() {
            const aiSummary = document.getElementById('ai-summary');
            const aiSummaryContent = document.getElementById('ai-summary-content');
            const aiSummarySources = document.getElementById('ai-summary-sources');

            aiSummary.style.display = 'block';
            aiSummaryContent.innerHTML = `
                JavaScript is a versatile, high-level programming language primarily used for web development. It enables interactive web pages and is an essential part of web applications, alongside HTML and CSS. JavaScript can run on both client-side (in web browsers) and server-side (with Node.js).

                The language supports multiple programming paradigms including procedural, object-oriented, and functional programming. Modern JavaScript includes features like arrow functions, async/await, destructuring, and modules. It's widely used for creating dynamic user interfaces, handling user interactions, and building full-stack applications.

                JavaScript has a vast ecosystem with numerous frameworks and libraries like React, Vue.js, Angular for frontend development, and Express.js for backend development. The language continues to evolve with regular updates and new features being added through ECMAScript specifications.
            `;
            
            const sourcesHtml = `
                <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript" target="_blank">MDN Web Docs - JavaScript</a> • 
                <a href="https://javascript.info/" target="_blank">The Modern JavaScript Tutorial</a> • 
                <a href="https://www.w3schools.com/js/" target="_blank">W3Schools JavaScript Tutorial</a>
            `;
            aiSummarySources.innerHTML = `<strong>Sources:</strong> ${sourcesHtml}`;
            aiSummarySources.style.display = 'block';
        }

        function showErrorState() {
            const aiSummary = document.getElementById('ai-summary');
            const aiSummaryContent = document.getElementById('ai-summary-content');
            const aiSummarySources = document.getElementById('ai-summary-sources');

            aiSummary.style.display = 'block';
            aiSummaryContent.innerHTML = `
                <div class="ai-summary-error">
                    <span>⚠️ Unable to generate AI summary: API quota exceeded</span>
                    <button class="ai-summary-retry" onclick="showLoadingState()">Retry</button>
                </div>
            `;
            aiSummarySources.style.display = 'none';
        }

        function hideAISummary() {
            const aiSummary = document.getElementById('ai-summary');
            aiSummary.style.display = 'none';
        }

        // Auto-show loading state on page load
        window.addEventListener('load', () => {
            setTimeout(showLoadingState, 500);
        });
    </script>
</body>
</html>
