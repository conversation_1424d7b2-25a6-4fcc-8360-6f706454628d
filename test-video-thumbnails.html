<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Thumbnails Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        /* Video Results Styles */
        .video-results-container {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)) !important;
            gap: 20px !important;
            padding: 20px 0 !important;
            width: 100% !important;
        }

        .video-result-item {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            animation: fadeInUp 0.3s ease-out;
        }

        .video-result-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
        }

        .video-thumbnail-wrapper {
            position: relative;
            width: 100%;
            height: 180px;
            overflow: hidden;
            background: #000;
        }

        .video-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-result-item:hover .video-thumbnail {
            transform: scale(1.05);
        }

        .video-play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0,0,0,0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-result-item:hover .video-play-button {
            background: rgba(255,0,0,0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .video-info {
            padding: 16px;
        }

        .video-title {
            font-size: 16px;
            font-weight: 500;
            color: #1a0dab;
            margin-bottom: 8px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-decoration: none;
        }

        .video-title:hover {
            text-decoration: underline;
        }

        .video-channel {
            font-size: 14px;
            color: #5f6368;
            margin-bottom: 4px;
        }

        .video-meta {
            font-size: 13px;
            color: #5f6368;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-views {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .video-date {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }

        .test-button:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Video Thumbnails Test</h1>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button class="test-button" onclick="loadTestVideos()">Load Test Videos</button>
            <button class="test-button" onclick="clearVideos()">Clear Videos</button>
        </div>

        <div id="video-container" class="video-results-container">
            <!-- Videos will be loaded here -->
        </div>
    </div>

    <script>
        function loadTestVideos() {
            const container = document.getElementById('video-container');
            container.innerHTML = '';
            
            // Test video data
            const testVideos = [
                {
                    title: 'JavaScript Tutorial - Complete Course',
                    videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    thumbnailUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
                    domain: 'youtube.com',
                    duration: '12:34',
                    views: '1.2M views',
                    uploadDate: '2 weeks ago'
                },
                {
                    title: 'React Explained in 5 Minutes',
                    videoUrl: 'https://www.youtube.com/watch?v=oHg5SJYRHA0',
                    thumbnailUrl: 'https://img.youtube.com/vi/oHg5SJYRHA0/hqdefault.jpg',
                    domain: 'youtube.com',
                    duration: '5:23',
                    views: '856K views',
                    uploadDate: '1 month ago'
                },
                {
                    title: 'Advanced Web Development',
                    videoUrl: 'https://vimeo.com/123456789',
                    thumbnailUrl: 'https://via.placeholder.com/320x180/4285f4/ffffff?text=Advanced+Video',
                    domain: 'vimeo.com',
                    duration: '28:45',
                    views: '234K views',
                    uploadDate: '3 months ago'
                },
                {
                    title: 'Programming Documentary',
                    videoUrl: 'https://www.youtube.com/watch?v=9bZkp7q19f0',
                    thumbnailUrl: 'https://img.youtube.com/vi/9bZkp7q19f0/hqdefault.jpg',
                    domain: 'youtube.com',
                    duration: '1:23:15',
                    views: '2.1M views',
                    uploadDate: '6 months ago'
                },
                {
                    title: 'Tech Innovation - TED Talk',
                    videoUrl: 'https://www.ted.com/talks/example',
                    thumbnailUrl: 'https://via.placeholder.com/320x180/ea4335/ffffff?text=TED+Talk',
                    domain: 'ted.com',
                    duration: '18:42',
                    views: '3.4M views',
                    uploadDate: '1 year ago'
                },
                {
                    title: 'Coding Best Practices',
                    videoUrl: 'https://www.youtube.com/watch?v=abc123def456',
                    thumbnailUrl: 'https://img.youtube.com/vi/abc123def456/hqdefault.jpg',
                    domain: 'youtube.com',
                    duration: '15:30',
                    views: '678K views',
                    uploadDate: '2 months ago'
                }
            ];

            testVideos.forEach((video, index) => {
                const videoDiv = document.createElement('div');
                videoDiv.className = 'video-result-item';
                videoDiv.style.animationDelay = `${index * 0.1}s`;

                videoDiv.innerHTML = `
                    <div class="video-thumbnail-wrapper">
                        <a href="${video.videoUrl}" target="_blank" rel="noopener">
                            <img src="${video.thumbnailUrl}"
                                 alt="${video.title}"
                                 class="video-thumbnail"
                                 loading="lazy"
                                 onerror="this.src='https://via.placeholder.com/320x180/000000/ffffff?text=Video'; this.onerror=null;">
                            <div class="video-play-button">
                                <span>▶</span>
                            </div>
                            <div class="video-duration">${video.duration}</div>
                        </a>
                    </div>
                    <div class="video-info">
                        <a href="${video.videoUrl}" class="video-title" target="_blank" rel="noopener">
                            ${video.title}
                        </a>
                        <div class="video-channel">${video.domain}</div>
                        <div class="video-meta">
                            <div class="video-views">👁 ${video.views}</div>
                            <div class="video-date">📅 ${video.uploadDate}</div>
                        </div>
                    </div>
                `;

                container.appendChild(videoDiv);
            });

            console.log('✅ Test videos loaded successfully');
        }

        function clearVideos() {
            const container = document.getElementById('video-container');
            container.innerHTML = '';
            console.log('🗑️ Videos cleared');
        }

        // Auto-load test videos on page load
        window.addEventListener('load', () => {
            setTimeout(loadTestVideos, 500);
        });
    </script>
</body>
</html>
