<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Search Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .test-btn {
            background: #4285f4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .result {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .search-input {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 Quick Search Test</h1>
    
    <div class="test-section">
        <h3>1. Direct API Test</h3>
        <button class="test-btn" onclick="testDirectAPI()">Test Google API</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Search Function Test</h3>
        <input type="text" class="search-input" id="search-query" value="javascript" placeholder="Enter search query">
        <button class="test-btn" onclick="testSearchFunction()">Test Search</button>
        <div id="search-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Navigation Test</h3>
        <button class="test-btn" onclick="testNavigation()">Test Results Page</button>
        <div id="nav-result"></div>
    </div>
    
    <script>
        // Test direct API call
        async function testDirectAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '<div>🔄 Testing API...</div>';
            
            try {
                const apiKey = 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM';
                const searchEngineId = '30a8567a4e17d49d2';
                const query = 'test';
                
                const url = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${query}&num=3`;
                
                console.log('Making API request to:', url);
                
                const response = await fetch(url);
                console.log('API Response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API Error:', errorText);
                    result.className = 'test-section error';
                    result.innerHTML = `<h3>1. Direct API Test</h3><div>❌ API Error: ${response.status} - ${errorText}</div>`;
                    return;
                }
                
                const data = await response.json();
                console.log('API Response data:', data);
                
                if (data.items && data.items.length > 0) {
                    result.className = 'test-section success';
                    let html = `<h3>1. Direct API Test</h3><div>✅ API Working! Found ${data.items.length} results:</div>`;
                    data.items.forEach(item => {
                        html += `<div class="result">
                            <strong>${item.title}</strong><br>
                            <small>${item.displayLink}</small><br>
                            ${item.snippet}
                        </div>`;
                    });
                    result.innerHTML = html;
                } else if (data.error) {
                    result.className = 'test-section error';
                    result.innerHTML = `<h3>1. Direct API Test</h3><div>❌ API Error: ${data.error.message}</div>`;
                } else {
                    result.className = 'test-section error';
                    result.innerHTML = `<h3>1. Direct API Test</h3><div>❌ No results returned</div>`;
                }
                
            } catch (error) {
                console.error('API Test Error:', error);
                result.className = 'test-section error';
                result.innerHTML = `<h3>1. Direct API Test</h3><div>❌ Error: ${error.message}</div>`;
            }
        }
        
        // Test search function
        async function testSearchFunction() {
            const query = document.getElementById('search-query').value.trim();
            const result = document.getElementById('search-result');
            
            if (!query) {
                result.innerHTML = '<div>❌ Please enter a search query</div>';
                return;
            }
            
            result.innerHTML = '<div>🔄 Testing search function...</div>';
            
            try {
                // Manual search implementation
                const apiKey = 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM';
                const searchEngineId = '30a8567a4e17d49d2';
                
                const url = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&num=5`;
                
                console.log('Search URL:', url);
                
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.items && data.items.length > 0) {
                    result.className = 'test-section success';
                    let html = `<h3>2. Search Function Test</h3><div>✅ Search successful for "${query}"! Found ${data.items.length} results:</div>`;
                    data.items.forEach(item => {
                        html += `<div class="result">
                            <strong>${item.title}</strong><br>
                            <small>${item.displayLink}</small><br>
                            ${item.snippet}
                        </div>`;
                    });
                    result.innerHTML = html;
                } else {
                    result.className = 'test-section error';
                    result.innerHTML = `<h3>2. Search Function Test</h3><div>❌ No results found for "${query}"</div>`;
                }
                
            } catch (error) {
                console.error('Search Error:', error);
                result.className = 'test-section error';
                result.innerHTML = `<h3>2. Search Function Test</h3><div>❌ Search failed: ${error.message}</div>`;
            }
        }
        
        // Test navigation
        function testNavigation() {
            const result = document.getElementById('nav-result');
            const query = document.getElementById('search-query').value.trim() || 'test';
            
            result.innerHTML = '<div>🔄 Testing navigation...</div>';
            
            try {
                const params = new URLSearchParams({
                    q: query,
                    start: 1,
                    type: 'web'
                });
                
                const resultsUrl = `results.html?${params.toString()}`;
                
                result.className = 'test-section success';
                result.innerHTML = `
                    <h3>3. Navigation Test</h3>
                    <div>✅ Navigation URL created: <a href="${resultsUrl}" target="_blank">${resultsUrl}</a></div>
                    <button class="test-btn" onclick="window.open('${resultsUrl}', '_blank')">Open Results Page</button>
                `;
                
            } catch (error) {
                result.className = 'test-section error';
                result.innerHTML = `<h3>3. Navigation Test</h3><div>❌ Navigation failed: ${error.message}</div>`;
            }
        }
        
        // Auto-run API test on page load
        window.addEventListener('load', () => {
            testDirectAPI();
        });
    </script>
</body>
</html>
