<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Debug - Find Issues</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .error {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .test-btn {
            background: #4285f4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #3367d6;
        }
        .log-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .search-test {
            background: white;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .search-input {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 Search Debug Tool</h1>
    <p>This tool will help identify why search is failing.</p>
    
    <div class="debug-section">
        <h3>1. JavaScript Console Errors</h3>
        <div id="console-errors">Checking for JavaScript errors...</div>
        <div class="log-output" id="error-log"></div>
    </div>
    
    <div class="debug-section">
        <h3>2. API Configuration Test</h3>
        <div id="api-config">Testing API configuration...</div>
        <button class="test-btn" onclick="testApiConfig()">Test API Config</button>
    </div>
    
    <div class="debug-section">
        <h3>3. Direct API Call Test</h3>
        <div id="api-test">Ready to test API call...</div>
        <button class="test-btn" onclick="testDirectApi()">Test Direct API</button>
        <div class="log-output" id="api-log"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. Search Function Test</h3>
        <div id="search-function">Testing search functions...</div>
        <button class="test-btn" onclick="testSearchFunction()">Test Search Function</button>
    </div>
    
    <div class="debug-section">
        <h3>5. Network Requests Monitor</h3>
        <div id="network-monitor">Monitoring network requests...</div>
        <button class="test-btn" onclick="startNetworkMonitor()">Start Monitor</button>
        <div class="log-output" id="network-log"></div>
    </div>
    
    <div class="search-test">
        <h3>6. Live Search Test</h3>
        <input type="text" class="search-input" id="test-query" placeholder="Enter search query" value="javascript">
        <button class="test-btn" onclick="performLiveSearch()">Search Now</button>
        <div id="search-results"></div>
    </div>
    
    <div class="debug-section">
        <h3>7. Browser Compatibility</h3>
        <div id="browser-compat">Checking browser compatibility...</div>
    </div>
    
    <script>
        // Capture console errors
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        const errors = [];
        
        console.error = function(...args) {
            errors.push({type: 'error', message: args.join(' '), time: new Date()});
            updateErrorLog();
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            errors.push({type: 'warning', message: args.join(' '), time: new Date()});
            updateErrorLog();
            originalConsoleWarn.apply(console, args);
        };
        
        function updateErrorLog() {
            const errorLog = document.getElementById('error-log');
            const consoleErrors = document.getElementById('console-errors');
            
            if (errors.length === 0) {
                consoleErrors.className = 'debug-section success';
                consoleErrors.innerHTML = '✅ No JavaScript errors detected';
                errorLog.innerHTML = 'No errors logged.';
            } else {
                consoleErrors.className = 'debug-section error';
                consoleErrors.innerHTML = `❌ ${errors.length} JavaScript errors detected`;
                
                let logHtml = '';
                errors.forEach(error => {
                    logHtml += `[${error.time.toLocaleTimeString()}] ${error.type.toUpperCase()}: ${error.message}\n`;
                });
                errorLog.innerHTML = logHtml;
            }
        }
        
        // Test API configuration
        function testApiConfig() {
            const result = document.getElementById('api-config');
            
            try {
                // Check if Utils is loaded
                if (typeof Utils === 'undefined') {
                    result.className = 'debug-section error';
                    result.innerHTML = '❌ Utils.js not loaded';
                    return;
                }
                
                // Check API configuration
                const config = Utils.CONFIG;
                if (!config) {
                    result.className = 'debug-section error';
                    result.innerHTML = '❌ CONFIG not found in Utils';
                    return;
                }
                
                let configHtml = '📋 API Configuration:<br>';
                configHtml += `API Key: ${config.GOOGLE_API_KEY ? config.GOOGLE_API_KEY.substring(0, 10) + '...' : 'NOT SET'}<br>`;
                configHtml += `Search Engine ID: ${config.SEARCH_ENGINE_ID || 'NOT SET'}<br>`;
                configHtml += `Base URL: ${config.BASE_URL || 'NOT SET'}<br>`;
                
                const isConfigured = Utils.checkApiConfiguration();
                if (isConfigured) {
                    result.className = 'debug-section success';
                    configHtml += '✅ API Configuration Valid';
                } else {
                    result.className = 'debug-section warning';
                    configHtml += '⚠️ API Configuration Invalid or Demo Mode';
                }
                
                result.innerHTML = configHtml;
                
            } catch (error) {
                result.className = 'debug-section error';
                result.innerHTML = `❌ Error testing API config: ${error.message}`;
            }
        }
        
        // Test direct API call
        async function testDirectApi() {
            const result = document.getElementById('api-test');
            const log = document.getElementById('api-log');
            
            result.innerHTML = '🔄 Testing direct API call...';
            log.innerHTML = 'Starting API test...\n';
            
            try {
                const apiKey = 'AIzaSyC3ZD5RiNGkyUPjOspKMN5HlPe2AqSUPvM';
                const searchEngineId = '30a8567a4e17d49d2';
                const query = 'test';
                
                const url = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${query}&num=3`;
                
                log.innerHTML += `Making request to: ${url}\n`;
                
                const response = await fetch(url);
                log.innerHTML += `Response status: ${response.status} ${response.statusText}\n`;
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log.innerHTML += `Error response: ${errorText}\n`;
                    result.className = 'debug-section error';
                    result.innerHTML = `❌ API call failed: ${response.status} ${response.statusText}`;
                    return;
                }
                
                const data = await response.json();
                log.innerHTML += `Response received: ${JSON.stringify(data, null, 2)}\n`;
                
                if (data.items && data.items.length > 0) {
                    result.className = 'debug-section success';
                    result.innerHTML = `✅ API call successful! Found ${data.items.length} results`;
                } else if (data.error) {
                    result.className = 'debug-section error';
                    result.innerHTML = `❌ API error: ${data.error.message}`;
                } else {
                    result.className = 'debug-section warning';
                    result.innerHTML = '⚠️ API call successful but no results returned';
                }
                
            } catch (error) {
                log.innerHTML += `Exception: ${error.message}\n`;
                result.className = 'debug-section error';
                result.innerHTML = `❌ API test failed: ${error.message}`;
            }
        }
        
        // Test search function
        function testSearchFunction() {
            const result = document.getElementById('search-function');
            
            try {
                // Check if search classes are loaded
                const checks = [
                    {name: 'SearchEngine', obj: window.SearchEngine},
                    {name: 'ResultsPage', obj: window.ResultsPage},
                    {name: 'Utils', obj: window.Utils},
                    {name: 'searchEngine instance', obj: window.searchEngine},
                    {name: 'resultsPage instance', obj: window.resultsPage}
                ];
                
                let html = '🔍 Search Function Status:<br>';
                let allGood = true;
                
                checks.forEach(check => {
                    if (check.obj) {
                        html += `✅ ${check.name}: Loaded<br>`;
                    } else {
                        html += `❌ ${check.name}: Not found<br>`;
                        allGood = false;
                    }
                });
                
                if (allGood) {
                    result.className = 'debug-section success';
                    html += '<br>✅ All search functions loaded successfully';
                } else {
                    result.className = 'debug-section error';
                    html += '<br>❌ Some search functions missing';
                }
                
                result.innerHTML = html;
                
            } catch (error) {
                result.className = 'debug-section error';
                result.innerHTML = `❌ Error testing search functions: ${error.message}`;
            }
        }
        
        // Monitor network requests
        function startNetworkMonitor() {
            const log = document.getElementById('network-log');
            const monitor = document.getElementById('network-monitor');
            
            monitor.innerHTML = '📡 Network monitor started...';
            log.innerHTML = 'Monitoring network requests...\n';
            
            // Override fetch to monitor requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                log.innerHTML += `[${new Date().toLocaleTimeString()}] FETCH: ${url}\n`;
                log.scrollTop = log.scrollHeight;
                
                return originalFetch.apply(this, args)
                    .then(response => {
                        log.innerHTML += `[${new Date().toLocaleTimeString()}] RESPONSE: ${response.status} ${response.statusText}\n`;
                        log.scrollTop = log.scrollHeight;
                        return response;
                    })
                    .catch(error => {
                        log.innerHTML += `[${new Date().toLocaleTimeString()}] ERROR: ${error.message}\n`;
                        log.scrollTop = log.scrollHeight;
                        throw error;
                    });
            };
            
            monitor.className = 'debug-section success';
            monitor.innerHTML = '✅ Network monitor active - make a search to see requests';
        }
        
        // Perform live search test
        async function performLiveSearch() {
            const query = document.getElementById('test-query').value.trim();
            const results = document.getElementById('search-results');
            
            if (!query) {
                results.innerHTML = '<div class="error">Please enter a search query</div>';
                return;
            }
            
            results.innerHTML = '<div>🔍 Searching...</div>';
            
            try {
                // Try to use the search engine
                if (window.searchEngine && window.searchEngine.searchGoogle) {
                    const searchResults = await window.searchEngine.searchGoogle(query, 1, 'web');
                    
                    if (searchResults && searchResults.items) {
                        let html = `<div class="success">✅ Search successful! Found ${searchResults.items.length} results:</div>`;
                        searchResults.items.slice(0, 3).forEach(item => {
                            html += `<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <strong>${item.title}</strong><br>
                                <small>${item.displayLink || item.link}</small><br>
                                ${item.snippet}
                            </div>`;
                        });
                        results.innerHTML = html;
                    } else {
                        results.innerHTML = '<div class="warning">⚠️ Search returned no results</div>';
                    }
                } else {
                    results.innerHTML = '<div class="error">❌ Search engine not available</div>';
                }
                
            } catch (error) {
                results.innerHTML = `<div class="error">❌ Search failed: ${error.message}</div>`;
            }
        }
        
        // Check browser compatibility
        function checkBrowserCompatibility() {
            const result = document.getElementById('browser-compat');
            
            const features = [
                {name: 'Fetch API', test: () => typeof fetch !== 'undefined'},
                {name: 'Promises', test: () => typeof Promise !== 'undefined'},
                {name: 'Arrow Functions', test: () => {try { eval('() => {}'); return true; } catch(e) { return false; }}},
                {name: 'Local Storage', test: () => typeof localStorage !== 'undefined'},
                {name: 'Service Workers', test: () => 'serviceWorker' in navigator},
                {name: 'Speech Recognition', test: () => 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window}
            ];
            
            let html = '🌐 Browser Compatibility:<br>';
            let compatible = true;
            
            features.forEach(feature => {
                const supported = feature.test();
                html += `${supported ? '✅' : '❌'} ${feature.name}: ${supported ? 'Supported' : 'Not supported'}<br>`;
                if (!supported && feature.name !== 'Speech Recognition' && feature.name !== 'Service Workers') {
                    compatible = false;
                }
            });
            
            if (compatible) {
                result.className = 'debug-section success';
                html += '<br>✅ Browser is compatible';
            } else {
                result.className = 'debug-section error';
                html += '<br>❌ Browser compatibility issues detected';
            }
            
            result.innerHTML = html;
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            updateErrorLog();
            testApiConfig();
            testSearchFunction();
            checkBrowserCompatibility();
        });
    </script>
    
    <!-- Load the search scripts -->
    <script src="js/utils.js"></script>
    <script src="js/search.js"></script>
    <script src="js/results.js"></script>
    <script src="js/voice-search.js"></script>
    <script src="js/advanced-search.js"></script>
    <script src="js/instant-answers.js"></script>
    <script src="js/search-tools.js"></script>
</body>
</html>
