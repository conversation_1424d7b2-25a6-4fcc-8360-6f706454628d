/**
 * Search Box Component
 * Enhanced search input with autocomplete, voice search, and image search
 */

import BaseComponent from './base.component.js';
import apiService from '../services/api.service.js';
import { APP_CONFIG } from '../config/app.config.js';
import dom, { createElement } from '../utils/dom.js';

export class SearchBoxComponent extends BaseComponent {
  getDefaultOptions() {
    return {
      ...super.getDefaultOptions(),
      placeholder: '',
      showVoiceSearch: APP_CONFIG.FEATURES.VOICE_SEARCH,
      showImageSearch: APP_CONFIG.FEATURES.IMAGE_SEARCH,
      enableAutocomplete: true,
      debounceDelay: APP_CONFIG.SEARCH.DEBOUNCE_DELAY,
      maxSuggestions: APP_CONFIG.SEARCH.MAX_SUGGESTIONS,
      className: 'search-container'
    };
  }
  
  async init() {
    await super.init();
    this.setupSearchInput();
    this.setupSearchIcons();
    this.setupSuggestions();
    this.setupKeyboardNavigation();
  }
  
  setupElement() {
    super.setupElement();
    
    // Create search input if not exists
    if (!this.element.querySelector('.search-box')) {
      this.createSearchInput();
    }
    
    this.input = this.element.querySelector('.search-box');
    this.iconsContainer = this.element.querySelector('.search-icons');
    this.suggestionsContainer = this.element.querySelector('.suggestions');
  }
  
  createSearchInput() {
    const input = createElement('input', {
      type: 'text',
      className: 'search-box',
      placeholder: this.options.placeholder,
      autocomplete: 'off',
      spellcheck: 'false',
      maxlength: APP_CONFIG.SEARCH.MAX_QUERY_LENGTH,
      title: 'Search',
      role: 'combobox',
      'aria-expanded': 'false',
      'aria-autocomplete': 'list'
    });
    
    const iconsContainer = createElement('div', {
      className: 'search-icons'
    });
    
    const suggestionsContainer = createElement('div', {
      className: 'suggestions',
      role: 'listbox',
      style: 'display: none;'
    });
    
    this.element.appendChild(input);
    this.element.appendChild(iconsContainer);
    this.element.appendChild(suggestionsContainer);
  }
  
  setupSearchInput() {
    if (!this.input) return;
    
    // Debounced input handler
    let debounceTimer;
    
    this.addEventListener('input', (event) => {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        this.handleInput(event);
      }, this.options.debounceDelay);
    });
    
    this.addEventListener('focus', this.handleFocus.bind(this));
    this.addEventListener('blur', this.handleBlur.bind(this));
    this.addEventListener('keydown', this.handleKeydown.bind(this));
  }
  
  setupSearchIcons() {
    if (!this.iconsContainer) return;
    
    this.iconsContainer.innerHTML = '';
    
    // Voice search icon
    if (this.options.showVoiceSearch) {
      const voiceIcon = this.createVoiceIcon();
      this.iconsContainer.appendChild(voiceIcon);
    }
    
    // Image search icon
    if (this.options.showImageSearch) {
      const imageIcon = this.createImageIcon();
      this.iconsContainer.appendChild(imageIcon);
    }
    
    // Search icon
    const searchIcon = this.createSearchIcon();
    this.iconsContainer.appendChild(searchIcon);
  }
  
  createVoiceIcon() {
    const icon = createElement('div', {
      className: 'voice-icon',
      title: 'Search by voice',
      role: 'button',
      tabindex: '0'
    });
    
    icon.innerHTML = `
      <svg viewBox="0 0 24 24">
        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
      </svg>
    `;
    
    icon.addEventListener('click', this.handleVoiceSearch.bind(this));
    icon.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.handleVoiceSearch();
      }
    });
    
    return icon;
  }
  
  createImageIcon() {
    const icon = createElement('div', {
      className: 'camera-icon',
      title: 'Search by image',
      role: 'button',
      tabindex: '0'
    });
    
    icon.innerHTML = `
      <svg viewBox="0 0 24 24">
        <path d="M14,6H10L8,4H6A2,2 0 0,0 4,6V18A2,2 0 0,0 6,20H18A2,2 0 0,0 20,18V8A2,2 0 0,0 18,6H16L14,6M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
      </svg>
    `;
    
    icon.addEventListener('click', this.handleImageSearch.bind(this));
    icon.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.handleImageSearch();
      }
    });
    
    return icon;
  }
  
  createSearchIcon() {
    const icon = createElement('div', {
      className: 'search-icon',
      title: 'Search',
      role: 'button',
      tabindex: '0'
    });
    
    icon.innerHTML = `
      <svg viewBox="0 0 24 24">
        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
      </svg>
    `;
    
    icon.addEventListener('click', this.handleSearch.bind(this));
    icon.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.handleSearch();
      }
    });
    
    return icon;
  }
  
  setupSuggestions() {
    if (!this.suggestionsContainer || !this.options.enableAutocomplete) return;
    
    this.suggestions = [];
    this.selectedSuggestionIndex = -1;
  }
  
  setupKeyboardNavigation() {
    // Keyboard navigation is handled in handleKeydown
  }
  
  async handleInput(event) {
    const query = event.target.value.trim();
    
    this.setState({ query });
    this.emit('input', { query });
    
    if (this.options.enableAutocomplete && query.length >= 2) {
      await this.showSuggestions(query);
    } else {
      this.hideSuggestions();
    }
  }
  
  handleFocus(event) {
    this.setState({ focused: true });
    this.emit('focus', { query: event.target.value });
    
    // Show recent suggestions if no query
    if (this.options.enableAutocomplete && !event.target.value.trim()) {
      this.showRecentSuggestions();
    }
  }
  
  handleBlur(event) {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      this.setState({ focused: false });
      this.hideSuggestions();
      this.emit('blur', { query: event.target.value });
    }, 150);
  }
  
  handleKeydown(event) {
    const { key } = event;
    
    switch (key) {
      case 'Enter':
        event.preventDefault();
        if (this.selectedSuggestionIndex >= 0) {
          this.selectSuggestion(this.suggestions[this.selectedSuggestionIndex]);
        } else {
          this.handleSearch();
        }
        break;
        
      case 'ArrowDown':
        event.preventDefault();
        this.navigateSuggestions(1);
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        this.navigateSuggestions(-1);
        break;
        
      case 'Escape':
        this.hideSuggestions();
        this.input.blur();
        break;
        
      case 'Tab':
        if (this.selectedSuggestionIndex >= 0) {
          event.preventDefault();
          this.selectSuggestion(this.suggestions[this.selectedSuggestionIndex]);
        }
        break;
    }
  }
  
  async showSuggestions(query) {
    try {
      this.suggestions = await apiService.getSuggestions(query);
      this.renderSuggestions();
      this.showSuggestionsContainer();
    } catch (error) {
      this.logger.error('Failed to get suggestions', error);
    }
  }
  
  showRecentSuggestions() {
    // Get recent searches from storage
    const recentSearches = this.getRecentSearches();
    this.suggestions = recentSearches.slice(0, this.options.maxSuggestions);
    this.renderSuggestions();
    this.showSuggestionsContainer();
  }
  
  renderSuggestions() {
    if (!this.suggestionsContainer) return;
    
    this.suggestionsContainer.innerHTML = '';
    this.selectedSuggestionIndex = -1;
    
    this.suggestions.forEach((suggestion, index) => {
      const item = this.createSuggestionItem(suggestion, index);
      this.suggestionsContainer.appendChild(item);
    });
  }
  
  createSuggestionItem(suggestion, index) {
    const item = createElement('div', {
      className: 'suggestion-item',
      role: 'option',
      dataset: { index }
    });
    
    // Search icon
    const icon = createElement('svg', {
      className: 'suggestion-icon',
      viewBox: '0 0 24 24'
    });
    icon.innerHTML = '<path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>';
    
    // Suggestion text
    const text = createElement('span', {
      className: 'suggestion-text'
    }, suggestion);
    
    // Remove button
    const removeBtn = createElement('svg', {
      className: 'suggestion-remove',
      viewBox: '0 0 24 24',
      title: 'Remove'
    });
    removeBtn.innerHTML = '<path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>';
    
    item.appendChild(icon);
    item.appendChild(text);
    item.appendChild(removeBtn);
    
    // Event listeners
    item.addEventListener('click', () => this.selectSuggestion(suggestion));
    item.addEventListener('mouseenter', () => this.highlightSuggestion(index));
    removeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.removeSuggestion(suggestion);
    });
    
    return item;
  }
  
  showSuggestionsContainer() {
    if (this.suggestionsContainer && this.suggestions.length > 0) {
      dom.show(this.suggestionsContainer);
      this.input.setAttribute('aria-expanded', 'true');
    }
  }
  
  hideSuggestions() {
    if (this.suggestionsContainer) {
      dom.hide(this.suggestionsContainer);
      this.input.setAttribute('aria-expanded', 'false');
      this.selectedSuggestionIndex = -1;
    }
  }
  
  navigateSuggestions(direction) {
    if (!this.suggestions.length) return;
    
    this.selectedSuggestionIndex += direction;
    
    if (this.selectedSuggestionIndex < -1) {
      this.selectedSuggestionIndex = this.suggestions.length - 1;
    } else if (this.selectedSuggestionIndex >= this.suggestions.length) {
      this.selectedSuggestionIndex = -1;
    }
    
    this.highlightSuggestion(this.selectedSuggestionIndex);
  }
  
  highlightSuggestion(index) {
    const items = this.suggestionsContainer.querySelectorAll('.suggestion-item');
    
    items.forEach((item, i) => {
      if (i === index) {
        dom.addClass(item, 'selected');
      } else {
        dom.removeClass(item, 'selected');
      }
    });
    
    this.selectedSuggestionIndex = index;
  }
  
  selectSuggestion(suggestion) {
    this.input.value = suggestion;
    this.hideSuggestions();
    this.saveToRecentSearches(suggestion);
    this.emit('suggestionSelected', { suggestion });
    this.handleSearch();
  }
  
  removeSuggestion(suggestion) {
    this.removeFromRecentSearches(suggestion);
    this.suggestions = this.suggestions.filter(s => s !== suggestion);
    this.renderSuggestions();
    
    if (this.suggestions.length === 0) {
      this.hideSuggestions();
    }
  }
  
  handleSearch() {
    const query = this.input.value.trim();
    
    if (query.length === 0) {
      this.emit('error', { message: APP_CONFIG.ERRORS.INVALID_QUERY });
      return;
    }
    
    this.saveToRecentSearches(query);
    this.hideSuggestions();
    this.emit('search', { query });
  }
  
  handleVoiceSearch() {
    this.emit('voiceSearch');
  }
  
  handleImageSearch() {
    this.emit('imageSearch');
  }
  
  // Utility methods
  getRecentSearches() {
    try {
      return JSON.parse(localStorage.getItem(APP_CONFIG.STORAGE_KEYS.SEARCH_HISTORY) || '[]');
    } catch {
      return [];
    }
  }
  
  saveToRecentSearches(query) {
    try {
      const recent = this.getRecentSearches();
      const filtered = recent.filter(item => item !== query);
      filtered.unshift(query);
      const limited = filtered.slice(0, 20); // Keep last 20 searches
      localStorage.setItem(APP_CONFIG.STORAGE_KEYS.SEARCH_HISTORY, JSON.stringify(limited));
    } catch (error) {
      this.logger.warn('Failed to save search history', error);
    }
  }
  
  removeFromRecentSearches(query) {
    try {
      const recent = this.getRecentSearches();
      const filtered = recent.filter(item => item !== query);
      localStorage.setItem(APP_CONFIG.STORAGE_KEYS.SEARCH_HISTORY, JSON.stringify(filtered));
    } catch (error) {
      this.logger.warn('Failed to remove from search history', error);
    }
  }
  
  // Public API
  getValue() {
    return this.input ? this.input.value : '';
  }
  
  setValue(value) {
    if (this.input) {
      this.input.value = value;
      this.setState({ query: value });
    }
  }
  
  focus() {
    if (this.input) {
      this.input.focus();
    }
  }
  
  clear() {
    this.setValue('');
    this.hideSuggestions();
  }
}

export default SearchBoxComponent;
