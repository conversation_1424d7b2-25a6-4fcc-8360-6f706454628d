/**
 * Navigation Component
 * Manages search type navigation tabs (All, Images, Videos, News, etc.)
 */

import BaseComponent from './base.component.js';
import { APP_CONFIG } from '../config/app.config.js';
import dom, { createElement } from '../utils/dom.js';

export class NavigationComponent extends BaseComponent {
  getDefaultOptions() {
    return {
      ...super.getDefaultOptions(),
      activeType: 'web',
      enableKeyboardNavigation: true,
      className: 'nav-tabs'
    };
  }
  
  async init() {
    await super.init();
    this.setupNavigation();
    this.setupKeyboardNavigation();
  }
  
  setupNavigation() {
    // Create navigation if it doesn't exist
    if (!this.element.querySelector('.nav-content')) {
      this.createNavigation();
    }
    
    this.navContent = this.element.querySelector('.nav-content');
    this.navTabs = this.element.querySelectorAll('.nav-tab');
    
    // Set initial active tab
    this.setActiveTab(this.options.activeType);
  }
  
  createNavigation() {
    const navContent = createElement('div', {
      className: 'nav-content',
      role: 'tablist'
    });
    
    const navigationItems = this.getNavigationItems();
    
    navigationItems.forEach(item => {
      const tab = this.createNavTab(item);
      navContent.appendChild(tab);
    });
    
    this.element.appendChild(navContent);
  }
  
  getNavigationItems() {
    return [
      {
        type: 'web',
        label: 'All',
        icon: `<path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>`,
        enabled: true
      },
      {
        type: 'images',
        label: 'Images',
        icon: `<path d="M21,19V5c0,-1.1 -0.9,-2 -2,-2H5c-1.1,0 -2,0.9 -2,2v14c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2zM8.5,13.5l2.5,3.01L14.5,12l4.5,6H5l3.5,-4.5z"/>`,
        enabled: APP_CONFIG.FEATURES.IMAGE_SEARCH
      },
      {
        type: 'videos',
        label: 'Videos',
        icon: `<path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>`,
        enabled: true
      },
      {
        type: 'news',
        label: 'News',
        icon: `<path d="M20,11H4V8H20M20,15H13V13H20M20,19H13V17H20M11,19H4V13H11M20.33,4.67L18.67,3L17,4.67L15.33,3L13.67,4.67L12,3L10.33,4.67L8.67,3L7,4.67L5.33,3L3.67,4.67L2,3V19A2,2 0 0,0 4,21H20A2,2 0 0,0 22,19V3L20.33,4.67Z"/>`,
        enabled: true
      },
      {
        type: 'shopping',
        label: 'Shopping',
        icon: `<path d="M7,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2zM1,2v2h2l3.6,7.59 -1.35,2.45c-0.16,0.28 -0.25,0.61 -0.25,0.96 0,1.1 0.9,2 2,2h12v-2L7.42,15c-0.14,0 -0.25,-0.11 -0.25,-0.25l0.03,-0.12L8.1,13h7.45c0.75,0 1.41,-0.41 1.75,-1.03L21.7,4H5.21l-0.94,-2L1,2zM17,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2z"/>`,
        enabled: true
      },
      {
        type: 'books',
        label: 'Books',
        icon: `<path d="M18,2H6C4.9,2 4,2.9 4,4v16c0,1.1 0.9,2 2,2h12c1.1,0 2,-0.9 2,-2V4C20,2.9 19.1,2 18,2zM18,20H6V4h2v7l2.5,-1.5L13,11V4h5V20z"/>`,
        enabled: true
      },
      {
        type: 'flights',
        label: 'Flights',
        icon: `<path d="M21,16v-2l-8,-5V3.5c0,-0.83 -0.67,-1.5 -1.5,-1.5S10,2.67 10,3.5V9l-8,5v2l8,-2.5V19l-2,1.5V22l3.5,-1 3.5,1v-1.5L13,19v-5.5L21,16z"/>`,
        enabled: true
      },
      {
        type: 'finance',
        label: 'Finance',
        icon: `<path d="M11.8,10.9c-2.27,-0.59 -3,-1.2 -3,-2.15 0,-1.09 1.01,-1.85 2.7,-1.85 1.78,0 2.44,0.85 2.5,2.1h2.21c-0.07,-1.72 -1.12,-3.3 -3.21,-3.81V3h-3v2.16c-1.94,0.42 -3.5,1.68 -3.5,3.61 0,2.31 1.91,3.46 4.7,4.13 2.5,0.6 3,1.48 3,2.41 0,0.69 -0.49,1.79 -2.7,1.79 -2.06,0 -2.87,-0.92 -2.98,-2.1h-2.2c0.12,2.19 1.76,3.42 3.68,3.83V21h3v-2.15c1.95,-0.37 3.5,-1.5 3.5,-3.55 0,-2.84 -2.43,-3.81 -4.7,-4.4z"/>`,
        enabled: true
      }
    ].filter(item => item.enabled);
  }
  
  createNavTab(item) {
    const tab = createElement('a', {
      href: '#',
      className: 'nav-tab',
      dataset: { type: item.type },
      role: 'tab',
      'aria-selected': item.type === this.options.activeType ? 'true' : 'false',
      tabindex: item.type === this.options.activeType ? '0' : '-1'
    });
    
    tab.innerHTML = `
      <svg viewBox="0 0 24 24" aria-hidden="true">
        ${item.icon}
      </svg>
      <span>${item.label}</span>
    `;
    
    return tab;
  }
  
  setupDefaultEvents() {
    this.addEventListener('click', this.handleTabClick.bind(this));
    this.addEventListener('keydown', this.handleKeydown.bind(this));
  }
  
  setupKeyboardNavigation() {
    if (!this.options.enableKeyboardNavigation) return;
    
    // Arrow key navigation
    this.addEventListener('keydown', (event) => {
      if (['ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(event.key)) {
        this.handleArrowNavigation(event);
      }
    });
  }
  
  handleTabClick(event) {
    event.preventDefault();
    
    const tab = event.target.closest('.nav-tab');
    if (!tab) return;
    
    const type = tab.dataset.type;
    
    if (type !== this.options.activeType) {
      this.setActiveTab(type);
      this.emit('tabChange', { 
        type, 
        previousType: this.options.activeType 
      });
      
      this.options.activeType = type;
      
      this.logger.userAction('Navigation Tab Click', { type });
    }
  }
  
  handleKeydown(event) {
    const tab = event.target.closest('.nav-tab');
    if (!tab) return;
    
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        tab.click();
        break;
        
      case 'ArrowLeft':
      case 'ArrowRight':
      case 'Home':
      case 'End':
        this.handleArrowNavigation(event);
        break;
    }
  }
  
  handleArrowNavigation(event) {
    event.preventDefault();
    
    const tabs = Array.from(this.navTabs);
    const currentIndex = tabs.findIndex(tab => tab.dataset.type === this.options.activeType);
    let newIndex;
    
    switch (event.key) {
      case 'ArrowLeft':
        newIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
        break;
      case 'ArrowRight':
        newIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = tabs.length - 1;
        break;
      default:
        return;
    }
    
    const newTab = tabs[newIndex];
    if (newTab) {
      newTab.focus();
      // Optionally activate the tab immediately
      if (event.ctrlKey || event.metaKey) {
        newTab.click();
      }
    }
  }
  
  setActiveTab(type) {
    this.navTabs.forEach(tab => {
      const isActive = tab.dataset.type === type;
      
      if (isActive) {
        dom.addClass(tab, 'active');
        tab.setAttribute('aria-selected', 'true');
        tab.setAttribute('tabindex', '0');
      } else {
        dom.removeClass(tab, 'active');
        tab.setAttribute('aria-selected', 'false');
        tab.setAttribute('tabindex', '-1');
      }
    });
    
    this.setState({ activeType: type });
  }
  
  getActiveTab() {
    return this.options.activeType;
  }
  
  enableTab(type) {
    const tab = this.element.querySelector(`[data-type="${type}"]`);
    if (tab) {
      tab.style.display = '';
      tab.setAttribute('aria-hidden', 'false');
    }
  }
  
  disableTab(type) {
    const tab = this.element.querySelector(`[data-type="${type}"]`);
    if (tab) {
      tab.style.display = 'none';
      tab.setAttribute('aria-hidden', 'true');
      
      // If this was the active tab, switch to 'web'
      if (this.options.activeType === type) {
        this.setActiveTab('web');
      }
    }
  }
  
  updateTabCount(type, count) {
    const tab = this.element.querySelector(`[data-type="${type}"]`);
    if (!tab) return;
    
    let countElement = tab.querySelector('.tab-count');
    
    if (count > 0) {
      if (!countElement) {
        countElement = createElement('span', {
          className: 'tab-count',
          'aria-label': `${count} results`
        });
        tab.appendChild(countElement);
      }
      countElement.textContent = count > 999 ? '999+' : count.toString();
    } else if (countElement) {
      countElement.remove();
    }
  }
  
  highlightTab(type, highlight = true) {
    const tab = this.element.querySelector(`[data-type="${type}"]`);
    if (tab) {
      if (highlight) {
        dom.addClass(tab, 'highlighted');
      } else {
        dom.removeClass(tab, 'highlighted');
      }
    }
  }
  
  addCustomTab(config) {
    const { type, label, icon, position = 'end' } = config;
    
    if (this.element.querySelector(`[data-type="${type}"]`)) {
      this.logger.warn(`Tab with type '${type}' already exists`);
      return;
    }
    
    const tab = this.createNavTab({ type, label, icon, enabled: true });
    
    if (position === 'start') {
      this.navContent.insertBefore(tab, this.navContent.firstChild);
    } else {
      this.navContent.appendChild(tab);
    }
    
    // Update navTabs collection
    this.navTabs = this.element.querySelectorAll('.nav-tab');
    
    this.logger.info(`Added custom tab: ${type}`);
  }
  
  removeTab(type) {
    const tab = this.element.querySelector(`[data-type="${type}"]`);
    if (tab) {
      tab.remove();
      
      // Update navTabs collection
      this.navTabs = this.element.querySelectorAll('.nav-tab');
      
      // If this was the active tab, switch to 'web'
      if (this.options.activeType === type) {
        this.setActiveTab('web');
      }
      
      this.logger.info(`Removed tab: ${type}`);
    }
  }
  
  getTabElement(type) {
    return this.element.querySelector(`[data-type="${type}"]`);
  }
  
  getAllTabs() {
    return Array.from(this.navTabs).map(tab => ({
      type: tab.dataset.type,
      label: tab.querySelector('span').textContent,
      active: tab.classList.contains('active'),
      element: tab
    }));
  }
  
  // Animation methods
  animateTabChange(fromType, toType) {
    const fromTab = this.getTabElement(fromType);
    const toTab = this.getTabElement(toType);
    
    if (fromTab && toTab) {
      // Add transition classes
      dom.addClass(fromTab, 'tab-exit');
      dom.addClass(toTab, 'tab-enter');
      
      // Remove classes after animation
      setTimeout(() => {
        dom.removeClass(fromTab, 'tab-exit');
        dom.removeClass(toTab, 'tab-enter');
      }, 300);
    }
  }
  
  // Accessibility methods
  announceTabChange(type) {
    const tab = this.getTabElement(type);
    if (tab) {
      const label = tab.querySelector('span').textContent;
      
      // Create or update live region
      let liveRegion = document.getElementById('nav-live-region');
      if (!liveRegion) {
        liveRegion = createElement('div', {
          id: 'nav-live-region',
          'aria-live': 'polite',
          'aria-atomic': 'true',
          style: 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;'
        });
        document.body.appendChild(liveRegion);
      }
      
      liveRegion.textContent = `Switched to ${label} search`;
    }
  }
  
  // Public API
  switchTo(type) {
    if (this.getTabElement(type)) {
      const previousType = this.options.activeType;
      this.setActiveTab(type);
      this.options.activeType = type;
      
      this.animateTabChange(previousType, type);
      this.announceTabChange(type);
      
      this.emit('tabChange', { type, previousType });
      
      return true;
    }
    
    this.logger.warn(`Cannot switch to tab '${type}' - tab not found`);
    return false;
  }
  
  refresh() {
    // Re-create navigation with current options
    this.navContent.innerHTML = '';
    this.createNavigation();
    this.setActiveTab(this.options.activeType);
  }
}

export default NavigationComponent;
