<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Search</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
    </style>
</head>
<body>
    <h1>Debug Search Test</h1>
    <div id="debug-output"></div>
    
    <script>
        function log(message, type = 'debug') {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            output.appendChild(div);
            console.log(message);
        }

        // Test basic functionality
        log('Starting debug test...', 'debug');

        // Test URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('q') || 'test';
        const type = urlParams.get('type') || 'all';
        
        log(`Query: ${query}, Type: ${type}`, 'debug');

        // Test demo results function
        function getDemoResults(query, start = 1, searchType = 'all') {
            log(`Generating demo results for: ${query}, type: ${searchType}`, 'debug');
            
            const results = [];
            const startNum = (start - 1) * 10 + 1;

            if (searchType === 'all') {
                const resultTypes = ['web', 'images', 'news', 'videos'];
                
                for (let i = 0; i < 12; i++) {
                    const type = resultTypes[i % resultTypes.length];
                    results.push({
                        title: `[${type.toUpperCase()}] ${query} - Demo Result ${startNum + i}`,
                        link: `https://example.com/demo-${type}-result-${startNum + i}`,
                        snippet: `This is a demo ${type} search result for "${query}".`,
                        displayLink: 'example.com',
                        resultType: type
                    });
                }
            } else {
                for (let i = 0; i < 10; i++) {
                    results.push({
                        title: `${query} - Demo ${searchType} Result ${startNum + i}`,
                        link: `https://example.com/demo-${searchType}-result-${startNum + i}`,
                        snippet: `This is a demo ${searchType} search result for "${query}".`,
                        displayLink: 'example.com',
                        resultType: searchType
                    });
                }
            }

            const demoData = {
                searchInformation: {
                    totalResults: "1234567",
                    searchTime: 0.45
                },
                items: results
            };
            
            log(`Generated ${results.length} demo results`, 'success');
            return demoData;
        }

        // Test the demo results
        try {
            const demoResults = getDemoResults(query, 1, type);
            log(`Demo results generated successfully: ${demoResults.items.length} items`, 'success');
            
            // Display results
            const resultsDiv = document.createElement('div');
            resultsDiv.innerHTML = '<h2>Demo Results:</h2>';
            
            demoResults.items.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.style.cssText = 'border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;';
                itemDiv.innerHTML = `
                    <h3>${item.title}</h3>
                    <p><strong>Link:</strong> ${item.link}</p>
                    <p><strong>Snippet:</strong> ${item.snippet}</p>
                    <p><strong>Type:</strong> ${item.resultType}</p>
                `;
                resultsDiv.appendChild(itemDiv);
            });
            
            document.body.appendChild(resultsDiv);
            
        } catch (error) {
            log(`Error generating demo results: ${error.message}`, 'error');
        }

        // Test API call (this will likely fail but we can see the error)
        async function testApiCall() {
            log('Testing API call...', 'debug');
            
            const API_CONFIG = {
                GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
                SEARCH_ENGINE_ID: '61201925358ea4e83',
                BASE_URL: 'https://www.googleapis.com/customsearch/v1'
            };

            const params = new URLSearchParams({
                key: API_CONFIG.GOOGLE_API_KEY,
                cx: API_CONFIG.SEARCH_ENGINE_ID,
                q: query,
                start: 1,
                num: 5
            });

            const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
            log(`API URL: ${url.substring(0, 100)}...`, 'debug');

            try {
                const response = await fetch(url);
                log(`API Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API returned ${data.items?.length || 0} results`, 'success');
                } else {
                    const errorData = await response.text();
                    log(`API Error: ${errorData}`, 'error');
                }
            } catch (error) {
                log(`API Call failed: ${error.message}`, 'error');
            }
        }

        // Run API test
        testApiCall();
    </script>
</body>
</html>
