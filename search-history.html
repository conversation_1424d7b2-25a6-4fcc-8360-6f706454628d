<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search History - Google Search</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: arial, sans-serif;
            background: #fff;
            color: #202124;
            line-height: 1.4;
        }

        .header {
            background: #fff;
            border-bottom: 1px solid #dadce0;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .logo {
            font-size: 20px;
            font-weight: normal;
            color: #4285f4;
            text-decoration: none;
        }

        .page-title {
            font-size: 22px;
            color: #202124;
            margin-left: 16px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: #fff;
            color: #3c4043;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn:hover {
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-color: #dadce0;
        }

        .btn-primary {
            background: #4285f4;
            color: white;
            border-color: #4285f4;
        }

        .btn-primary:hover {
            background: #3367d6;
            border-color: #3367d6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .stats-overview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            text-align: center;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 500;
            color: #4285f4;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #5f6368;
        }

        .search-history {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #dadce0;
            overflow: hidden;
        }

        .search-history-header {
            background: #f8f9fa;
            padding: 16px 20px;
            border-bottom: 1px solid #dadce0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .search-history-title {
            font-size: 18px;
            font-weight: 500;
            color: #202124;
        }

        .search-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }

        .search-item:hover {
            background: #f8f9fa;
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .search-query {
            font-size: 16px;
            color: #1a0dab;
            text-decoration: none;
            margin-bottom: 8px;
            display: block;
        }

        .search-query:hover {
            text-decoration: underline;
        }

        .search-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 13px;
            color: #5f6368;
            margin-bottom: 8px;
        }

        .search-performance {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-top: 8px;
        }

        .perf-metric {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
        }

        .perf-metric-icon {
            font-size: 14px;
        }

        .perf-metric-value {
            font-weight: 600;
        }

        .perf-metric.excellent .perf-metric-value {
            color: #28a745;
        }

        .perf-metric.good .perf-metric-value {
            color: #17a2b8;
        }

        .perf-metric.average .perf-metric-value {
            color: #ffc107;
        }

        .perf-metric.slow .perf-metric-value {
            color: #dc3545;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #5f6368;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state-title {
            font-size: 20px;
            margin-bottom: 8px;
            color: #202124;
        }

        .empty-state-description {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .filters {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 6px 12px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: #fff;
            font-size: 13px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .header {
                padding: 12px 16px;
            }

            .page-title {
                font-size: 18px;
                margin-left: 8px;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
                padding: 16px;
            }

            .search-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .search-performance {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .filters {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-select {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-left">
            <a href="index.html" class="logo">Google</a>
            <h1 class="page-title">Search History</h1>
        </div>
        <div class="header-right">
            <a href="results.html" class="btn">New Search</a>
            <button class="btn btn-primary" onclick="clearAllHistory()">Clear History</button>
        </div>
    </header>

    <div class="container">
        <div class="stats-overview" id="stats-overview">
            <div class="stat-card">
                <div class="stat-value" id="total-searches">0</div>
                <div class="stat-label">Total Searches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avg-response-time">--</div>
                <div class="stat-label">Avg Response Time</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="fastest-search">--</div>
                <div class="stat-label">Fastest Search</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="most-searched-type">--</div>
                <div class="stat-label">Most Searched Type</div>
            </div>
        </div>

        <div class="filters">
            <select class="filter-select" id="type-filter" onchange="filterHistory()">
                <option value="">All Types</option>
                <option value="all">All</option>
                <option value="images">Images</option>
                <option value="videos">Videos</option>
                <option value="news">News</option>
                <option value="shopping">Shopping</option>
                <option value="books">Books</option>
            </select>
            <select class="filter-select" id="performance-filter" onchange="filterHistory()">
                <option value="">All Performance</option>
                <option value="excellent">Excellent (Green)</option>
                <option value="good">Good (Blue)</option>
                <option value="average">Average (Yellow)</option>
                <option value="slow">Slow (Red)</option>
            </select>
            <select class="filter-select" id="sort-filter" onchange="filterHistory()">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="fastest">Fastest First</option>
                <option value="slowest">Slowest First</option>
            </select>
        </div>

        <div class="search-history">
            <div class="search-history-header">
                <h2 class="search-history-title">Recent Searches</h2>
                <span id="history-count">0 searches</span>
            </div>
            <div id="search-history-list">
                <div class="empty-state">
                    <div class="empty-state-icon">🔍</div>
                    <div class="empty-state-title">No search history</div>
                    <div class="empty-state-description">Your search history will appear here as you perform searches</div>
                    <a href="results.html" class="btn btn-primary">Start Searching</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SearchHistoryManager {
            constructor() {
                this.searchHistory = [];
                this.filteredHistory = [];
                this.init();
            }

            init() {
                this.loadSearchHistory();
                this.updateStats();
                this.renderHistory();
            }

            loadSearchHistory() {
                try {
                    const stored = localStorage.getItem('searchPerformanceHistory');
                    if (stored) {
                        const data = JSON.parse(stored);
                        this.searchHistory = data.searches || [];
                    }
                } catch (e) {
                    console.warn('Could not load search history:', e);
                    this.searchHistory = [];
                }
                this.filteredHistory = [...this.searchHistory];
            }

            updateStats() {
                const totalSearches = this.searchHistory.length;
                document.getElementById('total-searches').textContent = totalSearches;

                if (totalSearches === 0) {
                    document.getElementById('avg-response-time').textContent = '--';
                    document.getElementById('fastest-search').textContent = '--';
                    document.getElementById('most-searched-type').textContent = '--';
                    return;
                }

                // Calculate average response time
                const avgTotal = this.searchHistory.reduce((sum, search) => sum + search.totalTime, 0) / totalSearches;
                document.getElementById('avg-response-time').textContent = `${avgTotal.toFixed(0)}ms`;

                // Find fastest search
                const fastest = Math.min(...this.searchHistory.map(s => s.totalTime));
                document.getElementById('fastest-search').textContent = `${fastest.toFixed(0)}ms`;

                // Most searched type
                const typeCounts = {};
                this.searchHistory.forEach(search => {
                    typeCounts[search.searchType] = (typeCounts[search.searchType] || 0) + 1;
                });
                const mostSearchedType = Object.entries(typeCounts)
                    .sort(([,a], [,b]) => b - a)[0]?.[0] || '--';
                document.getElementById('most-searched-type').textContent = mostSearchedType.toUpperCase();
            }

            renderHistory() {
                const container = document.getElementById('search-history-list');
                const historyCount = document.getElementById('history-count');

                if (this.filteredHistory.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-state-icon">🔍</div>
                            <div class="empty-state-title">No search history</div>
                            <div class="empty-state-description">Your search history will appear here as you perform searches</div>
                            <a href="results.html" class="btn btn-primary">Start Searching</a>
                        </div>
                    `;
                    historyCount.textContent = '0 searches';
                    return;
                }

                historyCount.textContent = `${this.filteredHistory.length} searches`;

                const historyHtml = this.filteredHistory.map(search => {
                    const date = new Date(search.timestamp);
                    const timeAgo = this.getTimeAgo(date);
                    const performanceClass = this.getPerformanceClass(search.totalTime);

                    return `
                        <div class="search-item">
                            <a href="results.html?q=${encodeURIComponent(search.query)}&type=${search.searchType}" class="search-query">
                                ${this.escapeHtml(search.query)}
                            </a>
                            <div class="search-meta">
                                <span>🕐 ${timeAgo}</span>
                                <span>📊 ${search.resultCount} results</span>
                                <span>🔍 ${search.searchType.toUpperCase()}</span>
                                <span>${search.isRealAPI ? '🌐 Google API' : '🧪 Demo Data'}</span>
                            </div>
                            <div class="search-performance">
                                <div class="perf-metric ${this.getPerformanceClass(search.renderTime, 'render')}">
                                    <span class="perf-metric-icon">🎨</span>
                                    <span>Render: <span class="perf-metric-value">${search.renderTime.toFixed(0)}ms</span></span>
                                </div>
                                <div class="perf-metric ${this.getPerformanceClass(search.serverTime, 'server')}">
                                    <span class="perf-metric-icon">🌐</span>
                                    <span>Server: <span class="perf-metric-value">${search.serverTime.toFixed(0)}ms</span></span>
                                </div>
                                <div class="perf-metric ${this.getPerformanceClass(search.apiTime, 'api')}">
                                    <span class="perf-metric-icon">⚡</span>
                                    <span>API: <span class="perf-metric-value">${search.apiTime.toFixed(0)}ms</span></span>
                                </div>
                                <div class="perf-metric ${this.getPerformanceClass(search.totalTime, 'total')}">
                                    <span class="perf-metric-icon">🕐</span>
                                    <span>Total: <span class="perf-metric-value">${search.totalTime.toFixed(0)}ms</span></span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = historyHtml;
            }

            getPerformanceClass(timeMs, type = 'total') {
                const thresholds = {
                    render: { excellent: 50, good: 100, average: 200 },
                    server: { excellent: 150, good: 400, average: 800 },
                    api: { excellent: 200, good: 500, average: 1000 },
                    total: { excellent: 300, good: 700, average: 1500 }
                };

                const threshold = thresholds[type] || thresholds.total;

                if (timeMs <= threshold.excellent) return 'excellent';
                if (timeMs <= threshold.good) return 'good';
                if (timeMs <= threshold.average) return 'average';
                return 'slow';
            }

            getTimeAgo(date) {
                const now = new Date();
                const diffMs = now - date;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                const diffDays = Math.floor(diffMs / 86400000);

                if (diffMins < 1) return 'Just now';
                if (diffMins < 60) return `${diffMins}m ago`;
                if (diffHours < 24) return `${diffHours}h ago`;
                if (diffDays < 7) return `${diffDays}d ago`;
                return date.toLocaleDateString();
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            filterHistory() {
                const typeFilter = document.getElementById('type-filter').value;
                const performanceFilter = document.getElementById('performance-filter').value;
                const sortFilter = document.getElementById('sort-filter').value;

                let filtered = [...this.searchHistory];

                // Filter by type
                if (typeFilter) {
                    filtered = filtered.filter(search => search.searchType === typeFilter);
                }

                // Filter by performance
                if (performanceFilter) {
                    filtered = filtered.filter(search => 
                        this.getPerformanceClass(search.totalTime) === performanceFilter
                    );
                }

                // Sort
                switch (sortFilter) {
                    case 'oldest':
                        filtered.sort((a, b) => a.timestamp - b.timestamp);
                        break;
                    case 'fastest':
                        filtered.sort((a, b) => a.totalTime - b.totalTime);
                        break;
                    case 'slowest':
                        filtered.sort((a, b) => b.totalTime - a.totalTime);
                        break;
                    default: // newest
                        filtered.sort((a, b) => b.timestamp - a.timestamp);
                }

                this.filteredHistory = filtered;
                this.renderHistory();
            }

            clearHistory() {
                if (confirm('Are you sure you want to clear all search history? This action cannot be undone.')) {
                    try {
                        localStorage.removeItem('searchPerformanceHistory');
                        this.searchHistory = [];
                        this.filteredHistory = [];
                        this.updateStats();
                        this.renderHistory();
                        console.log('🗑️ Search history cleared');
                    } catch (e) {
                        console.error('Failed to clear search history:', e);
                        alert('Failed to clear search history. Please try again.');
                    }
                }
            }
        }

        // Global functions
        function filterHistory() {
            if (window.historyManager) {
                window.historyManager.filterHistory();
            }
        }

        function clearAllHistory() {
            if (window.historyManager) {
                window.historyManager.clearHistory();
            }
        }

        // Initialize the search history manager
        window.addEventListener('load', () => {
            window.historyManager = new SearchHistoryManager();
        });
    </script>
</body>
</html>
