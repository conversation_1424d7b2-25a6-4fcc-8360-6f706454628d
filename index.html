<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search</title>
    <meta name="description" content="Fast and simple search engine">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/search.js" as="script">
    
    <!-- Critical CSS inlined for performance - Google-like styling -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: arial, sans-serif;
            background: #fff;
            color: #202124;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        .header {
            padding: 6px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 60px;
        }

        .header-links {
            display: flex;
            gap: 15px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-link {
            color: rgba(0,0,0,.87);
            text-decoration: none;
            font-size: 13px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.1s ease;
        }

        .header-link:hover {
            background-color: rgba(60,64,67,.08);
        }

        .sign-in-btn {
            background: #1a73e8;
            color: white;
            border: 1px solid #1a73e8;
            border-radius: 4px;
            padding: 9px 23px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.1s ease;
            font-family: arial, sans-serif;
        }

        .sign-in-btn:hover {
            background: #1557b0;
            border-color: #1557b0;
            box-shadow: 0 1px 2px rgba(0,0,0,.1);
        }

        .sign-in-btn:active {
            background: #1557b0;
            box-shadow: 0 2px 4px rgba(0,0,0,.2);
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.1s ease;
        }

        .apps-menu:hover {
            background-color: rgba(60,64,67,.08);
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: box-shadow 0.1s ease;
        }

        .profile-pic:hover {
            box-shadow: 0 1px 3px rgba(60,64,67,.3);
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0 20px;
            margin-top: -150px;
        }

        .google-logo {
            margin-bottom: 25px;
            user-select: none;
        }

        .google-logo svg {
            width: 272px;
            height: 92px;
        }

        .search-container {
            width: 100%;
            max-width: 584px;
            position: relative;
            margin-bottom: 30px;
        }

        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 45px 0 16px;
            font-size: 16px;
            outline: none;
            transition: box-shadow 0.2s ease, border-color 0.2s ease;
            background: #fff;
            color: #202124;
            font-family: arial, sans-serif;
        }

        .search-box:hover {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }

        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }

        .search-icons {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-icon, .voice-icon, .camera-icon {
            width: 24px;
            height: 24px;
            padding: 8px;
            cursor: pointer;
            border-radius: 50%;
            transition: background-color 0.1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-icon:hover, .voice-icon:hover, .camera-icon:hover {
            background-color: rgba(60,64,67,.08);
        }

        .search-icon svg, .voice-icon svg, .camera-icon svg {
            width: 16px;
            height: 16px;
            fill: #9aa0a6;
        }

        .voice-icon svg {
            fill: #4285f4;
        }

        .buttons {
            display: flex;
            gap: 11px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .btn {
            background: #f8f9fa;
            border: 1px solid #f8f9fa;
            border-radius: 4px;
            color: #3c4043;
            font-size: 14px;
            font-family: arial, sans-serif;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.1s ease;
            min-width: 54px;
            text-align: center;
            user-select: none;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-weight: 400;
            letter-spacing: 0.25px;
        }

        .btn:hover {
            box-shadow: 0 1px 1px rgba(0,0,0,.1);
            background: #f1f3f4;
            border: 1px solid #dadce0;
            color: #202124;
        }

        .btn:focus {
            border: 1px solid #4285f4;
            outline: none;
        }

        .btn:active {
            background: #e8eaed;
            box-shadow: 0 1px 2px rgba(0,0,0,.1);
        }

        .btn-primary {
            background: #1a73e8;
            border: 1px solid #1a73e8;
            color: white;
        }

        .btn-primary:hover {
            background: #1557b0;
            border-color: #1557b0;
            box-shadow: 0 1px 2px rgba(0,0,0,.1);
        }

        .btn-primary:active {
            background: #1557b0;
            box-shadow: 0 2px 4px rgba(0,0,0,.2);
        }

        .suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dfe1e5;
            border-top: none;
            border-radius: 0 0 24px 24px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            animation: slideDown 0.2s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .suggestion-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #202124;
            font-family: arial, sans-serif;
            transition: background-color 0.1s ease;
            min-height: 44px;
        }

        .suggestion-item:hover,
        .suggestion-item.selected {
            background: #f1f3f4;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-icon {
            width: 16px;
            height: 16px;
            fill: #9aa0a6;
            flex-shrink: 0;
        }

        .suggestion-text {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .suggestion-remove {
            width: 16px;
            height: 16px;
            fill: #9aa0a6;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.1s ease;
        }

        .suggestion-item:hover .suggestion-remove {
            opacity: 1;
        }

        .footer {
            background: #f2f2f2;
            border-top: 1px solid #dadce0;
        }

        .footer-top {
            padding: 15px 30px;
            border-bottom: 1px solid #dadce0;
            color: rgba(0,0,0,.54);
            font-size: 15px;
        }

        .footer-bottom {
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }

        .footer-links {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: rgba(0,0,0,.54);
            text-decoration: none;
            font-size: 14px;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .google-logo svg {
                width: 200px;
                height: 68px;
            }

            .main-container {
                margin-top: -100px;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .btn {
                width: 180px;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 15px;
            }

            .footer-links {
                justify-content: center;
                gap: 20px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 6px 15px;
                gap: 10px;
            }

            .search-container {
                max-width: 100%;
            }

            .footer-links {
                gap: 15px;
                font-size: 13px;
            }
        }
    </style>
    
    <link rel="stylesheet" href="styles/main.css">
    <link rel="manifest" href="manifest.json">
</head>
<body>
    <header class="header">
        <div class="header-links">
            <a href="#" class="header-link">About</a>
            <a href="#" class="header-link">Store</a>
        </div>
        <div class="header-right">
            <a href="#" class="header-link">Gmail</a>
            <a href="results.html?q=images&type=images" class="header-link">Images</a>
            <div class="apps-menu" title="Google apps" id="apps-menu">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
                </svg>
            </div>
            <button class="sign-in-btn" id="sign-in-btn">Sign in</button>
        </div>
    </header>

    <main class="main-container">
        <div class="google-logo">
            <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0)">
                    <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                    <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                    <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                    <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                    <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                    <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                </g>
                <defs>
                    <clipPath id="clip0">
                        <rect width="272" height="92" fill="white"/>
                    </clipPath>
                </defs>
            </svg>
        </div>

        <div class="search-container">
            <input
                type="text"
                class="search-box"
                id="search-input"
                placeholder=""
                autocomplete="off"
                spellcheck="false"
                role="combobox"
                aria-expanded="false"
                aria-autocomplete="list"
                maxlength="2048"
                title="Search"
            >
            <div class="search-icons">
                <div class="voice-icon" id="voice-search-btn" title="Search by voice">
                    <svg viewBox="0 0 24 24">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    </svg>
                </div>
                <div class="camera-icon" title="Search by image">
                    <svg viewBox="0 0 24 24">
                        <path d="M14,6H10L8,4H6A2,2 0 0,0 4,6V18A2,2 0 0,0 6,20H18A2,2 0 0,0 20,18V8A2,2 0 0,0 18,6H16L14,6M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
                    </svg>
                </div>
                <div class="search-icon" id="search-btn" title="Search">
                    <svg viewBox="0 0 24 24">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </div>
            </div>

            <div class="suggestions" id="suggestions" role="listbox"></div>
        </div>

        <div class="buttons">
            <button class="btn" id="search-button">Google Search</button>
            <button class="btn" id="lucky-button">I'm Feeling Lucky</button>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-top">
            United States
        </div>
        <div class="footer-bottom">
            <div class="footer-links">
                <a href="#">About</a>
                <a href="#">Advertising</a>
                <a href="#">Business</a>
                <a href="#">How Search works</a>
            </div>
            <div class="footer-links">
                <a href="#">Privacy</a>
                <a href="#">Terms</a>
                <a href="#">Settings</a>
            </div>
        </div>
    </footer>
    
    <!-- Additional CSS for Google enhancements -->
    <link rel="stylesheet" href="styles/google-enhancements.css">

    <!-- Scripts loaded at the end for performance -->
    <script src="js/utils.js"></script>
    <script src="js/autocomplete.js"></script>
    <script src="js/search.js"></script>
    <script src="js/voice-search.js"></script>
    <script src="js/advanced-search.js"></script>
    <!-- <script src="js/instant-answers.js"></script> -->
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>
