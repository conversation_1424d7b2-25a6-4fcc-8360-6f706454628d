// Advanced Search functionality with filters and operators

class AdvancedSearch {
    constructor() {
        this.filters = {
            dateRange: 'any',
            language: 'any',
            region: 'any',
            fileType: 'any',
            safeSearch: 'moderate',
            exactPhrase: '',
            excludeWords: '',
            site: '',
            linkTo: ''
        };
        this.init();
    }
    
    init() {
        this.addAdvancedSearchButton();
        this.createAdvancedSearchModal();
        this.bindEvents();
    }
    
    addAdvancedSearchButton() {
        // Add to header on results page
        const header = document.querySelector('.header-content');
        if (header) {
            const advancedBtn = document.createElement('button');
            advancedBtn.id = 'advanced-search-btn';
            advancedBtn.className = 'btn';
            advancedBtn.textContent = 'Advanced';
            advancedBtn.style.marginLeft = 'auto';
            header.appendChild(advancedBtn);
        }
        
        // Add to main page
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
            const advancedLink = document.createElement('a');
            advancedLink.href = '#';
            advancedLink.id = 'advanced-search-link';
            advancedLink.textContent = 'Advanced Search';
            advancedLink.style.cssText = `
                position: absolute;
                bottom: 20px;
                right: 20px;
                color: #5f6368;
                text-decoration: none;
                font-size: 14px;
            `;
            advancedLink.addEventListener('mouseover', () => {
                advancedLink.style.textDecoration = 'underline';
            });
            advancedLink.addEventListener('mouseout', () => {
                advancedLink.style.textDecoration = 'none';
            });
            mainContainer.appendChild(advancedLink);
        }
    }
    
    createAdvancedSearchModal() {
        const modal = document.createElement('div');
        modal.id = 'advanced-search-modal';
        modal.className = 'advanced-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Advanced Search</h2>
                    <button class="modal-close" aria-label="Close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-group">
                            <label for="exact-phrase">Exact phrase:</label>
                            <input type="text" id="exact-phrase" placeholder="Enter exact phrase">
                            <small>Find pages with this exact phrase</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="exclude-words">Exclude words:</label>
                            <input type="text" id="exclude-words" placeholder="Words to exclude">
                            <small>Exclude pages with these words</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="site-search">Site or domain:</label>
                            <input type="text" id="site-search" placeholder="example.com">
                            <small>Search within a specific site</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-type">File type:</label>
                            <select id="file-type">
                                <option value="any">Any format</option>
                                <option value="pdf">PDF</option>
                                <option value="doc">Microsoft Word</option>
                                <option value="xls">Microsoft Excel</option>
                                <option value="ppt">Microsoft PowerPoint</option>
                                <option value="txt">Text files</option>
                                <option value="rtf">Rich Text Format</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="date-range">Date:</label>
                            <select id="date-range">
                                <option value="any">Any time</option>
                                <option value="day">Past 24 hours</option>
                                <option value="week">Past week</option>
                                <option value="month">Past month</option>
                                <option value="year">Past year</option>
                                <option value="custom">Custom range...</option>
                            </select>
                        </div>
                        
                        <div class="form-group" id="custom-date-range" style="display: none;">
                            <label>Custom date range:</label>
                            <div class="date-inputs">
                                <input type="date" id="date-from" placeholder="From">
                                <input type="date" id="date-to" placeholder="To">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="language">Language:</label>
                            <select id="language">
                                <option value="any">Any language</option>
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="it">Italian</option>
                                <option value="pt">Portuguese</option>
                                <option value="ru">Russian</option>
                                <option value="ja">Japanese</option>
                                <option value="ko">Korean</option>
                                <option value="zh">Chinese</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="region">Region:</label>
                            <select id="region">
                                <option value="any">Any region</option>
                                <option value="us">United States</option>
                                <option value="uk">United Kingdom</option>
                                <option value="ca">Canada</option>
                                <option value="au">Australia</option>
                                <option value="de">Germany</option>
                                <option value="fr">France</option>
                                <option value="es">Spain</option>
                                <option value="it">Italy</option>
                                <option value="jp">Japan</option>
                                <option value="kr">South Korea</option>
                                <option value="cn">China</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="safe-search">SafeSearch:</label>
                            <select id="safe-search">
                                <option value="off">Off</option>
                                <option value="moderate" selected>Moderate</option>
                                <option value="strict">Strict</option>
                            </select>
                            <small>Filter explicit content</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="reset-filters">Reset</button>
                    <button class="btn btn-primary" id="apply-filters">Search</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.addModalStyles();
    }
    
    addModalStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .advanced-modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1000;
            }
            
            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
            }
            
            .modal-content {
                position: relative;
                background: white;
                margin: 50px auto;
                max-width: 600px;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #dadce0;
            }
            
            .modal-header h2 {
                margin: 0;
                color: #202124;
                font-size: 20px;
                font-weight: 400;
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #5f6368;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            }
            
            .modal-close:hover {
                background: #f1f3f4;
            }
            
            .modal-body {
                padding: 20px;
            }
            
            .form-group {
                margin-bottom: 20px;
            }
            
            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: 500;
                color: #202124;
            }
            
            .form-group input,
            .form-group select {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                font-size: 14px;
                background: white;
            }
            
            .form-group input:focus,
            .form-group select:focus {
                outline: none;
                border-color: #4285f4;
                box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
            }
            
            .form-group small {
                display: block;
                margin-top: 5px;
                color: #5f6368;
                font-size: 12px;
            }
            
            .date-inputs {
                display: flex;
                gap: 10px;
            }
            
            .date-inputs input {
                flex: 1;
            }
            
            .modal-footer {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                padding: 20px;
                border-top: 1px solid #dadce0;
            }
            
            .btn-secondary {
                background: #f8f9fa;
                color: #3c4043;
                border: 1px solid #dadce0;
            }
            
            .btn-secondary:hover {
                background: #f1f3f4;
            }
            
            .btn-primary {
                background: #4285f4;
                color: white;
                border: 1px solid #4285f4;
            }
            
            .btn-primary:hover {
                background: #3367d6;
            }
            
            @media (max-width: 768px) {
                .modal-content {
                    margin: 20px;
                    max-width: none;
                }
                
                .date-inputs {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    bindEvents() {
        // Open modal
        document.addEventListener('click', (e) => {
            if (e.target.id === 'advanced-search-btn' || e.target.id === 'advanced-search-link') {
                e.preventDefault();
                this.openModal();
            }
        });
        
        // Close modal
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || 
                e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
        
        // Date range change
        document.addEventListener('change', (e) => {
            if (e.target.id === 'date-range') {
                const customRange = document.getElementById('custom-date-range');
                if (e.target.value === 'custom') {
                    customRange.style.display = 'block';
                } else {
                    customRange.style.display = 'none';
                }
            }
        });
        
        // Apply filters
        document.addEventListener('click', (e) => {
            if (e.target.id === 'apply-filters') {
                this.applyFilters();
            } else if (e.target.id === 'reset-filters') {
                this.resetFilters();
            }
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }
    
    openModal() {
        const modal = document.getElementById('advanced-search-modal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Focus first input
            const firstInput = modal.querySelector('input');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    }
    
    closeModal() {
        const modal = document.getElementById('advanced-search-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }
    
    applyFilters() {
        // Collect filter values
        this.filters.exactPhrase = document.getElementById('exact-phrase')?.value || '';
        this.filters.excludeWords = document.getElementById('exclude-words')?.value || '';
        this.filters.site = document.getElementById('site-search')?.value || '';
        this.filters.fileType = document.getElementById('file-type')?.value || 'any';
        this.filters.dateRange = document.getElementById('date-range')?.value || 'any';
        this.filters.language = document.getElementById('language')?.value || 'any';
        this.filters.region = document.getElementById('region')?.value || 'any';
        this.filters.safeSearch = document.getElementById('safe-search')?.value || 'moderate';
        
        // Build search query
        const query = this.buildAdvancedQuery();
        
        if (query.trim()) {
            // Perform search with advanced query
            this.performAdvancedSearch(query);
            this.closeModal();
        } else {
            alert('Please enter at least one search criteria.');
        }
    }
    
    buildAdvancedQuery() {
        let query = '';
        const currentQuery = document.getElementById('search-input')?.value || '';
        
        // Start with current query
        if (currentQuery) {
            query = currentQuery;
        }
        
        // Add exact phrase
        if (this.filters.exactPhrase) {
            query += ` "${this.filters.exactPhrase}"`;
        }
        
        // Exclude words
        if (this.filters.excludeWords) {
            const excludeWords = this.filters.excludeWords.split(' ');
            excludeWords.forEach(word => {
                if (word.trim()) {
                    query += ` -${word.trim()}`;
                }
            });
        }
        
        // Site search
        if (this.filters.site) {
            query += ` site:${this.filters.site}`;
        }
        
        // File type
        if (this.filters.fileType !== 'any') {
            query += ` filetype:${this.filters.fileType}`;
        }
        
        return query.trim();
    }
    
    performAdvancedSearch(query) {
        // Update search input
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = query;
        }
        
        // Perform search
        if (window.searchEngine) {
            window.searchEngine.performSearch(query);
        } else if (window.resultsPage) {
            window.resultsPage.updateSearch(query, 1, window.resultsPage.currentType);
        } else {
            // Navigate to results page
            const params = new URLSearchParams({
                q: query,
                start: 1,
                type: 'web'
            });
            window.location.href = `results.html?${params.toString()}`;
        }
    }
    
    resetFilters() {
        // Reset all form fields
        document.getElementById('exact-phrase').value = '';
        document.getElementById('exclude-words').value = '';
        document.getElementById('site-search').value = '';
        document.getElementById('file-type').value = 'any';
        document.getElementById('date-range').value = 'any';
        document.getElementById('language').value = 'any';
        document.getElementById('region').value = 'any';
        document.getElementById('safe-search').value = 'moderate';
        
        // Hide custom date range
        document.getElementById('custom-date-range').style.display = 'none';
        
        // Reset filters object
        this.filters = {
            dateRange: 'any',
            language: 'any',
            region: 'any',
            fileType: 'any',
            safeSearch: 'moderate',
            exactPhrase: '',
            excludeWords: '',
            site: '',
            linkTo: ''
        };
    }
    
    // Public methods
    getFilters() {
        return { ...this.filters };
    }
    
    setFilters(filters) {
        this.filters = { ...this.filters, ...filters };
    }
}

// Initialize advanced search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.advancedSearch = new AdvancedSearch();
});

// Export for use in other modules
window.AdvancedSearch = AdvancedSearch;
